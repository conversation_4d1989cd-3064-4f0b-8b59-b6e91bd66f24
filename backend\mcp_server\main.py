"""
MCP Server - Main Application

This module implements a private Model Context Protocol (MCP) server using FastAPI.
It provides endpoints for storing, retrieving, and updating context information
for the trading signal platform.
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="MCP Trading Server",
    description="A private Model Context Protocol server for trading signals",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "development_secret_key")  # Change in production
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Data models
class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None

class UserInDB(User):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class ContextItem(BaseModel):
    id: str
    type: str
    content: Dict[str, Any]
    timestamp: datetime
    source: str
    confidence: float = 1.0

class ContextQuery(BaseModel):
    types: Optional[List[str]] = None
    sources: Optional[List[str]] = None
    time_range: Optional[Dict[str, datetime]] = None
    limit: int = 100

# In-memory storage (replace with database in production)
fake_users_db = {
    "admin": {
        "username": "admin",
        "full_name": "Admin User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("admin"),
        "disabled": False,
    }
}

# In-memory context storage (replace with database in production)
context_storage: List[ContextItem] = []

# Security functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def get_user(db, username: str):
    if username in db:
        user_dict = db[username]
        return UserInDB(**user_dict)
    return None

def authenticate_user(fake_db, username: str, password: str):
    user = get_user(fake_db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(fake_users_db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# API endpoints
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(fake_users_db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return current_user

@app.post("/context", status_code=status.HTTP_201_CREATED)
async def add_context(
    context_item: ContextItem, current_user: User = Depends(get_current_active_user)
):
    """Add a new context item to the MCP server."""
    logger.info(f"Adding context item: {context_item.id} from {context_item.source}")
    context_storage.append(context_item)
    return {"status": "success", "id": context_item.id}

@app.post("/context/batch", status_code=status.HTTP_201_CREATED)
async def add_context_batch(
    context_items: List[ContextItem], current_user: User = Depends(get_current_active_user)
):
    """Add multiple context items in a single request."""
    for item in context_items:
        context_storage.append(item)
    return {"status": "success", "count": len(context_items)}

@app.post("/context/batch/noauth", status_code=status.HTTP_201_CREATED)
async def add_context_batch_noauth(
    context_items: List[ContextItem]
):
    """Add multiple context items in a single request (no authentication required).
    WARNING: This endpoint is for development/testing only and should be removed in production.
    """
    logger.warning("Using unauthenticated context batch endpoint - for development only!")
    for item in context_items:
        context_storage.append(item)
    return {"status": "success", "count": len(context_items)}

@app.post("/context/query/noauth")
async def query_context_noauth(query: ContextQuery):
    """Query context items based on specified criteria (no authentication required).
    WARNING: This endpoint is for development/testing only and should be removed in production.
    """
    logger.warning("Using unauthenticated context query endpoint - for development only!")

    results = context_storage.copy()

    # Filter by types
    if query.types:
        results = [item for item in results if item.type in query.types]

    # Filter by sources
    if query.sources:
        results = [item for item in results if item.source in query.sources]

    # Filter by time range
    if query.time_range:
        start = query.time_range.get("start")
        end = query.time_range.get("end", datetime.utcnow())
        if start:
            results = [item for item in results if start <= item.timestamp <= end]

    # Sort by timestamp (newest first)
    results.sort(key=lambda x: x.timestamp, reverse=True)

    # Apply limit
    results = results[:query.limit]

    return results

@app.post("/context/query", response_model=List[ContextItem])
async def query_context(
    query: ContextQuery, current_user: User = Depends(get_current_active_user)
):
    """Query context items based on specified criteria."""
    results = context_storage.copy()

    # Filter by types
    if query.types:
        results = [item for item in results if item.type in query.types]

    # Filter by sources
    if query.sources:
        results = [item for item in results if item.source in query.sources]

    # Filter by time range
    if query.time_range:
        start = query.time_range.get("start")
        end = query.time_range.get("end", datetime.utcnow())
        if start:
            results = [item for item in results if start <= item.timestamp <= end]

    # Sort by timestamp (newest first)
    results.sort(key=lambda x: x.timestamp, reverse=True)

    # Apply limit
    results = results[:query.limit]

    return results

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
