{"name": "@chakra-ui/react", "version": "2.10.7", "description": "Responsive and accessible React UI components built with React and Emotion", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "files": ["dist"], "sideEffects": false, "publishConfig": {"access": "public"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://chakra-ui.com/", "repository": {"type": "git", "url": "https://github.com/chakra-ui/chakra-ui", "directory": "packages/components/react"}, "keywords": ["react", "ui", "design-system", "react-components", "uikit", "accessible", "components", "emotion", "library", "design-system"], "storybook": {"title": "Chakra UI", "url": "https://storybook.chakra-ui.com"}, "dependencies": {"@popperjs/core": "^2.11.8", "@zag-js/focus-visible": "^0.31.1", "aria-hidden": "^1.2.3", "react-fast-compare": "3.2.2", "react-focus-lock": "^2.9.6", "react-remove-scroll": "^2.5.7", "@chakra-ui/styled-system": "2.12.2", "@chakra-ui/theme": "3.4.8", "@chakra-ui/utils": "2.2.4", "@chakra-ui/hooks": "2.4.4"}, "peerDependencies": {"@emotion/react": ">=11", "@emotion/styled": ">=11", "framer-motion": ">=4.0.0", "react": ">=18", "react-dom": ">=18"}, "devDependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@tanstack/react-table": "^8.10.7", "@testing-library/react": "^14.0.0", "framer-motion": "10.16.15", "react": "^18.2.0", "react-dom": "^18.2.0", "react-frame-component": "^5.2.6", "react-lorem-component": "^0.13.0", "react-hook-form": "^7.48.2", "react-icons": "^4.12.0", "react-router-dom": "^6.20.1", "react-spinners": "^0.13.8"}, "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json", "./*": {"import": {"types": "./dist/types/*/index.d.ts", "default": "./dist/esm/*/index.mjs"}, "require": {"types": "./dist/types/*/index.d.ts", "default": "./dist/cjs/*/index.cjs"}}}, "scripts": {"build": "tsup --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup"}}