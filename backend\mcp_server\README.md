# Model Context Protocol (MCP) Server

This is a private implementation of a Model Context Protocol server for the trading signal platform. The MCP server is responsible for storing, retrieving, and managing context information that is used to generate trading signals.

## What is Model Context Protocol?

Model Context Protocol (MCP) is a protocol for managing context information that can be used by AI models and other systems. In our trading platform, the MCP server acts as a central repository for all market data, news, sentiment, and other information that is relevant for generating trading signals.

## Security Features

The MCP server is designed with security as a core principle:

1. **Authentication and Authorization**
   - JWT-based authentication for all API endpoints
   - Role-based access control
   - API key authentication for data aggregators

2. **Data Security**
   - All communications secured with TLS/SSL
   - Input validation and sanitization
   - Protection against common attacks

3. **Infrastructure Security**
   - Runs in an isolated environment
   - Minimal required dependencies
   - Regular security updates

For more details, see the [Security Guide](../../docs/security.md).

## API Endpoints

The MCP server exposes the following API endpoints:

- `/token` - Obtain an authentication token
- `/users/me` - Get information about the current user
- `/context` - Add a new context item
- `/context/batch` - Add multiple context items in a single request
- `/context/query` - Query context items based on specified criteria
- `/health` - Health check endpoint

## Context Items

Context items are the basic unit of information in the MCP server. Each context item has the following structure:

```json
{
  "id": "unique_identifier",
  "type": "context_type",
  "content": {
    "key1": "value1",
    "key2": "value2"
  },
  "timestamp": "2023-01-01T00:00:00Z",
  "source": "data_source",
  "confidence": 0.9
}
```

## Running the MCP Server

### Local Development

```bash
cd backend/mcp_server
uvicorn main:app --reload
```

### Production Deployment

For production deployment, it is recommended to use the provided deployment script:

```bash
cd infrastructure
./deploy.sh
```

This will set up the MCP server as a systemd service with proper security configurations.

## Configuration

The MCP server can be configured using environment variables:

- `SECRET_KEY` - Secret key for JWT token generation
- `ACCESS_TOKEN_EXPIRE_MINUTES` - Token expiration time in minutes

## Why Self-Host?

By self-hosting the MCP server, you maintain complete control over your data and infrastructure. This provides several advantages:

1. **Data Privacy** - Your trading data stays on your infrastructure
2. **Customization** - You can customize the server to your specific needs
3. **Independence** - No reliance on third-party services
4. **Cost Control** - No subscription fees for third-party services

## Monitoring and Maintenance

The MCP server includes logging and monitoring features to help you keep track of its operation:

- Comprehensive logging of all operations
- Health check endpoint for monitoring
- Statistics about stored context items

Regular maintenance tasks include:

- Backing up context data
- Updating dependencies
- Reviewing security configurations
