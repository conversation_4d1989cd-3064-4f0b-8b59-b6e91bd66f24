"""
News API Client for Sentiment Analysis

This module fetches news articles from NewsAPI.org for sentiment analysis.
"""

import logging
import httpx
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
NEWSAPI_URL = "https://newsapi.org/v2"
DEFAULT_LANGUAGE = "en"
REQUEST_TIMEOUT = 30  # seconds

class NewsAPIClient:
    """Client for fetching news articles from NewsAPI.org."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the NewsAPI client.
        
        Args:
            api_key: NewsAPI.org API key
        """
        self.api_key = api_key
        self.headers = {"X-Api-Key": api_key} if api_key else {}
        logger.info("NewsAPI client initialized")
    
    async def fetch_crypto_news(
        self, 
        keywords: List[str], 
        days: int = 1,
        language: str = DEFAULT_LANGUAGE,
        page_size: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch news articles related to cryptocurrencies.
        
        Args:
            keywords: List of keywords to search for
            days: Number of days to look back
            language: Language of articles
            page_size: Number of articles to fetch
            
        Returns:
            List of news articles
        """
        if not self.api_key:
            logger.warning("No NewsAPI key provided, using sample data")
            return self._get_sample_news(keywords)
        
        try:
            # Calculate date range
            to_date = datetime.now()
            from_date = to_date - timedelta(days=days)
            
            # Format dates for API
            from_date_str = from_date.strftime("%Y-%m-%d")
            to_date_str = to_date.strftime("%Y-%m-%d")
            
            # Construct query
            query = " OR ".join([f'"{keyword}"' for keyword in keywords])
            
            # Make request to NewsAPI
            url = f"{NEWSAPI_URL}/everything"
            params = {
                "q": query,
                "language": language,
                "from": from_date_str,
                "to": to_date_str,
                "sortBy": "relevancy",
                "pageSize": page_size
            }
            
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                articles = data.get("articles", [])
                logger.info(f"Fetched {len(articles)} news articles for keywords: {keywords}")
                
                return articles
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching news: {e}")
            if e.response.status_code == 429:
                logger.warning("Rate limit exceeded for NewsAPI")
            elif e.response.status_code == 401:
                logger.warning("Invalid API key for NewsAPI")
            return self._get_sample_news(keywords)
        except httpx.RequestError as e:
            logger.error(f"Request error fetching news: {e}")
            return self._get_sample_news(keywords)
        except Exception as e:
            logger.error(f"Unexpected error fetching news: {e}")
            return self._get_sample_news(keywords)
    
    def _get_sample_news(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """
        Get sample news data for testing.
        
        Args:
            keywords: List of keywords to filter sample data
            
        Returns:
            List of sample news articles
        """
        logger.info("Using sample news data")
        
        # Sample news articles
        sample_articles = [
            {
                "source": {"id": "coindesk", "name": "CoinDesk"},
                "author": "Sample Author",
                "title": "Bitcoin Surges Past $60,000 as Institutional Interest Grows",
                "description": "Bitcoin has surged past $60,000 as institutional investors continue to show interest in the cryptocurrency.",
                "url": "https://www.coindesk.com/sample-article-1",
                "urlToImage": "https://www.coindesk.com/sample-image-1.jpg",
                "publishedAt": datetime.now().isoformat(),
                "content": "Bitcoin has surged past $60,000 as institutional investors continue to show interest in the cryptocurrency. Analysts attribute the rise to growing adoption among major financial institutions and corporations."
            },
            {
                "source": {"id": "cointelegraph", "name": "Cointelegraph"},
                "author": "Sample Author",
                "title": "Ethereum 2.0 Upgrade on Track for Q3 Completion",
                "description": "The Ethereum 2.0 upgrade is on track for completion in Q3, according to developers.",
                "url": "https://www.cointelegraph.com/sample-article-2",
                "urlToImage": "https://www.cointelegraph.com/sample-image-2.jpg",
                "publishedAt": (datetime.now() - timedelta(hours=12)).isoformat(),
                "content": "The Ethereum 2.0 upgrade is on track for completion in Q3, according to developers. The upgrade is expected to significantly improve scalability and reduce energy consumption."
            },
            {
                "source": {"id": "theblock", "name": "The Block"},
                "author": "Sample Author",
                "title": "Uniswap Governance Proposal Passes with 99% Support",
                "description": "A Uniswap governance proposal to allocate treasury funds to development grants has passed with 99% support.",
                "url": "https://www.theblock.com/sample-article-3",
                "urlToImage": "https://www.theblock.com/sample-image-3.jpg",
                "publishedAt": (datetime.now() - timedelta(days=1)).isoformat(),
                "content": "A Uniswap governance proposal to allocate treasury funds to development grants has passed with 99% support. The proposal will allocate $10 million to support development of the Uniswap ecosystem."
            },
            {
                "source": {"id": "decrypt", "name": "Decrypt"},
                "author": "Sample Author",
                "title": "Aave Launches New Lending Protocol on Layer 2",
                "description": "Aave has launched a new lending protocol on a Layer 2 solution to reduce gas fees.",
                "url": "https://www.decrypt.co/sample-article-4",
                "urlToImage": "https://www.decrypt.co/sample-image-4.jpg",
                "publishedAt": (datetime.now() - timedelta(days=2)).isoformat(),
                "content": "Aave has launched a new lending protocol on a Layer 2 solution to reduce gas fees. The move is expected to make the protocol more accessible to users with smaller amounts of capital."
            }
        ]
        
        # Filter articles based on keywords
        filtered_articles = []
        for article in sample_articles:
            for keyword in keywords:
                if (
                    keyword.lower() in article["title"].lower() or
                    keyword.lower() in article["description"].lower() or
                    keyword.lower() in article["content"].lower()
                ):
                    filtered_articles.append(article)
                    break
        
        return filtered_articles
