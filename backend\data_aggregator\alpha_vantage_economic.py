"""
Alpha Vantage Economic Indicators Fetcher

This module fetches economic indicators data from the Alpha Vantage API.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Alpha Vantage API credentials
ALPHA_VANTAGE_API_KEY = "YOUR_ALPHA_VANTAGE_API_KEY"  # Replace with your actual API key

class AlphaVantageEconomicFetcher:
    """Fetches economic indicators data from the Alpha Vantage API."""

    def __init__(self, api_key: str = None):
        """
        Initialize the Alpha Vantage economic indicators fetcher.
        
        Args:
            api_key: Alpha Vantage API key (optional, uses default if not provided)
        """
        self.api_key = api_key or ALPHA_VANTAGE_API_KEY
        self.base_url = "https://www.alphavantage.co/query"
        logger.info("Alpha Vantage economic indicators fetcher initialized")

    async def _make_request(self, function: str, params: Dict = None) -> Dict:
        """
        Make a request to the Alpha Vantage API.
        
        Args:
            function: API function
            params: Additional request parameters
            
        Returns:
            API response
        """
        params = params or {}
        params["function"] = function
        params["apikey"] = self.api_key
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.base_url, params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error making request to {function}: {str(e)}")
            return {}

    async def fetch_real_gdp(self) -> Dict[str, Any]:
        """
        Fetch real GDP data.
        
        Returns:
            Dictionary with real GDP data
        """
        try:
            data = await self._make_request("REAL_GDP", {"interval": "quarterly"})
            
            if "data" not in data or not data["data"]:
                logger.warning("No real GDP data found")
                return {}
            
            # Get the latest and previous data points
            latest = data["data"][0]
            previous = data["data"][1] if len(data["data"]) > 1 else None
            
            # Calculate growth rate
            growth_rate = None
            if previous:
                growth_rate = (float(latest["value"]) - float(previous["value"])) / float(previous["value"]) * 100
            
            return {
                "indicator": "real_gdp",
                "value": float(latest["value"]),
                "date": latest["date"],
                "previous": float(previous["value"]) if previous else None,
                "growth_rate": growth_rate,
                "unit": "Billions of chained 2012 dollars",
                "source": "alpha_vantage"
            }
        
        except Exception as e:
            logger.error(f"Error fetching real GDP: {str(e)}")
            return {}

    async def fetch_inflation(self) -> Dict[str, Any]:
        """
        Fetch inflation (CPI) data.
        
        Returns:
            Dictionary with inflation data
        """
        try:
            data = await self._make_request("CPI", {"interval": "monthly"})
            
            if "data" not in data or not data["data"]:
                logger.warning("No inflation data found")
                return {}
            
            # Get the latest and previous data points
            latest = data["data"][0]
            previous = data["data"][1] if len(data["data"]) > 1 else None
            
            # Get data from one year ago for annual inflation rate
            year_ago_index = 12  # Approximately 12 months ago
            year_ago = data["data"][year_ago_index] if len(data["data"]) > year_ago_index else None
            
            # Calculate annual inflation rate
            annual_inflation_rate = None
            if year_ago:
                annual_inflation_rate = (float(latest["value"]) - float(year_ago["value"])) / float(year_ago["value"]) * 100
            
            return {
                "indicator": "inflation",
                "value": float(latest["value"]),
                "date": latest["date"],
                "previous": float(previous["value"]) if previous else None,
                "annual_rate": annual_inflation_rate,
                "unit": "Index 1982-1984=100",
                "source": "alpha_vantage"
            }
        
        except Exception as e:
            logger.error(f"Error fetching inflation: {str(e)}")
            return {}

    async def fetch_interest_rate(self) -> Dict[str, Any]:
        """
        Fetch federal funds interest rate data.
        
        Returns:
            Dictionary with interest rate data
        """
        try:
            data = await self._make_request("FEDERAL_FUNDS_RATE", {"interval": "monthly"})
            
            if "data" not in data or not data["data"]:
                logger.warning("No interest rate data found")
                return {}
            
            # Get the latest and previous data points
            latest = data["data"][0]
            previous = data["data"][1] if len(data["data"]) > 1 else None
            
            # Calculate rate change
            rate_change = None
            if previous:
                rate_change = float(latest["value"]) - float(previous["value"])
            
            return {
                "indicator": "interest_rate",
                "value": float(latest["value"]),
                "date": latest["date"],
                "previous": float(previous["value"]) if previous else None,
                "rate_change": rate_change,
                "unit": "Percent",
                "source": "alpha_vantage"
            }
        
        except Exception as e:
            logger.error(f"Error fetching interest rate: {str(e)}")
            return {}

    async def fetch_unemployment(self) -> Dict[str, Any]:
        """
        Fetch unemployment rate data.
        
        Returns:
            Dictionary with unemployment rate data
        """
        try:
            data = await self._make_request("UNEMPLOYMENT")
            
            if "data" not in data or not data["data"]:
                logger.warning("No unemployment data found")
                return {}
            
            # Get the latest and previous data points
            latest = data["data"][0]
            previous = data["data"][1] if len(data["data"]) > 1 else None
            
            # Calculate rate change
            rate_change = None
            if previous:
                rate_change = float(latest["value"]) - float(previous["value"])
            
            return {
                "indicator": "unemployment",
                "value": float(latest["value"]),
                "date": latest["date"],
                "previous": float(previous["value"]) if previous else None,
                "rate_change": rate_change,
                "unit": "Percent",
                "source": "alpha_vantage"
            }
        
        except Exception as e:
            logger.error(f"Error fetching unemployment: {str(e)}")
            return {}

    async def fetch_retail_sales(self) -> Dict[str, Any]:
        """
        Fetch retail sales data.
        
        Returns:
            Dictionary with retail sales data
        """
        try:
            data = await self._make_request("RETAIL_SALES")
            
            if "data" not in data or not data["data"]:
                logger.warning("No retail sales data found")
                return {}
            
            # Get the latest and previous data points
            latest = data["data"][0]
            previous = data["data"][1] if len(data["data"]) > 1 else None
            
            # Calculate growth rate
            growth_rate = None
            if previous:
                growth_rate = (float(latest["value"]) - float(previous["value"])) / float(previous["value"]) * 100
            
            return {
                "indicator": "retail_sales",
                "value": float(latest["value"]),
                "date": latest["date"],
                "previous": float(previous["value"]) if previous else None,
                "growth_rate": growth_rate,
                "unit": "Millions of dollars",
                "source": "alpha_vantage"
            }
        
        except Exception as e:
            logger.error(f"Error fetching retail sales: {str(e)}")
            return {}

    async def fetch_consumer_sentiment(self) -> Dict[str, Any]:
        """
        Fetch consumer sentiment data.
        
        Returns:
            Dictionary with consumer sentiment data
        """
        try:
            data = await self._make_request("CONSUMER_SENTIMENT")
            
            if "data" not in data or not data["data"]:
                logger.warning("No consumer sentiment data found")
                return {}
            
            # Get the latest and previous data points
            latest = data["data"][0]
            previous = data["data"][1] if len(data["data"]) > 1 else None
            
            # Calculate change
            sentiment_change = None
            if previous:
                sentiment_change = float(latest["value"]) - float(previous["value"])
            
            return {
                "indicator": "consumer_sentiment",
                "value": float(latest["value"]),
                "date": latest["date"],
                "previous": float(previous["value"]) if previous else None,
                "sentiment_change": sentiment_change,
                "unit": "Index",
                "source": "alpha_vantage"
            }
        
        except Exception as e:
            logger.error(f"Error fetching consumer sentiment: {str(e)}")
            return {}

    async def fetch_all_indicators(self) -> List[Dict[str, Any]]:
        """
        Fetch all economic indicators.
        
        Returns:
            List of dictionaries with economic indicators data
        """
        tasks = [
            self.fetch_real_gdp(),
            self.fetch_inflation(),
            self.fetch_interest_rate(),
            self.fetch_unemployment(),
            self.fetch_retail_sales(),
            self.fetch_consumer_sentiment()
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Filter out empty results
        indicators = [result for result in results if result]
        
        logger.info(f"Fetched {len(indicators)} economic indicators")
        return indicators

    def analyze_economic_trends(self, indicators: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Analyze economic trends based on indicators.
        
        Args:
            indicators: List of economic indicators
            
        Returns:
            Dictionary with trend analysis
        """
        trends = {}
        
        for indicator in indicators:
            indicator_name = indicator["indicator"]
            
            if indicator_name == "inflation":
                if "annual_rate" in indicator and indicator["annual_rate"] is not None:
                    if indicator["annual_rate"] > 4.0:
                        trends["inflation_trend"] = "negative"  # High inflation is negative for markets
                    elif indicator["annual_rate"] < 2.0:
                        trends["inflation_trend"] = "positive"  # Low inflation is positive for markets
                    else:
                        trends["inflation_trend"] = "neutral"  # Moderate inflation is neutral
            
            elif indicator_name == "interest_rate":
                if "rate_change" in indicator and indicator["rate_change"] is not None:
                    if indicator["rate_change"] > 0:
                        trends["interest_rate_trend"] = "negative"  # Rising rates are negative for markets
                    elif indicator["rate_change"] < 0:
                        trends["interest_rate_trend"] = "positive"  # Falling rates are positive for markets
                    else:
                        trends["interest_rate_trend"] = "neutral"  # Stable rates are neutral
            
            elif indicator_name == "unemployment":
                if "rate_change" in indicator and indicator["rate_change"] is not None:
                    if indicator["rate_change"] > 0.2:
                        trends["unemployment_trend"] = "negative"  # Rising unemployment is negative
                    elif indicator["rate_change"] < -0.2:
                        trends["unemployment_trend"] = "positive"  # Falling unemployment is positive
                    else:
                        trends["unemployment_trend"] = "neutral"  # Stable unemployment is neutral
            
            elif indicator_name == "real_gdp":
                if "growth_rate" in indicator and indicator["growth_rate"] is not None:
                    if indicator["growth_rate"] > 2.5:
                        trends["gdp_trend"] = "positive"  # Strong growth is positive
                    elif indicator["growth_rate"] < 1.0:
                        trends["gdp_trend"] = "negative"  # Weak growth is negative
                    else:
                        trends["gdp_trend"] = "neutral"  # Moderate growth is neutral
            
            elif indicator_name == "consumer_sentiment":
                if "sentiment_change" in indicator and indicator["sentiment_change"] is not None:
                    if indicator["sentiment_change"] > 2.0:
                        trends["consumer_sentiment_trend"] = "positive"  # Improving sentiment is positive
                    elif indicator["sentiment_change"] < -2.0:
                        trends["consumer_sentiment_trend"] = "negative"  # Deteriorating sentiment is negative
                    else:
                        trends["consumer_sentiment_trend"] = "neutral"  # Stable sentiment is neutral
        
        # Determine overall economic trend
        positive_count = sum(1 for trend in trends.values() if trend == "positive")
        negative_count = sum(1 for trend in trends.values() if trend == "negative")
        
        if positive_count > negative_count:
            trends["overall_economic_trend"] = "positive"
        elif negative_count > positive_count:
            trends["overall_economic_trend"] = "negative"
        else:
            trends["overall_economic_trend"] = "neutral"
        
        return trends

    def to_context_items(self, indicators: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Convert economic indicators to context items for MCP.
        
        Args:
            indicators: List of dictionaries with economic indicators data
            
        Returns:
            List of context items
        """
        context_items = []
        
        for indicator in indicators:
            context_item = {
                "id": f"economic_{indicator['indicator']}_{uuid.uuid4()}",
                "type": "economic_indicator",
                "content": indicator,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": indicator.get("source", "alpha_vantage"),
                "confidence": 1.0
            }
            
            context_items.append(context_item)
        
        return context_items

async def main():
    """Main function for testing the Alpha Vantage economic indicators fetcher."""
    # Replace with your actual API key
    api_key = "YOUR_ALPHA_VANTAGE_API_KEY"
    
    fetcher = AlphaVantageEconomicFetcher(api_key)
    
    # Fetch all indicators
    indicators = await fetcher.fetch_all_indicators()
    
    # Analyze economic trends
    trends = fetcher.analyze_economic_trends(indicators)
    
    # Convert to context items
    context_items = fetcher.to_context_items(indicators)
    
    # Print results
    print(f"Fetched {len(indicators)} economic indicators")
    
    for indicator in indicators:
        print(f"\n{indicator['indicator'].upper()}:")
        for key, value in indicator.items():
            if key != "indicator":
                print(f"  {key}: {value}")
    
    print("\nEconomic Trends Analysis:")
    for trend, value in trends.items():
        print(f"  {trend}: {value}")
    
    print(f"\nGenerated {len(context_items)} context items")

if __name__ == "__main__":
    asyncio.run(main())
