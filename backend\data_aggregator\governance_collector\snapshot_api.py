"""
API client for Snapshot.org, a decentralized voting platform.

Snapshot is one of the most widely used governance platforms in the Web3 space,
used by thousands of DAOs for proposal creation and voting.
"""

import logging
import asyncio
import platform
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

import aiohttp
from .models import GovernanceProposal, VotingData

# Fix for Windows event loop policy
if platform.system() == 'Windows':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

logger = logging.getLogger(__name__)

class SnapshotAPI:
    """
    Client for the Snapshot.org GraphQL API.

    Snapshot.org is a decentralized voting system used by many DAOs for
    governance proposals and voting.
    """

    def __init__(self, api_url: str = "https://hub.snapshot.org/graphql"):
        """
        Initialize the Snapshot API client.

        Args:
            api_url: The GraphQL API endpoint URL
        """
        self.api_url = api_url
        logger.info(f"Initialized Snapshot API client with endpoint: {api_url}")

    async def _execute_query(self, query: str, variables: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Execute a GraphQL query against the Snapshot API.

        Args:
            query: The GraphQL query string
            variables: Variables for the GraphQL query

        Returns:
            The JSON response from the API
        """
        variables = variables or {}

        async with aiohttp.ClientSession() as session:
            async with session.post(
                self.api_url,
                json={"query": query, "variables": variables}
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Snapshot API error: {response.status} - {error_text}")
                    raise Exception(f"Snapshot API returned {response.status}: {error_text}")

                result = await response.json()
                if "errors" in result:
                    logger.error(f"GraphQL errors: {result['errors']}")
                    raise Exception(f"GraphQL errors: {result['errors']}")

                return result

    async def get_recent_proposals(self, hours_ago: int = 24) -> List[GovernanceProposal]:
        """
        Get recent proposals from Snapshot.

        Args:
            hours_ago: Look back this many hours for proposals

        Returns:
            List of GovernanceProposal objects
        """
        # Calculate timestamp for hours_ago
        timestamp = int((datetime.now() - timedelta(hours=hours_ago)).timestamp())

        # GraphQL query for proposals
        query = """
        query GetProposals($timestamp: Int!) {
          proposals(
            first: 100,
            skip: 0,
            where: {
              created_gte: $timestamp
            },
            orderBy: "created",
            orderDirection: desc
          ) {
            id
            title
            body
            choices
            start
            end
            snapshot
            state
            author
            space {
              id
              name
            }
            scores
            scores_total
            votes
          }
        }
        """

        variables = {"timestamp": timestamp}

        try:
            result = await self._execute_query(query, variables)
            proposals_data = result.get("data", {}).get("proposals", [])

            logger.info(f"Retrieved {len(proposals_data)} proposals from Snapshot")

            # Convert to GovernanceProposal objects
            proposals = []
            for p in proposals_data:
                try:
                    proposal = GovernanceProposal(
                        id=p["id"],
                        title=p["title"],
                        description=p["body"],
                        platform="snapshot",
                        space_id=p["space"]["id"],
                        space_name=p["space"]["name"],
                        state=p["state"],
                        created_at=datetime.fromtimestamp(p["snapshot"]),
                        start_date=datetime.fromtimestamp(p["start"]),
                        end_date=datetime.fromtimestamp(p["end"]),
                        author=p["author"],
                        choices=p["choices"],
                        scores=p["scores"] if p["scores"] else [],
                        scores_total=p["scores_total"] if p["scores_total"] else 0.0,
                        votes_count=p["votes"],
                        raw_data=p
                    )
                    proposals.append(proposal)
                except Exception as e:
                    logger.error(f"Error parsing proposal {p['id']}: {str(e)}")

            return proposals

        except Exception as e:
            logger.error(f"Error fetching proposals from Snapshot: {str(e)}")
            return []

    async def get_votes_for_proposal(self, proposal_id: str) -> List[VotingData]:
        """
        Get votes for a specific proposal.

        Args:
            proposal_id: The Snapshot proposal ID

        Returns:
            List of VotingData objects
        """
        query = """
        query GetVotes($proposal_id: String!) {
          votes(
            first: 1000,
            skip: 0,
            where: {
              proposal: $proposal_id
            }
          ) {
            id
            voter
            choice
            vp
            vp_by_strategy
            created
          }
        }
        """

        variables = {"proposal_id": proposal_id}

        try:
            result = await self._execute_query(query, variables)
            votes_data = result.get("data", {}).get("votes", [])

            logger.info(f"Retrieved {len(votes_data)} votes for proposal {proposal_id}")

            # Convert to VotingData objects
            votes = []
            for v in votes_data:
                try:
                    vote = VotingData(
                        proposal_id=proposal_id,
                        voter_address=v["voter"],
                        choice=v["choice"],
                        voting_power=v["vp"],
                        timestamp=datetime.fromtimestamp(v["created"]),
                        raw_data=v
                    )
                    votes.append(vote)
                except Exception as e:
                    logger.error(f"Error parsing vote {v['id']}: {str(e)}")

            return votes

        except Exception as e:
            logger.error(f"Error fetching votes for proposal {proposal_id}: {str(e)}")
            return []

    async def get_space_details(self, space_id: str) -> Dict[str, Any]:
        """
        Get details about a specific Snapshot space (DAO).

        Args:
            space_id: The Snapshot space ID

        Returns:
            Dictionary with space details
        """
        query = """
        query GetSpace($space_id: String!) {
          space(id: $space_id) {
            id
            name
            about
            network
            symbol
            members
            admins
            strategies {
              name
              params
            }
            filters {
              minScore
              onlyMembers
            }
            plugins
          }
        }
        """

        variables = {"space_id": space_id}

        try:
            result = await self._execute_query(query, variables)
            return result.get("data", {}).get("space", {})
        except Exception as e:
            logger.error(f"Error fetching space details for {space_id}: {str(e)}")
            return {}
