"""
Mock Signal Generator for Testing

This module provides a mock signal generator for testing the enhanced signal generator.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class MockSignalGenerator:
    """Mock signal generator for testing."""
    
    def __init__(self):
        """Initialize the mock signal generator."""
        logger.info("Mock signal generator initialized")
    
    async def generate_crypto_signals(self) -> List[Dict[str, Any]]:
        """
        Generate mock trading signals for cryptocurrencies.
        
        Returns:
            List of mock trading signals
        """
        # Create mock signals for BTC and ETH
        signals = [
            {
                "id": f"signal_bitcoin_{uuid.uuid4()}",
                "asset": "BTC",
                "asset_id": "bitcoin",
                "asset_symbol": "BTC",
                "asset_name": "Bitcoin",
                "signal_type": "buy",
                "time_frame": "1d",
                "confidence": 0.75,
                "price": 87245.32,
                "timestamp": datetime.now().isoformat(),
                "expiration": (datetime.now() + timedelta(days=1)).isoformat(),
                "indicators": {
                    "sma_5": 86500.25,
                    "sma_10": 85200.75,
                    "rsi": 62.5,
                    "price_change_24h": 2.3
                }
            },
            {
                "id": f"signal_ethereum_{uuid.uuid4()}",
                "asset": "ETH",
                "asset_id": "ethereum",
                "asset_symbol": "ETH",
                "asset_name": "Ethereum",
                "signal_type": "hold",
                "time_frame": "1d",
                "confidence": 0.6,
                "price": 3487.21,
                "timestamp": datetime.now().isoformat(),
                "expiration": (datetime.now() + timedelta(days=1)).isoformat(),
                "indicators": {
                    "sma_5": 3450.15,
                    "sma_10": 3425.80,
                    "rsi": 55.2,
                    "price_change_24h": 0.8
                }
            }
        ]
        
        logger.info(f"Generated {len(signals)} mock signals")
        return signals

async def main():
    """Main function for testing the mock signal generator."""
    generator = MockSignalGenerator()
    signals = await generator.generate_crypto_signals()
    
    print(f"Generated {len(signals)} mock signals")
    
    if signals:
        print("\nSample Signal:")
        for key, value in signals[0].items():
            print(f"{key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
