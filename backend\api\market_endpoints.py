"""
Market Data API Endpoints

This module provides API endpoints for accessing cryptocurrency market data.
"""

import os
import json
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import Dict, List, Optional, Any

# Import clients
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from data_aggregator.coingecko_client import CoinGeckoClient, COIN_IDS
from data_aggregator.cryptocompare_client import CryptoCompareClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('market_api')

# Create router
router = APIRouter(
    prefix="/api/market",
    tags=["market"],
    responses={404: {"description": "Not found"}},
)

# Initialize clients
coingecko_client = CoinGeckoClient()
cryptocompare_client = CryptoCompareClient()

# Data paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')

# Models
class PriceData(BaseModel):
    price: float
    market_cap: float
    volume_24h: float
    price_change_24h: float
    last_updated: str

class MarketData(BaseModel):
    prices: List[List[float]]
    market_caps: List[List[float]]
    total_volumes: List[List[float]]

class OHLCData(BaseModel):
    timestamp: int
    open: float
    high: float
    low: float
    close: float

class CoinData(BaseModel):
    id: str
    symbol: str
    name: str
    price_data: PriceData
    market_data: Optional[MarketData]
    ohlc_data: Optional[List[OHLCData]]

@router.get("/coins")
async def get_supported_coins():
    """Get a list of supported coins."""
    return {"coins": list(COIN_IDS.keys())}

@router.get("/price/{coin}")
async def get_coin_price(coin: str):
    """
    Get current price data for a specific coin.

    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    try:
        # Check if coin is supported
        if coin.upper() not in COIN_IDS:
            raise HTTPException(status_code=404, detail=f"Coin not found: {coin}")

        # Get price data from CoinGecko
        price_data = coingecko_client.get_price([coin.upper()])

        if not price_data:
            # Try to get from saved data
            coin_dir = os.path.join(DATA_DIR, coin.lower())
            price_file = os.path.join(coin_dir, "price_latest.json")

            if os.path.exists(price_file):
                with open(price_file, 'r') as f:
                    price_data = json.load(f)
            else:
                raise HTTPException(status_code=404, detail=f"Price data not found for {coin}")

        # Format response
        coin_id = COIN_IDS[coin.upper()]
        if coin_id in price_data:
            data = price_data[coin_id]
            return {
                "symbol": coin.upper(),
                "price_usd": data.get("usd", 0),
                "market_cap_usd": data.get("usd_market_cap", 0),
                "volume_24h_usd": data.get("usd_24h_vol", 0),
                "price_change_24h": data.get("usd_24h_change", 0),
                "last_updated": datetime.fromtimestamp(data.get("last_updated_at", 0)).isoformat()
            }
        else:
            raise HTTPException(status_code=404, detail=f"Price data not found for {coin}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting price for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/chart/{coin}")
async def get_coin_chart(
    coin: str,
    days: int = Query(30, description="Number of days of data to retrieve")
):
    """
    Get historical chart data for a specific coin.

    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
        days: Number of days of data to retrieve
    """
    try:
        # Check if coin is supported
        if coin.upper() not in COIN_IDS:
            raise HTTPException(status_code=404, detail=f"Coin not found: {coin}")

        # Get chart data from CoinGecko
        chart_data = coingecko_client.get_market_chart(coin.upper(), days)

        if not chart_data:
            # Try to get from saved data
            coin_dir = os.path.join(DATA_DIR, coin.lower())
            chart_file = os.path.join(coin_dir, "chart_latest.json")

            if os.path.exists(chart_file):
                with open(chart_file, 'r') as f:
                    chart_data = json.load(f)
            else:
                raise HTTPException(status_code=404, detail=f"Chart data not found for {coin}")

        # Format response
        return {
            "symbol": coin.upper(),
            "days": days,
            "prices": chart_data.get("prices", []),
            "market_caps": chart_data.get("market_caps", []),
            "total_volumes": chart_data.get("total_volumes", [])
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chart for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/ohlc/{coin}")
async def get_coin_ohlc(
    coin: str,
    days: int = Query(30, description="Number of days of data to retrieve")
):
    """
    Get OHLC (Open, High, Low, Close) data for a specific coin.

    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
        days: Number of days of data to retrieve
    """
    try:
        # Check if coin is supported
        if coin.upper() not in COIN_IDS:
            raise HTTPException(status_code=404, detail=f"Coin not found: {coin}")

        # Get OHLC data from CoinGecko
        ohlc_data = coingecko_client.get_ohlc(coin.upper(), days)

        if not ohlc_data:
            # Try to get from saved data
            coin_dir = os.path.join(DATA_DIR, coin.lower())
            ohlc_file = os.path.join(coin_dir, "ohlc_latest.json")

            if os.path.exists(ohlc_file):
                with open(ohlc_file, 'r') as f:
                    ohlc_data = json.load(f)
            else:
                raise HTTPException(status_code=404, detail=f"OHLC data not found for {coin}")

        # Format response
        formatted_data = []
        for item in ohlc_data:
            if len(item) >= 5:
                formatted_data.append({
                    "timestamp": item[0],
                    "open": item[1],
                    "high": item[2],
                    "low": item[3],
                    "close": item[4]
                })

        return {
            "symbol": coin.upper(),
            "days": days,
            "ohlc": formatted_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting OHLC for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/data/{coin}")
async def get_coin_data(coin: str):
    """
    Get comprehensive data for a specific coin.

    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    try:
        # Check if coin is supported
        if coin.upper() not in COIN_IDS:
            raise HTTPException(status_code=404, detail=f"Coin not found: {coin}")

        # Get data from CoinGecko
        coin_data = coingecko_client.get_coin_data(coin.upper())

        if not coin_data:
            # Try to get from saved data
            coin_dir = os.path.join(DATA_DIR, coin.lower())
            data_file = os.path.join(coin_dir, "data_latest.json")

            if os.path.exists(data_file):
                with open(data_file, 'r') as f:
                    coin_data = json.load(f)
            else:
                raise HTTPException(status_code=404, detail=f"Data not found for {coin}")

        # Format response (simplified version)
        market_data = coin_data.get("market_data", {})

        return {
            "id": coin_data.get("id", ""),
            "symbol": coin_data.get("symbol", "").upper(),
            "name": coin_data.get("name", ""),
            "current_price": market_data.get("current_price", {}).get("usd", 0),
            "market_cap": market_data.get("market_cap", {}).get("usd", 0),
            "total_volume": market_data.get("total_volume", {}).get("usd", 0),
            "high_24h": market_data.get("high_24h", {}).get("usd", 0),
            "low_24h": market_data.get("low_24h", {}).get("usd", 0),
            "price_change_24h": market_data.get("price_change_24h", 0),
            "price_change_percentage_24h": market_data.get("price_change_percentage_24h", 0),
            "market_cap_change_24h": market_data.get("market_cap_change_24h", 0),
            "market_cap_change_percentage_24h": market_data.get("market_cap_change_percentage_24h", 0),
            "circulating_supply": market_data.get("circulating_supply", 0),
            "total_supply": market_data.get("total_supply", 0),
            "max_supply": market_data.get("max_supply", 0),
            "ath": market_data.get("ath", {}).get("usd", 0),
            "ath_change_percentage": market_data.get("ath_change_percentage", {}).get("usd", 0),
            "ath_date": market_data.get("ath_date", {}).get("usd", ""),
            "atl": market_data.get("atl", {}).get("usd", 0),
            "atl_change_percentage": market_data.get("atl_change_percentage", {}).get("usd", 0),
            "atl_date": market_data.get("atl_date", {}).get("usd", ""),
            "last_updated": market_data.get("last_updated", "")
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/global")
async def get_global_data():
    """Get global cryptocurrency market data."""
    try:
        # Get global data from CoinGecko
        global_data = coingecko_client.get_global_data()

        if not global_data or "data" not in global_data:
            raise HTTPException(status_code=404, detail="Global market data not found")

        # Format response
        data = global_data["data"]
        return {
            "active_cryptocurrencies": data.get("active_cryptocurrencies", 0),
            "total_market_cap_usd": data.get("total_market_cap", {}).get("usd", 0),
            "total_volume_usd": data.get("total_volume", {}).get("usd", 0),
            "market_cap_percentage": data.get("market_cap_percentage", {}),
            "market_cap_change_percentage_24h_usd": data.get("market_cap_change_percentage_24h_usd", 0),
            "updated_at": data.get("updated_at", 0)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting global data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/trending")
async def get_trending_coins():
    """Get trending coins (top-7 trending coins on CoinGecko)."""
    try:
        # Get trending data from CoinGecko
        trending_data = coingecko_client.get_trending()

        if not trending_data or "coins" not in trending_data:
            raise HTTPException(status_code=404, detail="Trending coins data not found")

        # Format response
        trending_coins = []
        for item in trending_data["coins"]:
            coin = item.get("item", {})
            trending_coins.append({
                "id": coin.get("id", ""),
                "name": coin.get("name", ""),
                "symbol": coin.get("symbol", ""),
                "market_cap_rank": coin.get("market_cap_rank", 0),
                "thumb": coin.get("thumb", ""),
                "small": coin.get("small", "")
            })

        return {"trending_coins": trending_coins}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trending coins: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/refresh")
async def refresh_market_data():
    """Refresh market data for all coins."""
    try:
        # Save market data for all coins
        results = coingecko_client.save_all_market_data()

        # Count successful saves
        success_count = sum(1 for files in results.values() if files)

        return {
            "status": "success" if success_count > 0 else "partial_success",
            "message": f"Refreshed market data for {success_count}/{len(COIN_IDS)} coins",
            "details": {coin: bool(files) for coin, files in results.items()}
        }
    except Exception as e:
        logger.error(f"Error refreshing market data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Import for datetime
from datetime import datetime

@router.get("/live-price/{symbol}")
async def get_live_price(symbol: str):
    """
    Get the latest live price for a cryptocurrency from CryptoCompare.

    Args:
        symbol: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    try:
        # Get price data from CryptoCompare
        price_data = cryptocompare_client.get_price(symbol.upper())

        if not price_data or symbol.upper() not in price_data:
            raise HTTPException(status_code=404, detail=f"Price data not found for {symbol}")

        # Extract price
        symbol_data = price_data[symbol.upper()]
        if "USD" not in symbol_data:
            raise HTTPException(status_code=404, detail=f"USD price not found for {symbol}")

        price = symbol_data["USD"]["PRICE"]

        # Format response
        return {
            "symbol": symbol.upper(),
            "price": price,
            "last_updated": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting live price for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
