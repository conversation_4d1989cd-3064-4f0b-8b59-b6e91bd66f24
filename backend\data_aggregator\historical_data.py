"""
Historical On-Chain Data Storage

This module handles the storage and retrieval of historical on-chain metrics.
It provides functionality to store daily snapshots and retrieve time series data.
"""

import os
import json
import logging
import pandas as pd
from datetime import datetime, timedelta
import sqlite3

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('historical_data')

# Database path
DB_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
DB_PATH = os.path.join(DB_DIR, 'onchain_history.db')
os.makedirs(DB_DIR, exist_ok=True)

class HistoricalDataManager:
    """Manages historical on-chain data storage and retrieval."""
    
    def __init__(self):
        """Initialize the database connection."""
        self.conn = None
        self.initialize_database()
    
    def initialize_database(self):
        """Create the database and tables if they don't exist."""
        try:
            self.conn = sqlite3.connect(DB_PATH)
            cursor = self.conn.cursor()
            
            # Create tables for each cryptocurrency
            for coin in ['BTC', 'ETH', 'SOL']:
                # Main metrics table
                cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS {coin.lower()}_metrics (
                    date TEXT PRIMARY KEY,
                    active_addresses INTEGER,
                    transaction_count INTEGER,
                    transaction_volume REAL,
                    average_transaction_value REAL,
                    hash_rate REAL,
                    difficulty REAL,
                    mempool_size INTEGER,
                    exchange_inflow REAL,
                    exchange_outflow REAL,
                    exchange_netflow REAL,
                    total_whale_balance REAL,
                    mining_concentration REAL,
                    data_json TEXT
                )
                ''')
                
                # Price data table
                cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS {coin.lower()}_prices (
                    date TEXT PRIMARY KEY,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    volume REAL
                )
                ''')
            
            self.conn.commit()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
    
    def close(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()
    
    def store_metrics(self, coin, metrics):
        """
        Store on-chain metrics for a specific coin.
        
        Args:
            coin: Cryptocurrency symbol (BTC, ETH, SOL)
            metrics: Dictionary of metrics
        """
        try:
            if not self.conn:
                self.initialize_database()
            
            cursor = self.conn.cursor()
            
            # Extract date from timestamp
            timestamp = metrics.get('timestamp')
            if not timestamp:
                date = datetime.now().strftime('%Y-%m-%d')
            else:
                date = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
            
            # Extract common metrics
            active_addresses = metrics.get('active_addresses_24h', 0)
            transaction_count = metrics.get('transaction_count_24h', 0) or metrics.get('transaction_count_recent', 0)
            transaction_volume = metrics.get('transaction_volume_24h', 0)
            avg_tx_value = metrics.get('average_transaction_value', 0)
            hash_rate = metrics.get('hash_rate', 0)
            difficulty = metrics.get('difficulty', 0)
            mempool_size = metrics.get('mempool_size', 0)
            exchange_inflow = metrics.get('exchange_inflow_24h', 0)
            exchange_outflow = metrics.get('exchange_outflow_24h', 0)
            exchange_netflow = metrics.get('exchange_netflow_24h', 0)
            total_whale_balance = metrics.get('total_whale_balance', 0)
            mining_concentration = metrics.get('mining_concentration', 0)
            
            # Store full JSON for future reference
            data_json = json.dumps(metrics)
            
            # Insert into database
            cursor.execute(f'''
            INSERT OR REPLACE INTO {coin.lower()}_metrics
            (date, active_addresses, transaction_count, transaction_volume, average_transaction_value,
             hash_rate, difficulty, mempool_size, exchange_inflow, exchange_outflow, exchange_netflow,
             total_whale_balance, mining_concentration, data_json)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                date, active_addresses, transaction_count, transaction_volume, avg_tx_value,
                hash_rate, difficulty, mempool_size, exchange_inflow, exchange_outflow, exchange_netflow,
                total_whale_balance, mining_concentration, data_json
            ))
            
            self.conn.commit()
            logger.info(f"Stored {coin} metrics for {date}")
            return True
        except Exception as e:
            logger.error(f"Error storing {coin} metrics: {str(e)}")
            return False
    
    def store_price_data(self, coin, price_data):
        """
        Store price data for a specific coin.
        
        Args:
            coin: Cryptocurrency symbol (BTC, ETH, SOL)
            price_data: Dictionary with price data (open, high, low, close, volume)
        """
        try:
            if not self.conn:
                self.initialize_database()
            
            cursor = self.conn.cursor()
            
            # Extract date
            date = price_data.get('date', datetime.now().strftime('%Y-%m-%d'))
            
            # Extract price data
            open_price = price_data.get('open', 0)
            high_price = price_data.get('high', 0)
            low_price = price_data.get('low', 0)
            close_price = price_data.get('close', 0)
            volume = price_data.get('volume', 0)
            
            # Insert into database
            cursor.execute(f'''
            INSERT OR REPLACE INTO {coin.lower()}_prices
            (date, open, high, low, close, volume)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (date, open_price, high_price, low_price, close_price, volume))
            
            self.conn.commit()
            logger.info(f"Stored {coin} price data for {date}")
            return True
        except Exception as e:
            logger.error(f"Error storing {coin} price data: {str(e)}")
            return False
    
    def get_metrics_history(self, coin, metric_name, days=30):
        """
        Get historical data for a specific metric.
        
        Args:
            coin: Cryptocurrency symbol (BTC, ETH, SOL)
            metric_name: Name of the metric to retrieve
            days: Number of days of history to retrieve
            
        Returns:
            DataFrame with date and metric value
        """
        try:
            if not self.conn:
                self.initialize_database()
            
            # Calculate start date
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Query database
            if metric_name in ['active_addresses', 'transaction_count', 'transaction_volume', 
                              'average_transaction_value', 'hash_rate', 'difficulty', 
                              'mempool_size', 'exchange_inflow', 'exchange_outflow', 
                              'exchange_netflow', 'total_whale_balance', 'mining_concentration']:
                # Direct column query
                query = f'''
                SELECT date, {metric_name} FROM {coin.lower()}_metrics
                WHERE date >= ?
                ORDER BY date
                '''
                df = pd.read_sql_query(query, self.conn, params=(start_date.strftime('%Y-%m-%d'),))
            else:
                # Need to extract from JSON
                query = f'''
                SELECT date, data_json FROM {coin.lower()}_metrics
                WHERE date >= ?
                ORDER BY date
                '''
                raw_df = pd.read_sql_query(query, self.conn, params=(start_date.strftime('%Y-%m-%d'),))
                
                # Extract metric from JSON
                values = []
                for _, row in raw_df.iterrows():
                    data = json.loads(row['data_json'])
                    value = self._extract_nested_metric(data, metric_name)
                    values.append(value)
                
                df = pd.DataFrame({
                    'date': raw_df['date'],
                    metric_name: values
                })
            
            return df
        except Exception as e:
            logger.error(f"Error retrieving {metric_name} history for {coin}: {str(e)}")
            return pd.DataFrame(columns=['date', metric_name])
    
    def _extract_nested_metric(self, data, metric_path):
        """
        Extract a nested metric from a JSON object.
        
        Args:
            data: Dictionary of metrics
            metric_path: Path to the metric (e.g., 'defi_stats.total_value_locked')
            
        Returns:
            Metric value or 0 if not found
        """
        try:
            parts = metric_path.split('.')
            value = data
            for part in parts:
                value = value.get(part, {})
            
            # If we ended up with a dictionary, return 0
            if isinstance(value, dict):
                return 0
            
            return value
        except Exception:
            return 0
    
    def get_price_history(self, coin, days=30):
        """
        Get historical price data.
        
        Args:
            coin: Cryptocurrency symbol (BTC, ETH, SOL)
            days: Number of days of history to retrieve
            
        Returns:
            DataFrame with date and OHLCV data
        """
        try:
            if not self.conn:
                self.initialize_database()
            
            # Calculate start date
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Query database
            query = f'''
            SELECT date, open, high, low, close, volume FROM {coin.lower()}_prices
            WHERE date >= ?
            ORDER BY date
            '''
            df = pd.read_sql_query(query, self.conn, params=(start_date.strftime('%Y-%m-%d'),))
            
            return df
        except Exception as e:
            logger.error(f"Error retrieving price history for {coin}: {str(e)}")
            return pd.DataFrame(columns=['date', 'open', 'high', 'low', 'close', 'volume'])
    
    def get_combined_data(self, coin, metric_names, days=30):
        """
        Get combined price and metrics data.
        
        Args:
            coin: Cryptocurrency symbol (BTC, ETH, SOL)
            metric_names: List of metric names to retrieve
            days: Number of days of history to retrieve
            
        Returns:
            DataFrame with date, price, and metrics data
        """
        try:
            # Get price data
            price_df = self.get_price_history(coin, days)
            
            # Start with price data as the base
            combined_df = price_df.copy()
            
            # Add each metric
            for metric in metric_names:
                metric_df = self.get_metrics_history(coin, metric, days)
                if not metric_df.empty:
                    # Merge on date
                    combined_df = pd.merge(combined_df, metric_df, on='date', how='left')
            
            return combined_df
        except Exception as e:
            logger.error(f"Error retrieving combined data for {coin}: {str(e)}")
            return pd.DataFrame()
    
    def get_correlation_matrix(self, coin, metric_names, days=30):
        """
        Calculate correlation matrix between price and on-chain metrics.
        
        Args:
            coin: Cryptocurrency symbol (BTC, ETH, SOL)
            metric_names: List of metric names to include
            days: Number of days of history to use
            
        Returns:
            DataFrame with correlation matrix
        """
        try:
            # Get combined data
            df = self.get_combined_data(coin, metric_names, days)
            
            if df.empty:
                return pd.DataFrame()
            
            # Add price change column
            df['price_change'] = df['close'].pct_change()
            
            # Calculate correlation matrix
            columns_to_include = ['price_change'] + metric_names
            correlation_matrix = df[columns_to_include].corr()
            
            return correlation_matrix
        except Exception as e:
            logger.error(f"Error calculating correlation matrix for {coin}: {str(e)}")
            return pd.DataFrame()

# Singleton instance
_instance = None

def get_instance():
    """Get the singleton instance of HistoricalDataManager."""
    global _instance
    if _instance is None:
        _instance = HistoricalDataManager()
    return _instance
