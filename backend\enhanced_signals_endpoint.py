"""
Enhanced Signals API Endpoint

This module provides an API endpoint for enhanced trading signals with additional context.
"""

import logging
import json
import asyncio
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException

# Import data fetchers and signal generator
import sys
import os
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
from data_aggregator.binance_futures_mini import BinanceFuturesDataFetcher
from data_aggregator.alpha_vantage_mini import AlphaVantageEconomicFetcher
from data_aggregator.coingecko_onchain import CoinGeckoOnChainFetcher
from data_aggregator.nlp_simple import SimpleSentimentAnalyzer
from signal_generator import AdvancedSignalGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create API router
router = APIRouter()

@router.get("/signals/enhanced")
async def get_enhanced_signals() -> List[Dict[str, Any]]:
    """
    Get enhanced trading signals with additional context.

    Returns:
        List of enhanced trading signals
    """
    try:
        logger.info("Fetching enhanced trading signals...")

        # Initialize data fetchers and signal generator
        binance_fetcher = BinanceFuturesDataFetcher()
        alpha_vantage_fetcher = AlphaVantageEconomicFetcher()
        coingecko_fetcher = CoinGeckoOnChainFetcher()
        sentiment_analyzer = SimpleSentimentAnalyzer()
        signal_generator = AdvancedSignalGenerator()

        # Fetch real derivatives data from Binance
        symbols = ["BTCUSDT", "ETHUSDT", "SOLUSDT"]
        futures_data = await binance_fetcher.fetch_all_futures_data(symbols)

        # Analyze derivatives sentiment
        derivatives_sentiment = binance_fetcher.analyze_futures_sentiment(futures_data)

        # Create a mapping of futures data by symbol
        futures_data_map = {data["symbol"].replace("USDT", ""): data for data in futures_data}

        # Fetch economic indicators from Alpha Vantage
        economic_indicators = await alpha_vantage_fetcher.fetch_all_indicators()

        # Analyze economic trends
        economic_trends = alpha_vantage_fetcher.analyze_economic_trends(economic_indicators)

        # Fetch on-chain data from CoinGecko
        coin_ids = ["bitcoin", "ethereum", "solana"]
        onchain_metrics = await coingecko_fetcher.fetch_all_onchain_metrics(coin_ids)

        # Analyze on-chain sentiment
        onchain_sentiment = coingecko_fetcher.analyze_onchain_sentiment(onchain_metrics)

        # Create a mapping of on-chain metrics by symbol
        onchain_metrics_map = {data.get("symbol", "").upper(): data for data in onchain_metrics}

        # Fetch and analyze news sentiment
        crypto_symbols = ["BTC", "ETH", "SOL"]
        news_sentiment_map = {}

        for symbol in crypto_symbols:
            # Analyze news sentiment for each cryptocurrency
            sentiment_result = await sentiment_analyzer.get_crypto_sentiment(symbol)
            news_sentiment_map[symbol] = sentiment_result

        # Prepare data for the signal generator
        # Create a mapping of derivatives data by symbol (without USDT suffix)
        derivatives_data = {}
        for data in futures_data:
            symbol = data["symbol"].replace("USDT", "")
            derivatives_data[symbol] = data

        # Get current prices
        price_data = {
            "BTC": derivatives_data.get("BTC", {}).get("mark_price", 0.0),
            "ETH": derivatives_data.get("ETH", {}).get("mark_price", 0.0),
            "SOL": derivatives_data.get("SOL", {}).get("mark_price", 0.0)
        }

        # Generate advanced signals using the signal generator
        signals = signal_generator.generate_signals(
            derivatives_data=derivatives_data,
            derivatives_sentiment=derivatives_sentiment,
            economic_trends=economic_trends,
            onchain_metrics=onchain_metrics_map,
            onchain_sentiment=onchain_sentiment,
            news_sentiment=news_sentiment_map,
            price_data=price_data
        )

        logger.info(f"Generated {len(signals)} advanced trading signals using sophisticated algorithms with real derivatives, economic, on-chain, and news sentiment data")
        return signals

    except Exception as e:
        logger.error(f"Error generating enhanced signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating enhanced signals: {str(e)}")

def register_endpoints(app):
    """
    Register the enhanced signals endpoints with the FastAPI app.

    Args:
        app: FastAPI app
    """
    app.include_router(router)
    logger.info("Enhanced signals endpoints registered")
