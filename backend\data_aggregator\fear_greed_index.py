"""
Crypto Fear & Greed Index Fetcher

This module fetches the Crypto Fear & Greed Index to provide market sentiment data.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class FearGreedIndexFetcher:
    """Fetches the Crypto Fear & Greed Index."""

    def __init__(self):
        """Initialize the Fear & Greed Index fetcher."""
        logger.info("Fear & Greed Index fetcher initialized")

    async def fetch_current_index(self) -> Optional[Dict[str, Any]]:
        """
        Fetch the current Fear & Greed Index.

        Returns:
            Dictionary with Fear & Greed Index data
        """
        try:
            url = "https://api.alternative.me/fng/"
            params = {
                "limit": 1,
                "format": "json"
            }

            logger.info("Fetching live Fear & Greed Index data...")
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if "data" in data and len(data["data"]) > 0:
                    latest = data["data"][0]
                    result = {
                        "value": int(latest["value"]),
                        "value_classification": latest["value_classification"],
                        "timestamp": latest["timestamp"],
                        "source": "alternative.me"
                    }
                    logger.info(f"Successfully fetched Fear & Greed Index: {result['value']} ({result['value_classification']})")
                    return result

            logger.warning("No Fear & Greed Index data found")
            return None

        except Exception as e:
            logger.error(f"Error fetching Fear & Greed Index: {str(e)}")
            return None

    async def fetch_historical_index(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        Fetch historical Fear & Greed Index data.

        Args:
            days: Number of days of historical data to fetch

        Returns:
            List of dictionaries with historical Fear & Greed Index data
        """
        try:
            url = "https://api.alternative.me/fng/"
            params = {
                "limit": days,
                "format": "json"
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                historical_data = []
                if "data" in data:
                    for item in data["data"]:
                        historical_data.append({
                            "value": int(item["value"]),
                            "value_classification": item["value_classification"],
                            "timestamp": item["timestamp"],
                            "date": datetime.fromtimestamp(int(item["timestamp"]), tz=timezone.utc).strftime("%Y-%m-%d"),
                            "source": "alternative.me"
                        })

                logger.info(f"Fetched {len(historical_data)} days of historical Fear & Greed Index data")
                return historical_data

        except Exception as e:
            logger.error(f"Error fetching historical Fear & Greed Index: {str(e)}")
            return []

    def to_context_items(self, index_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Convert Fear & Greed Index data to context items for MCP.

        Args:
            index_data: Dictionary with Fear & Greed Index data

        Returns:
            List of context items
        """
        if not index_data:
            return []

        context_item = {
            "id": f"fear_greed_{index_data['timestamp']}_{uuid.uuid4()}",
            "type": "market_sentiment",
            "content": {
                "indicator": "fear_greed_index",
                "value": index_data["value"],
                "classification": index_data["value_classification"],
                "timestamp": index_data["timestamp"]
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": index_data.get("source", "unknown"),
            "confidence": 1.0
        }

        return [context_item]

    def analyze_sentiment(self, index_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze sentiment based on Fear & Greed Index.

        Args:
            index_data: Dictionary with Fear & Greed Index data

        Returns:
            Dictionary with sentiment analysis
        """
        if not index_data:
            return {
                "sentiment": "neutral",
                "score": 0.5,
                "confidence": 0.0
            }

        value = index_data["value"]

        # Map Fear & Greed value to sentiment
        if value <= 20:  # Extreme Fear
            sentiment = "very_bearish"
            score = 0.1
        elif value <= 40:  # Fear
            sentiment = "bearish"
            score = 0.3
        elif value <= 60:  # Neutral
            sentiment = "neutral"
            score = 0.5
        elif value <= 80:  # Greed
            sentiment = "bullish"
            score = 0.7
        else:  # Extreme Greed
            sentiment = "very_bullish"
            score = 0.9

        return {
            "sentiment": sentiment,
            "score": score,
            "confidence": 0.8  # Fear & Greed Index is a reliable sentiment indicator
        }

async def main():
    """Main function for testing the Fear & Greed Index fetcher."""
    fetcher = FearGreedIndexFetcher()

    # Fetch current index
    current_index = await fetcher.fetch_current_index()

    if current_index:
        print("\nCurrent Fear & Greed Index:")
        print(f"Value: {current_index['value']}")
        print(f"Classification: {current_index['value_classification']}")
        print(f"Timestamp: {current_index['timestamp']}")

        # Convert to context items
        context_items = fetcher.to_context_items(current_index)
        print(f"\nGenerated {len(context_items)} context items")

        # Analyze sentiment
        sentiment = fetcher.analyze_sentiment(current_index)
        print("\nSentiment Analysis:")
        print(f"Sentiment: {sentiment['sentiment']}")
        print(f"Score: {sentiment['score']}")
        print(f"Confidence: {sentiment['confidence']}")

    # Fetch historical data
    historical_data = await fetcher.fetch_historical_index(days=7)

    if historical_data:
        print("\nHistorical Fear & Greed Index (Last 7 days):")
        for item in historical_data:
            print(f"{item['date']}: {item['value']} ({item['value_classification']})")

if __name__ == "__main__":
    asyncio.run(main())
