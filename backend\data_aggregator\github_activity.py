"""
GitHub Activity Fetcher

This module fetches developer activity data from GitHub for cryptocurrency projects
and formats it for the MCP server.
"""

import logging
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

import httpx
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
GITHUB_API_URL = "https://api.github.com"
REQUEST_TIMEOUT = 30  # seconds
RATE_LIMIT_DELAY = 1.0  # seconds between requests to avoid rate limiting

# Default crypto projects to track
DEFAULT_PROJECTS = [
    {"name": "Bitcoin", "repo": "bitcoin/bitcoin"},
    {"name": "Ethereum", "repo": "ethereum/go-ethereum"},
    {"name": "Solana", "repo": "solana-labs/solana"},
    {"name": "Polkadot", "repo": "paritytech/polkadot"},
    {"name": "Cardano", "repo": "input-output-hk/cardano-node"},
    {"name": "Uniswap", "repo": "Uniswap/v3-core"},
    {"name": "Aave", "repo": "aave/aave-protocol"},
    {"name": "Chainlink", "repo": "smartcontractkit/chainlink"}
]

class GitHubStats(BaseModel):
    """Model for GitHub repository statistics."""
    repo: str
    name: str
    stars: int
    forks: int
    open_issues: int
    watchers: int
    commit_activity: List[Dict[str, Any]]
    contributors: List[Dict[str, Any]]
    timestamp: datetime

class ContextItem(BaseModel):
    """Model for MCP context items."""
    id: str
    type: str
    content: Dict[str, Any]
    timestamp: datetime
    source: str
    confidence: float = 1.0

class GitHubActivityFetcher:
    """Fetches developer activity data from GitHub."""

    def __init__(self, api_token: Optional[str] = None, projects: List[Dict[str, str]] = None):
        """Initialize the fetcher with optional API token."""
        self.api_token = api_token
        self.headers = {}
        if api_token:
            self.headers["Authorization"] = f"token {api_token}"
        self.headers["Accept"] = "application/vnd.github.v3+json"
        self.projects = projects or DEFAULT_PROJECTS
        logger.info(f"GitHub activity fetcher initialized with {len(self.projects)} projects")

    async def fetch_repo_stats(self, repo: str) -> Optional[Dict[str, Any]]:
        """Fetch basic repository statistics."""
        url = f"{GITHUB_API_URL}/repos/{repo}"

        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, headers=self.headers)
                response.raise_for_status()
                data = response.json()

                # Extract relevant stats
                stats = {
                    "stars": data["stargazers_count"],
                    "forks": data["forks_count"],
                    "open_issues": data["open_issues_count"],
                    "watchers": data["subscribers_count"],
                    "created_at": data["created_at"],
                    "updated_at": data["updated_at"],
                    "language": data["language"],
                    "description": data["description"]
                }

                logger.info(f"Fetched basic stats for {repo}")
                return stats

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching repo stats for {repo}: {e}")
            if e.response.status_code == 403 and "rate limit" in e.response.text.lower():
                logger.warning("GitHub API rate limit exceeded")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error fetching repo stats for {repo}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching repo stats for {repo}: {e}")
            return None

    async def fetch_commit_activity(self, repo: str) -> List[Dict[str, Any]]:
        """Fetch commit activity for the past year."""
        url = f"{GITHUB_API_URL}/repos/{repo}/stats/commit_activity"

        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, headers=self.headers)
                response.raise_for_status()
                data = response.json()

                # GitHub may return 202 and compute the stats asynchronously
                if response.status_code == 202:
                    logger.info(f"GitHub is computing stats for {repo}, waiting...")
                    await asyncio.sleep(2)
                    return await self.fetch_commit_activity(repo)

                logger.info(f"Fetched commit activity for {repo}")
                return data

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching commit activity for {repo}: {e}")
            return []
        except httpx.RequestError as e:
            logger.error(f"Request error fetching commit activity for {repo}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching commit activity for {repo}: {e}")
            return []

    async def fetch_contributors(self, repo: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Fetch top contributors to the repository."""
        url = f"{GITHUB_API_URL}/repos/{repo}/contributors"
        params = {"per_page": limit}

        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()

                # Extract relevant contributor info
                contributors = []
                for contributor in data:
                    contributors.append({
                        "login": contributor["login"],
                        "contributions": contributor["contributions"],
                        "url": contributor["html_url"]
                    })

                logger.info(f"Fetched {len(contributors)} contributors for {repo}")
                return contributors

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching contributors for {repo}: {e}")
            return []
        except httpx.RequestError as e:
            logger.error(f"Request error fetching contributors for {repo}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching contributors for {repo}: {e}")
            return []

    async def fetch_recent_commits(self, repo: str, days: int = 7) -> List[Dict[str, Any]]:
        """Fetch recent commits to the repository."""
        url = f"{GITHUB_API_URL}/repos/{repo}/commits"
        since = (datetime.now(timezone.utc) - timedelta(days=days)).isoformat()
        params = {"since": since}

        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()

                # Extract relevant commit info
                commits = []
                for commit in data:
                    commits.append({
                        "sha": commit["sha"],
                        "message": commit["commit"]["message"],
                        "author": commit["commit"]["author"]["name"],
                        "date": commit["commit"]["author"]["date"],
                        "url": commit["html_url"]
                    })

                logger.info(f"Fetched {len(commits)} recent commits for {repo}")
                return commits

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching recent commits for {repo}: {e}")
            return []
        except httpx.RequestError as e:
            logger.error(f"Request error fetching recent commits for {repo}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching recent commits for {repo}: {e}")
            return []

    async def fetch_all_project_stats(self) -> List[GitHubStats]:
        """Fetch statistics for all tracked projects."""
        all_stats = []

        for project in self.projects:
            try:
                repo = project["repo"]
                name = project["name"]

                # Fetch basic repository stats
                basic_stats = await self.fetch_repo_stats(repo)
                if not basic_stats:
                    logger.warning(f"Could not fetch basic stats for {repo}, skipping")
                    continue

                # Fetch commit activity
                commit_activity = await self.fetch_commit_activity(repo)

                # Fetch contributors
                contributors = await self.fetch_contributors(repo)

                # Create stats object
                stats = GitHubStats(
                    repo=repo,
                    name=name,
                    stars=basic_stats["stars"],
                    forks=basic_stats["forks"],
                    open_issues=basic_stats["open_issues"],
                    watchers=basic_stats["watchers"],
                    commit_activity=commit_activity,
                    contributors=contributors,
                    timestamp=datetime.now(timezone.utc)
                )

                all_stats.append(stats)

                # Sleep to avoid rate limiting
                await asyncio.sleep(RATE_LIMIT_DELAY)

            except Exception as e:
                logger.error(f"Error fetching stats for {project['repo']}: {e}")

        logger.info(f"Fetched stats for {len(all_stats)} projects")
        return all_stats

    def stats_to_context_items(self, stats_list: List[GitHubStats]) -> List[ContextItem]:
        """Convert GitHub stats to MCP context items."""
        context_items = []

        for stats in stats_list:
            # Calculate developer activity metrics
            total_commits = sum(week["total"] for week in stats.commit_activity) if stats.commit_activity else 0

            # Make sure recent_commits is positive
            if stats.commit_activity and len(stats.commit_activity) >= 4:
                recent_commits = sum(max(0, week["total"]) for week in stats.commit_activity[-4:])
            else:
                recent_commits = 0

            total_contributors = len(stats.contributors)
            active_contributors = sum(1 for contributor in stats.contributors if contributor["contributions"] > 5)

            # Calculate activity trend (0-2, where 1 is neutral)
            if total_commits > 0 and len(stats.commit_activity) > 0:
                avg_monthly_commits = total_commits / len(stats.commit_activity) * 4
                if avg_monthly_commits > 0:
                    activity_trend = min(2.0, max(0.1, recent_commits / avg_monthly_commits))
                else:
                    activity_trend = 1.0
            else:
                activity_trend = 1.0

            # Calculate activity score (0-1)
            activity_score = min(1.0, (
                (0.4 * min(1.0, recent_commits / 100)) +  # Recent commit volume
                (0.3 * min(1.0, active_contributors / 10)) +  # Active contributors
                (0.3 * min(1.0, (activity_trend / 2)))  # Trend compared to average (normalized to 0-1)
            ))

            # Create context item
            item = ContextItem(
                id=f"github_activity_{stats.repo.replace('/', '_')}_{uuid.uuid4()}",
                type="github_activity",
                content={
                    "project_name": stats.name,
                    "repository": stats.repo,
                    "stars": stats.stars,
                    "forks": stats.forks,
                    "open_issues": stats.open_issues,
                    "watchers": stats.watchers,
                    "total_commits_year": total_commits,
                    "recent_commits_month": recent_commits,
                    "total_contributors": total_contributors,
                    "active_contributors": active_contributors,
                    "activity_score": activity_score,
                    "activity_trend": activity_trend
                },
                timestamp=stats.timestamp,
                source="github",
                confidence=0.9
            )

            context_items.append(item)

        return context_items

    def _get_sample_stats(self) -> List[GitHubStats]:
        """Get sample GitHub stats for testing."""
        logger.info("Using sample GitHub stats")

        now = datetime.now(timezone.utc)

        # Sample weekly commit data
        sample_commit_activity = [
            {"week": int((now - timedelta(days=7*i)).timestamp()), "total": 30 - i, "days": [5, 6, 4, 3, 5, 2, 5]}
            for i in range(52)
        ]

        # Sample contributors
        sample_contributors = [
            {"login": f"dev{i}", "contributions": 100 - i*10, "url": f"https://github.com/dev{i}"}
            for i in range(10)
        ]

        # Sample stats for projects
        sample_stats = []
        for project in self.projects:
            stats = GitHubStats(
                repo=project["repo"],
                name=project["name"],
                stars=10000 + hash(project["name"]) % 40000,
                forks=1000 + hash(project["name"]) % 5000,
                open_issues=100 + hash(project["name"]) % 500,
                watchers=500 + hash(project["name"]) % 2000,
                commit_activity=sample_commit_activity,
                contributors=sample_contributors,
                timestamp=now
            )
            sample_stats.append(stats)

        return sample_stats

async def main():
    """Main function for testing the fetcher."""
    # Create fetcher with sample token
    fetcher = GitHubActivityFetcher(api_token=None)

    # Use sample data for testing without API token
    stats_list = fetcher._get_sample_stats()

    # Convert to context items
    context_items = fetcher.stats_to_context_items(stats_list)

    # Print the first context item as an example
    if context_items:
        print(f"Example GitHub activity context item: {context_items[0].model_dump_json(indent=2)}")

    print(f"Generated {len(context_items)} GitHub activity context items")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
