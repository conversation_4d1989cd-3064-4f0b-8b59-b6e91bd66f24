<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis Tabs Test | Project Ruby</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/ruby-core.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .analysis-tabs {
            display: flex;
            background: var(--ruby-bg-light);
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .analysis-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: var(--ruby-text-secondary);
            font-weight: 500;
        }
        
        .analysis-tab.active {
            background: var(--ruby-gold);
            color: var(--ruby-bg-dark);
            font-weight: 600;
        }
        
        .analysis-content {
            display: none;
            background: var(--ruby-bg-dark);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid var(--ruby-border);
            min-height: 300px;
        }
        
        .analysis-content.active {
            display: block;
        }
        
        .test-info {
            background: var(--ruby-bg-light);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid var(--ruby-gold);
        }
        
        .consensus-item {
            background: var(--ruby-bg-light);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid var(--ruby-border);
        }
        
        .consensus-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .consensus-symbol {
            font-weight: bold;
            color: var(--ruby-text-primary);
            font-size: 1.1em;
        }
        
        .signal-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .badge-buy { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .badge-hold { background: rgba(156, 163, 175, 0.2); color: #9ca3af; }
        
        .consensus-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }
        
        .consensus-metric {
            text-align: center;
        }
        
        .metric-label {
            font-size: 0.8em;
            color: var(--ruby-text-secondary);
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-weight: bold;
            color: var(--ruby-gold);
            font-size: 1.1em;
        }
        
        .signal-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--ruby-border);
        }
        
        .signal-info {
            display: flex;
            align-items: center;
        }
        
        .signal-symbol {
            font-weight: bold;
            margin-right: 10px;
        }
        
        .signal-strength {
            font-weight: 600;
            color: var(--ruby-gold);
        }
    </style>
</head>
<body class="ruby-theme">
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: var(--ruby-gold);">Analysis Tabs Test</h1>
            <p style="color: var(--ruby-text-secondary);">Testing the 4 analysis tabs to ensure they work properly</p>
        </header>

        <div class="test-info">
            <h3 style="color: var(--ruby-gold); margin-bottom: 10px;">
                <i class="fas fa-info-circle"></i> Test Instructions
            </h3>
            <p>Click each tab below. Each should switch content without page reload. Check browser console for debug logs.</p>
        </div>

        <!-- Analysis Tabs -->
        <div class="analysis-tabs">
            <div class="analysis-tab active" data-tab="signals">Trading Signals</div>
            <div class="analysis-tab" data-tab="sentiment">Market Sentiment</div>
            <div class="analysis-tab" data-tab="consensus">AI Consensus</div>
            <div class="analysis-tab" data-tab="timeframes">Multi-Timeframe</div>
        </div>

        <!-- Trading Signals Content -->
        <div class="analysis-content active" id="signals-content">
            <h2 style="color: var(--ruby-gold); margin-bottom: 15px;">
                <i class="fas fa-chart-line"></i> Trading Signals Test
            </h2>
            <div class="signal-row">
                <div class="signal-info">
                    <span class="signal-symbol">BTC</span>
                    <span class="signal-badge badge-buy">BUY</span>
                </div>
                <span class="signal-strength">85%</span>
            </div>
            <div class="signal-row">
                <div class="signal-info">
                    <span class="signal-symbol">ETH</span>
                    <span class="signal-badge badge-buy">BUY</span>
                </div>
                <span class="signal-strength">72%</span>
            </div>
            <p style="margin-top: 20px; color: var(--ruby-text-secondary);">✅ Signals tab is working!</p>
        </div>

        <!-- Market Sentiment Content -->
        <div class="analysis-content" id="sentiment-content">
            <h2 style="color: var(--ruby-gold); margin-bottom: 15px;">
                <i class="fas fa-comments"></i> Market Sentiment Test
            </h2>
            <div style="text-align: center; margin-bottom: 20px;">
                <div style="font-size: 2em; font-weight: bold; color: var(--ruby-gold);">74</div>
                <div style="color: var(--ruby-text-secondary);">Overall Market Sentiment</div>
            </div>
            <p style="color: var(--ruby-text-secondary);">✅ Sentiment tab is working!</p>
        </div>

        <!-- AI Consensus Content -->
        <div class="analysis-content" id="consensus-content">
            <h2 style="color: var(--ruby-gold); margin-bottom: 15px;">
                <i class="fas fa-brain"></i> AI Consensus Test
            </h2>
            <div class="consensus-item">
                <div class="consensus-header">
                    <span class="consensus-symbol">BTC</span>
                    <span class="signal-badge badge-buy">Bullish Consensus</span>
                </div>
                <div class="consensus-metrics">
                    <div class="consensus-metric">
                        <div class="metric-label">Agreement</div>
                        <div class="metric-value">85%</div>
                    </div>
                    <div class="consensus-metric">
                        <div class="metric-label">Confidence</div>
                        <div class="metric-value">78%</div>
                    </div>
                    <div class="consensus-metric">
                        <div class="metric-label">Models</div>
                        <div class="metric-value">6/7</div>
                    </div>
                </div>
            </div>
            <p style="color: var(--ruby-text-secondary);">✅ Consensus tab is working!</p>
        </div>

        <!-- Multi-Timeframe Content -->
        <div class="analysis-content" id="timeframes-content">
            <h2 style="color: var(--ruby-gold); margin-bottom: 15px;">
                <i class="fas fa-layer-group"></i> Multi-Timeframe Test
            </h2>
            <div class="signal-row">
                <div class="signal-info">
                    <span class="signal-symbol">BTC</span>
                    <span class="signal-badge badge-buy">5m: BUY</span>
                </div>
                <span class="signal-strength">82%</span>
            </div>
            <div class="signal-row">
                <div class="signal-info">
                    <span class="signal-symbol">BTC</span>
                    <span class="signal-badge badge-buy">1h: BUY</span>
                </div>
                <span class="signal-strength">78%</span>
            </div>
            <div class="signal-row">
                <div class="signal-info">
                    <span class="signal-symbol">BTC</span>
                    <span class="signal-badge badge-hold">4h: HOLD</span>
                </div>
                <span class="signal-strength">65%</span>
            </div>
            <p style="margin-top: 20px; color: var(--ruby-text-secondary);">✅ Timeframes tab is working!</p>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <a href="analysis.html" style="
                background: var(--ruby-gold); 
                color: var(--ruby-bg-dark); 
                padding: 12px 24px; 
                border-radius: 8px; 
                text-decoration: none; 
                font-weight: 600;
            ">
                Go to Real Analysis Page
            </a>
        </div>
    </div>

    <script>
        console.log('Analysis tabs test page loaded');
        
        // Tab switching functionality
        document.querySelectorAll('.analysis-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = tab.dataset.tab;
                console.log('🔄 Tab clicked:', tabName);
                
                // Remove active class from all tabs and content
                document.querySelectorAll('.analysis-tab').forEach(t => {
                    t.classList.remove('active');
                });
                document.querySelectorAll('.analysis-content').forEach(c => {
                    c.classList.remove('active');
                });
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const contentElement = document.getElementById(tabName + '-content');
                if (contentElement) {
                    contentElement.classList.add('active');
                    console.log('✅ Successfully switched to tab:', tabName);
                } else {
                    console.error('❌ Content element not found for tab:', tabName);
                }
            });
        });

        // Test all tabs on load
        setTimeout(() => {
            console.log('🧪 Testing all tabs automatically...');
            const tabs = ['signals', 'sentiment', 'consensus', 'timeframes'];
            let index = 0;
            
            const testInterval = setInterval(() => {
                if (index < tabs.length) {
                    const tabElement = document.querySelector(`[data-tab="${tabs[index]}"]`);
                    if (tabElement) {
                        console.log(`🔄 Auto-testing tab: ${tabs[index]}`);
                        tabElement.click();
                    }
                    index++;
                } else {
                    clearInterval(testInterval);
                    console.log('✅ All tabs tested successfully!');
                    // Return to first tab
                    document.querySelector('[data-tab="signals"]').click();
                }
            }, 1000);
        }, 2000);
    </script>
</body>
</html>
