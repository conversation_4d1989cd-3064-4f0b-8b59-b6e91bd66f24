"""
Context Manager for MCP Server

This module handles the storage, retrieval, and management of context information
in the Model Context Protocol server.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class ContextItem(BaseModel):
    """A single piece of context information."""
    id: str
    type: str
    content: Dict[str, Any]
    timestamp: datetime
    source: str
    confidence: float = 1.0

class ContextQuery(BaseModel):
    """Query parameters for retrieving context."""
    types: Optional[List[str]] = None
    sources: Optional[List[str]] = None
    time_range: Optional[Dict[str, datetime]] = None
    limit: int = 100

class ContextManager:
    """Manages context information for the MCP server."""
    
    def __init__(self):
        """Initialize the context manager with an empty storage."""
        self.storage: List[ContextItem] = []
        logger.info("Context manager initialized")
    
    def add_item(self, item: ContextItem) -> str:
        """Add a single context item to storage."""
        self.storage.append(item)
        logger.info(f"Added context item: {item.id} from {item.source}")
        return item.id
    
    def add_items(self, items: List[ContextItem]) -> int:
        """Add multiple context items to storage."""
        for item in items:
            self.storage.append(item)
        logger.info(f"Added {len(items)} context items in batch")
        return len(items)
    
    def query(self, query: ContextQuery) -> List[ContextItem]:
        """Query context items based on specified criteria."""
        results = self.storage.copy()
        
        # Filter by types
        if query.types:
            results = [item for item in results if item.type in query.types]
        
        # Filter by sources
        if query.sources:
            results = [item for item in results if item.source in query.sources]
        
        # Filter by time range
        if query.time_range:
            start = query.time_range.get("start")
            end = query.time_range.get("end", datetime.utcnow())
            if start:
                results = [item for item in results if start <= item.timestamp <= end]
        
        # Sort by timestamp (newest first)
        results.sort(key=lambda x: x.timestamp, reverse=True)
        
        # Apply limit
        results = results[:query.limit]
        
        logger.info(f"Query returned {len(results)} context items")
        return results
    
    def get_item_by_id(self, item_id: str) -> Optional[ContextItem]:
        """Retrieve a specific context item by ID."""
        for item in self.storage:
            if item.id == item_id:
                return item
        return None
    
    def update_item(self, item_id: str, updated_item: ContextItem) -> bool:
        """Update an existing context item."""
        for i, item in enumerate(self.storage):
            if item.id == item_id:
                self.storage[i] = updated_item
                logger.info(f"Updated context item: {item_id}")
                return True
        logger.warning(f"Failed to update context item: {item_id} (not found)")
        return False
    
    def delete_item(self, item_id: str) -> bool:
        """Delete a context item by ID."""
        for i, item in enumerate(self.storage):
            if item.id == item_id:
                del self.storage[i]
                logger.info(f"Deleted context item: {item_id}")
                return True
        logger.warning(f"Failed to delete context item: {item_id} (not found)")
        return False
    
    def clear_storage(self) -> int:
        """Clear all context items from storage."""
        count = len(self.storage)
        self.storage = []
        logger.info(f"Cleared {count} context items from storage")
        return count
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the current context storage."""
        if not self.storage:
            return {
                "count": 0,
                "types": {},
                "sources": {},
                "oldest": None,
                "newest": None,
            }
        
        types = {}
        sources = {}
        timestamps = []
        
        for item in self.storage:
            # Count types
            if item.type in types:
                types[item.type] += 1
            else:
                types[item.type] = 1
            
            # Count sources
            if item.source in sources:
                sources[item.source] += 1
            else:
                sources[item.source] = 1
            
            # Collect timestamps
            timestamps.append(item.timestamp)
        
        return {
            "count": len(self.storage),
            "types": types,
            "sources": sources,
            "oldest": min(timestamps),
            "newest": max(timestamps),
        }

# Create a singleton instance
context_manager = ContextManager()
