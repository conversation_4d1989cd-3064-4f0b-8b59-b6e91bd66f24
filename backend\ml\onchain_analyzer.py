"""
On-Chain Metrics Analyzer

This module analyzes on-chain metrics to generate insights for trading signals.
It processes raw on-chain data and calculates trends, anomalies, and trading implications.
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('onchain_analyzer')

# Data paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'onchain')
ANALYSIS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'analysis')
os.makedirs(ANALYSIS_DIR, exist_ok=True)

class OnChainAnalyzer:
    """Analyzes on-chain metrics to generate trading insights."""
    
    def __init__(self):
        self.metrics = {
            "BTC": {},
            "ETH": {},
            "SOL": {}
        }
        self.historical_data = {
            "BTC": pd.DataFrame(),
            "ETH": pd.DataFrame(),
            "SOL": pd.DataFrame()
        }
        self.analysis_results = {
            "BTC": {},
            "ETH": {},
            "SOL": {}
        }
    
    def load_latest_metrics(self):
        """Load the latest on-chain metrics for each coin."""
        for coin in self.metrics.keys():
            latest_file = os.path.join(DATA_DIR, f"{coin.lower()}_metrics_latest.json")
            if os.path.exists(latest_file):
                try:
                    with open(latest_file, 'r') as f:
                        self.metrics[coin] = json.load(f)
                    logger.info(f"Loaded latest {coin} metrics from {latest_file}")
                except Exception as e:
                    logger.error(f"Error loading {coin} metrics: {str(e)}")
            else:
                logger.warning(f"No latest metrics file found for {coin}")
    
    def load_historical_data(self, days=30):
        """
        Load historical on-chain data for trend analysis.
        This is a simplified version - in production, you'd query a database.
        """
        # For demonstration, we'll create synthetic historical data
        # In a real implementation, you would load actual historical data
        
        for coin in self.metrics.keys():
            if not self.metrics[coin]:
                continue
                
            # Create date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # Create synthetic data based on latest metrics
            active_addresses = self.metrics[coin].get("active_addresses_24h", 0)
            tx_count = self.metrics[coin].get("transaction_count_24h", 0)
            
            # Add random variations to create historical trend
            data = {
                'date': date_range,
                'active_addresses': [
                    max(0, int(active_addresses * (0.7 + 0.6 * np.random.random())))
                    for _ in range(len(date_range))
                ],
                'transaction_count': [
                    max(0, int(tx_count * (0.7 + 0.6 * np.random.random())))
                    for _ in range(len(date_range))
                ]
            }
            
            # Create DataFrame
            self.historical_data[coin] = pd.DataFrame(data)
            
            # Sort by date
            self.historical_data[coin] = self.historical_data[coin].sort_values('date')
            
            logger.info(f"Created synthetic historical data for {coin}")
    
    def analyze_trends(self):
        """Analyze trends in on-chain metrics."""
        for coin, df in self.historical_data.items():
            if df.empty:
                continue
                
            # Calculate 7-day moving averages
            df['active_addresses_7d_ma'] = df['active_addresses'].rolling(window=7).mean()
            df['transaction_count_7d_ma'] = df['transaction_count'].rolling(window=7).mean()
            
            # Calculate 30-day moving averages
            df['active_addresses_30d_ma'] = df['active_addresses'].rolling(window=30).mean()
            df['transaction_count_30d_ma'] = df['transaction_count'].rolling(window=30).mean()
            
            # Get latest values
            latest = df.iloc[-1]
            
            # Determine trends
            active_addr_trend = self._determine_trend(
                latest['active_addresses'],
                latest['active_addresses_7d_ma'],
                latest['active_addresses_30d_ma']
            )
            
            tx_count_trend = self._determine_trend(
                latest['transaction_count'],
                latest['transaction_count_7d_ma'],
                latest['transaction_count_30d_ma']
            )
            
            # Store analysis results
            self.analysis_results[coin] = {
                "active_addresses": {
                    "current": int(latest['active_addresses']),
                    "7d_average": int(latest['active_addresses_7d_ma']),
                    "30d_average": int(latest['active_addresses_30d_ma']),
                    "trend": active_addr_trend
                },
                "transaction_count": {
                    "current": int(latest['transaction_count']),
                    "7d_average": int(latest['transaction_count_7d_ma']),
                    "30d_average": int(latest['transaction_count_30d_ma']),
                    "trend": tx_count_trend
                },
                "network_health": self._calculate_network_health(active_addr_trend, tx_count_trend)
            }
            
            logger.info(f"Completed trend analysis for {coin}")
    
    def _determine_trend(self, current, ma_7d, ma_30d):
        """
        Determine trend based on current value vs. moving averages.
        
        Returns:
            str: 'bullish', 'bearish', or 'neutral'
        """
        # Check if current value is above both MAs
        if current > ma_7d > ma_30d:
            return "bullish"
        # Check if current value is below both MAs
        elif current < ma_7d < ma_30d:
            return "bearish"
        # Check if 7d MA is above 30d MA (potential uptrend)
        elif ma_7d > ma_30d:
            return "neutral_bullish"
        # Check if 7d MA is below 30d MA (potential downtrend)
        elif ma_7d < ma_30d:
            return "neutral_bearish"
        else:
            return "neutral"
    
    def _calculate_network_health(self, active_addr_trend, tx_count_trend):
        """
        Calculate overall network health based on trends.
        
        Returns:
            dict: Network health assessment
        """
        # Score each trend
        trend_scores = {
            "bullish": 2,
            "neutral_bullish": 1,
            "neutral": 0,
            "neutral_bearish": -1,
            "bearish": -2
        }
        
        # Calculate total score
        total_score = trend_scores.get(active_addr_trend, 0) + trend_scores.get(tx_count_trend, 0)
        
        # Determine health status
        if total_score >= 3:
            status = "very_healthy"
        elif total_score > 0:
            status = "healthy"
        elif total_score == 0:
            status = "neutral"
        elif total_score > -3:
            status = "concerning"
        else:
            status = "unhealthy"
        
        return {
            "status": status,
            "score": total_score,
            "description": self._get_health_description(status)
        }
    
    def _get_health_description(self, status):
        """Get description for network health status."""
        descriptions = {
            "very_healthy": "Strong network activity with growing adoption.",
            "healthy": "Good network activity with stable usage.",
            "neutral": "Average network activity with no clear trend.",
            "concerning": "Declining network activity may indicate reduced interest.",
            "unhealthy": "Significant decline in network usage is concerning."
        }
        return descriptions.get(status, "Unknown network health status.")
    
    def get_trading_insights(self):
        """
        Generate trading insights based on on-chain analysis.
        
        Returns:
            dict: Trading insights for each coin
        """
        insights = {}
        
        for coin, analysis in self.analysis_results.items():
            if not analysis:
                continue
                
            # Determine signal strength based on network health
            health_status = analysis.get("network_health", {}).get("status", "neutral")
            health_score = analysis.get("network_health", {}).get("score", 0)
            
            # Map health status to signal strength
            signal_strength_map = {
                "very_healthy": 0.9,
                "healthy": 0.7,
                "neutral": 0.5,
                "concerning": 0.3,
                "unhealthy": 0.1
            }
            
            signal_strength = signal_strength_map.get(health_status, 0.5)
            
            # Determine signal direction
            if health_score > 0:
                signal_direction = "LONG"
            elif health_score < 0:
                signal_direction = "SHORT"
            else:
                signal_direction = "NEUTRAL"
            
            # Generate insights
            insights[coin] = {
                "signal_direction": signal_direction,
                "signal_strength": signal_strength,
                "confidence": min(0.95, 0.5 + abs(health_score) * 0.1),
                "timeframes": self._get_suitable_timeframes(health_score),
                "reasoning": [
                    f"Active addresses trend: {analysis.get('active_addresses', {}).get('trend', 'neutral')}",
                    f"Transaction count trend: {analysis.get('transaction_count', {}).get('trend', 'neutral')}",
                    f"Network health: {health_status}"
                ],
                "recommendation": self._generate_recommendation(coin, signal_direction, health_status)
            }
        
        return insights
    
    def _get_suitable_timeframes(self, health_score):
        """Determine which timeframes the signal is most relevant for."""
        timeframes = []
        
        # Strong signals are relevant for all timeframes
        if abs(health_score) >= 3:
            timeframes = ["1h", "2h", "4h", "8h"]
        # Medium signals are relevant for medium timeframes
        elif abs(health_score) >= 2:
            timeframes = ["2h", "4h", "8h"]
        # Weak signals are only relevant for longer timeframes
        else:
            timeframes = ["4h", "8h"]
        
        return timeframes
    
    def _generate_recommendation(self, coin, direction, health_status):
        """Generate a human-readable recommendation."""
        if direction == "LONG":
            if health_status in ["very_healthy", "healthy"]:
                return f"On-chain metrics show strong network adoption for {coin}, suggesting accumulation by users. Consider opening long positions with confidence."
            else:
                return f"On-chain metrics show improving network activity for {coin}, which may indicate growing interest. Consider small long positions with caution."
        elif direction == "SHORT":
            if health_status in ["unhealthy", "concerning"]:
                return f"On-chain metrics show declining network usage for {coin}, suggesting reduced user interest. Consider short positions with appropriate risk management."
            else:
                return f"On-chain metrics show some concerning signs for {coin}'s network activity. Consider small short positions or reducing exposure."
        else:
            return f"On-chain metrics for {coin} show mixed signals. Consider waiting for clearer trends before taking a position."
    
    def save_analysis(self):
        """Save analysis results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(ANALYSIS_DIR, f"onchain_analysis_{timestamp}.json")
        
        with open(filename, 'w') as f:
            json.dump(self.analysis_results, f, indent=2)
        
        # Also save to latest file
        latest_file = os.path.join(ANALYSIS_DIR, "onchain_analysis_latest.json")
        with open(latest_file, 'w') as f:
            json.dump(self.analysis_results, f, indent=2)
        
        logger.info(f"Saved analysis results to {filename}")
    
    def save_insights(self, insights):
        """Save trading insights to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(ANALYSIS_DIR, f"onchain_insights_{timestamp}.json")
        
        with open(filename, 'w') as f:
            json.dump(insights, f, indent=2)
        
        # Also save to latest file
        latest_file = os.path.join(ANALYSIS_DIR, "onchain_insights_latest.json")
        with open(latest_file, 'w') as f:
            json.dump(insights, f, indent=2)
        
        logger.info(f"Saved trading insights to {filename}")

def main():
    """Main function to analyze on-chain metrics and generate insights."""
    analyzer = OnChainAnalyzer()
    
    # Load latest metrics
    analyzer.load_latest_metrics()
    
    # Load or generate historical data
    analyzer.load_historical_data()
    
    # Analyze trends
    analyzer.analyze_trends()
    
    # Save analysis results
    analyzer.save_analysis()
    
    # Generate trading insights
    insights = analyzer.get_trading_insights()
    
    # Save insights
    analyzer.save_insights(insights)
    
    logger.info("Completed on-chain analysis and generated trading insights")
    return insights

if __name__ == "__main__":
    main()
