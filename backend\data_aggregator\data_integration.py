"""
Data Integration Module

This module integrates data from various sources for the MCP trading platform.
"""

import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class DataIntegrator:
    """Integrates data from various sources for the MCP trading platform."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the data integrator.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.data_sources = []
        self.initialize_data_sources()
        logger.info("Data integrator initialized")
    
    def initialize_data_sources(self):
        """Initialize data sources based on configuration."""
        # Initialize price data sources
        if self.config.get("use_coingecko", True):
            from backend.data_aggregator.coingecko_fetcher import CoinGeckoFetcher
            self.data_sources.append(("price", CoinGeckoFetcher()))
        
        if self.config.get("use_binance", False):
            from backend.data_aggregator.binance_fetcher import BinanceFetcher
            api_keys = self.config.get("api_keys", {}).get("binance", {})
            self.data_sources.append(("price", BinanceFetcher(api_keys)))
        
        # Initialize news data sources
        if self.config.get("use_news", True):
            from backend.data_aggregator.news_fetcher import NewsFetcher
            api_keys = self.config.get("api_keys", {}).get("news", {})
            self.data_sources.append(("news", NewsFetcher(api_keys)))
        
        # Initialize social media data sources
        if self.config.get("use_twitter", False):
            from backend.data_aggregator.twitter_fetcher import TwitterFetcher
            api_keys = self.config.get("api_keys", {}).get("twitter", {})
            self.data_sources.append(("social", TwitterFetcher(api_keys)))
        
        if self.config.get("use_reddit", False):
            from backend.data_aggregator.reddit_fetcher import RedditFetcher
            api_keys = self.config.get("api_keys", {}).get("reddit", {})
            self.data_sources.append(("social", RedditFetcher(api_keys)))
        
        # Initialize on-chain data sources (if available)
        try:
            if self.config.get("use_onchain", False):
                from backend.data_aggregator.onchain_data_fetcher import OnChainDataFetcher
                api_keys = self.config.get("api_keys", {}).get("onchain", {})
                self.data_sources.append(("onchain", OnChainDataFetcher(api_keys)))
        except ImportError:
            logger.warning("OnChainDataFetcher not available")
        
        # Initialize futures/options data sources (if available)
        try:
            if self.config.get("use_derivatives", False):
                from backend.data_aggregator.futures_options_fetcher import FuturesOptionsFetcher
                api_keys = self.config.get("api_keys", {}).get("derivatives", {})
                self.data_sources.append(("derivatives", FuturesOptionsFetcher(api_keys)))
        except ImportError:
            logger.warning("FuturesOptionsFetcher not available")
        
        # Initialize economic indicators data sources (if available)
        try:
            if self.config.get("use_economic", False):
                from backend.data_aggregator.economic_indicators import EconomicIndicatorsFetcher
                api_keys = self.config.get("api_keys", {}).get("economic", {})
                self.data_sources.append(("economic", EconomicIndicatorsFetcher(api_keys)))
        except ImportError:
            logger.warning("EconomicIndicatorsFetcher not available")
        
        logger.info(f"Initialized {len(self.data_sources)} data sources")
    
    async def fetch_all_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Fetch data from all sources.
        
        Returns:
            Dictionary with data from all sources
        """
        all_data = {}
        
        for source_type, source in self.data_sources:
            try:
                if source_type == "price":
                    data = await source.fetch_prices()
                elif source_type == "news":
                    data = await source.fetch_news()
                elif source_type == "social":
                    data = await source.fetch_posts()
                elif source_type == "onchain":
                    data = await source.fetch_all_metrics()
                elif source_type == "derivatives":
                    data = await source.fetch_all_derivatives()
                elif source_type == "economic":
                    data = await source.fetch_all_indicators()
                else:
                    logger.warning(f"Unknown source type: {source_type}")
                    continue
                
                if source_type not in all_data:
                    all_data[source_type] = []
                
                all_data[source_type].extend(data)
                logger.info(f"Fetched {len(data)} items from {source_type} source")
            
            except Exception as e:
                logger.error(f"Error fetching data from {source_type} source: {str(e)}")
        
        return all_data
    
    def to_context_items(self, all_data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Convert all data to context items for MCP.
        
        Args:
            all_data: Dictionary with data from all sources
            
        Returns:
            List of context items
        """
        context_items = []
        
        for source_type, data_list in all_data.items():
            try:
                source_obj = next((source for t, source in self.data_sources if t == source_type), None)
                
                if source_obj and hasattr(source_obj, "to_context_items"):
                    items = source_obj.to_context_items(data_list)
                    context_items.extend(items)
                    logger.info(f"Converted {len(items)} items from {source_type} to context items")
            
            except Exception as e:
                logger.error(f"Error converting {source_type} data to context items: {str(e)}")
        
        return context_items
    
    async def fetch_and_convert(self) -> List[Dict[str, Any]]:
        """
        Fetch data from all sources and convert to context items.
        
        Returns:
            List of context items
        """
        all_data = await self.fetch_all_data()
        context_items = self.to_context_items(all_data)
        
        logger.info(f"Generated {len(context_items)} context items from all sources")
        return context_items

async def main():
    """Main function for testing the data integrator."""
    # Sample configuration
    config = {
        "use_coingecko": True,
        "use_binance": False,
        "use_news": True,
        "use_twitter": False,
        "use_reddit": False,
        "use_onchain": False,
        "use_derivatives": False,
        "use_economic": False,
        "api_keys": {
            "news": {
                "newsapi": "YOUR_NEWSAPI_KEY"
            }
        }
    }
    
    integrator = DataIntegrator(config)
    
    # Fetch and convert data
    context_items = await integrator.fetch_and_convert()
    
    # Print results
    print(f"Generated {len(context_items)} context items")
    
    # Group by type
    item_types = {}
    for item in context_items:
        item_type = item.get("type", "unknown")
        if item_type not in item_types:
            item_types[item_type] = 0
        item_types[item_type] += 1
    
    print("\nContext Item Types:")
    for item_type, count in item_types.items():
        print(f"  {item_type}: {count}")
    
    # Print sample items
    if context_items:
        print("\nSample Context Item:")
        sample_item = context_items[0]
        for key, value in sample_item.items():
            if key == "content":
                print(f"  {key}: {type(value)}")
            else:
                print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
