"""
Binance Data Fetcher

This module fetches cryptocurrency trading data from the Binance API
and formats it for the MCP server.
"""

import logging
import time
import uuid
import hmac
import hashlib
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

import httpx
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
BINANCE_API_URL = "https://api.binance.com"
DEFAULT_SYMBOLS = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "XRPUSDT", "ADAUSDT", "SOLUSDT"]
REQUEST_TIMEOUT = 30  # seconds
RATE_LIMIT_DELAY = 1.0  # seconds between requests to avoid rate limiting

class BinanceTickerData(BaseModel):
    """Model for Binance ticker data."""
    symbol: str
    price: float
    volume: float
    quote_volume: float
    count: int
    bid_price: float
    ask_price: float
    timestamp: datetime

class BinanceOrderBookData(BaseModel):
    """Model for Binance order book data."""
    symbol: str
    bids: List[List[float]]  # [price, quantity]
    asks: List[List[float]]  # [price, quantity]
    timestamp: datetime

class BinanceTradeData(BaseModel):
    """Model for Binance trade data."""
    symbol: str
    id: int
    price: float
    quantity: float
    quote_quantity: float
    timestamp: datetime
    buyer_maker: bool
    best_match: bool

class ContextItem(BaseModel):
    """Model for MCP context items."""
    id: str
    type: str
    content: Dict[str, Any]
    timestamp: datetime
    source: str
    confidence: float = 1.0

class BinanceFetcher:
    """Fetches cryptocurrency data from Binance API."""
    
    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        """Initialize the fetcher with optional API credentials."""
        self.api_key = api_key
        self.api_secret = api_secret
        self.headers = {}
        if api_key:
            self.headers["X-MBX-APIKEY"] = api_key
        logger.info("Binance fetcher initialized")
    
    async def fetch_ticker_data(self, symbols: List[str] = DEFAULT_SYMBOLS) -> List[BinanceTickerData]:
        """Fetch current ticker data for specified cryptocurrency pairs."""
        url = f"{BINANCE_API_URL}/api/v3/ticker/24hr"
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                # Filter by requested symbols
                filtered_data = [item for item in data if item["symbol"] in symbols]
                
                # Convert to Pydantic models
                ticker_data = []
                for item in filtered_data:
                    ticker_data.append(BinanceTickerData(
                        symbol=item["symbol"],
                        price=float(item["lastPrice"]),
                        volume=float(item["volume"]),
                        quote_volume=float(item["quoteVolume"]),
                        count=int(item["count"]),
                        bid_price=float(item["bidPrice"]),
                        ask_price=float(item["askPrice"]),
                        timestamp=datetime.now(timezone.utc)
                    ))
                
                logger.info(f"Fetched ticker data for {len(ticker_data)} cryptocurrency pairs")
                return ticker_data
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e}")
            if e.response.status_code == 429:
                logger.warning("Rate limit exceeded, consider increasing delay")
            return []
        except httpx.RequestError as e:
            logger.error(f"Request error occurred: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return []
    
    async def fetch_order_book(self, symbol: str, limit: int = 20) -> Optional[BinanceOrderBookData]:
        """Fetch order book data for a specific cryptocurrency pair."""
        url = f"{BINANCE_API_URL}/api/v3/depth"
        params = {
            "symbol": symbol,
            "limit": limit
        }
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                order_book = BinanceOrderBookData(
                    symbol=symbol,
                    bids=[[float(price), float(qty)] for price, qty in data["bids"]],
                    asks=[[float(price), float(qty)] for price, qty in data["asks"]],
                    timestamp=datetime.now(timezone.utc)
                )
                
                logger.info(f"Fetched order book for {symbol}")
                return order_book
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e}")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error occurred: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return None
    
    async def fetch_recent_trades(self, symbol: str, limit: int = 50) -> List[BinanceTradeData]:
        """Fetch recent trades for a specific cryptocurrency pair."""
        url = f"{BINANCE_API_URL}/api/v3/trades"
        params = {
            "symbol": symbol,
            "limit": limit
        }
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                trades = []
                for item in data:
                    trade = BinanceTradeData(
                        symbol=symbol,
                        id=item["id"],
                        price=float(item["price"]),
                        quantity=float(item["qty"]),
                        quote_quantity=float(item["price"]) * float(item["qty"]),
                        timestamp=datetime.fromtimestamp(item["time"] / 1000, tz=timezone.utc),
                        buyer_maker=item["isBuyerMaker"],
                        best_match=item["isBestMatch"]
                    )
                    trades.append(trade)
                
                logger.info(f"Fetched {len(trades)} recent trades for {symbol}")
                return trades
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e}")
            return []
        except httpx.RequestError as e:
            logger.error(f"Request error occurred: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return []
    
    def ticker_to_context_items(self, ticker_data: List[BinanceTickerData]) -> List[ContextItem]:
        """Convert ticker data to MCP context items."""
        context_items = []
        
        for data in ticker_data:
            # Create a context item for each cryptocurrency pair
            item = ContextItem(
                id=f"binance_ticker_{data.symbol}_{uuid.uuid4()}",
                type="binance_ticker",
                content={
                    "symbol": data.symbol,
                    "price": data.price,
                    "volume": data.volume,
                    "quote_volume": data.quote_volume,
                    "count": data.count,
                    "bid_price": data.bid_price,
                    "ask_price": data.ask_price,
                    "spread": data.ask_price - data.bid_price,
                    "spread_percentage": (data.ask_price - data.bid_price) / data.price * 100
                },
                timestamp=data.timestamp,
                source="binance",
                confidence=1.0
            )
            context_items.append(item)
        
        return context_items
    
    def order_book_to_context_item(self, order_book: BinanceOrderBookData) -> ContextItem:
        """Convert order book data to MCP context item."""
        # Calculate order book metrics
        bid_volume = sum(qty for _, qty in order_book.bids)
        ask_volume = sum(qty for _, qty in order_book.asks)
        
        # Calculate weighted average prices
        if bid_volume > 0:
            weighted_bid = sum(price * qty for price, qty in order_book.bids) / bid_volume
        else:
            weighted_bid = 0
            
        if ask_volume > 0:
            weighted_ask = sum(price * qty for price, qty in order_book.asks) / ask_volume
        else:
            weighted_ask = 0
        
        # Create context item
        item = ContextItem(
            id=f"binance_orderbook_{order_book.symbol}_{uuid.uuid4()}",
            type="binance_orderbook",
            content={
                "symbol": order_book.symbol,
                "bids": order_book.bids,
                "asks": order_book.asks,
                "bid_volume": bid_volume,
                "ask_volume": ask_volume,
                "volume_imbalance": (bid_volume - ask_volume) / (bid_volume + ask_volume) if (bid_volume + ask_volume) > 0 else 0,
                "weighted_bid": weighted_bid,
                "weighted_ask": weighted_ask
            },
            timestamp=order_book.timestamp,
            source="binance",
            confidence=1.0
        )
        
        return item
    
    def trades_to_context_item(self, trades: List[BinanceTradeData]) -> ContextItem:
        """Convert trade data to MCP context item."""
        if not trades:
            return None
            
        symbol = trades[0].symbol
        
        # Calculate trade metrics
        buy_volume = sum(trade.quantity for trade in trades if not trade.buyer_maker)
        sell_volume = sum(trade.quantity for trade in trades if trade.buyer_maker)
        
        total_volume = buy_volume + sell_volume
        buy_percentage = buy_volume / total_volume * 100 if total_volume > 0 else 50
        
        # Calculate average price
        total_value = sum(trade.price * trade.quantity for trade in trades)
        average_price = total_value / total_volume if total_volume > 0 else 0
        
        # Create context item
        item = ContextItem(
            id=f"binance_trades_{symbol}_{uuid.uuid4()}",
            type="binance_trades",
            content={
                "symbol": symbol,
                "trade_count": len(trades),
                "buy_volume": buy_volume,
                "sell_volume": sell_volume,
                "total_volume": total_volume,
                "buy_percentage": buy_percentage,
                "average_price": average_price,
                "min_price": min(trade.price for trade in trades),
                "max_price": max(trade.price for trade in trades),
                "price_range_percentage": (max(trade.price for trade in trades) - min(trade.price for trade in trades)) / average_price * 100 if average_price > 0 else 0
            },
            timestamp=datetime.now(timezone.utc),
            source="binance",
            confidence=1.0
        )
        
        return item

async def main():
    """Main function for testing the fetcher."""
    fetcher = BinanceFetcher()
    
    # Fetch ticker data
    ticker_data = await fetcher.fetch_ticker_data()
    context_items = fetcher.ticker_to_context_items(ticker_data)
    
    # Print the first context item as an example
    if context_items:
        print(f"Example ticker context item: {context_items[0].model_dump_json(indent=2)}")
    
    # Fetch order book
    if ticker_data:
        order_book = await fetcher.fetch_order_book(ticker_data[0].symbol)
        if order_book:
            order_book_item = fetcher.order_book_to_context_item(order_book)
            print(f"Example order book context item: {order_book_item.model_dump_json(indent=2)}")
    
    # Fetch recent trades
    if ticker_data:
        trades = await fetcher.fetch_recent_trades(ticker_data[0].symbol)
        if trades:
            trades_item = fetcher.trades_to_context_item(trades)
            print(f"Example trades context item: {trades_item.model_dump_json(indent=2)}")
    
    print(f"Fetched {len(context_items)} ticker context items")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
