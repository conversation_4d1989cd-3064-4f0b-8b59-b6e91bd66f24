"""
Configuration file for API keys and other settings.

In a production environment, these would be stored in environment variables
or a secure configuration management system.
"""

# API Keys
API_KEYS = {
    # News API key (https://newsapi.org/)
    'news': None,  # Replace with your API key
    
    # Twitter API keys (https://developer.twitter.com/)
    'twitter': None,  # Replace with your API key
    
    # Reddit API keys (https://www.reddit.com/dev/api/)
    'reddit': None,  # Replace with your API key
    
    # Tally API key (https://tally.xyz/)
    'tally': None,  # Replace with your API key
}

# Exchange API settings
EXCHANGE_SETTINGS = {
    # CoinGecko API settings
    'coingecko': {
        'api_url': 'https://api.coingecko.com/api/v3',
        'api_key': None,  # CoinGecko API is free for basic usage
        'rate_limit': 10,  # Requests per minute
    },
    
    # Binance API settings
    'binance': {
        'api_url': 'https://api.binance.com',
        'api_key': None,  # Replace with your API key
        'api_secret': None,  # Replace with your API secret
    },
}

# MCP Server settings
MCP_SERVER = {
    'url': 'http://localhost:8000',
    'username': 'admin',
    'password': 'password',
}

# Scheduler settings
SCHEDULER_SETTINGS = {
    'crypto_prices_interval': 60,  # seconds
    'governance_data_interval': 3600,  # 1 hour in seconds
    'sentiment_data_interval': 1800,  # 30 minutes in seconds
}
