"""
External Services Integration

This module provides integration with external APIs for enhanced data sources and ML capabilities.
"""

import logging
import json
import asyncio
import httpx
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class ExternalServicesClient:
    """Client for integrating with external APIs and services."""
    
    def __init__(self, api_keys: Dict[str, str] = None):
        """
        Initialize the external services client.
        
        Args:
            api_keys: Dictionary of API keys for different services
        """
        self.api_keys = api_keys or {}
        logger.info("External services client initialized")
    
    async def fetch_economic_indicators(self) -> List[Dict[str, Any]]:
        """
        Fetch economic indicators from Alpha Vantage.
        
        Returns:
            List of economic indicators
        """
        if "alphavantage" not in self.api_keys:
            logger.warning("Alpha Vantage API key not provided")
            return []
        
        indicators = []
        
        try:
            # Fetch GDP data
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "REAL_GDP",
                "interval": "quarterly",
                "apikey": self.api_keys["alphavantage"]
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "data" in data:
                    latest = data["data"][0]
                    indicators.append({
                        "name": "GDP",
                        "value": float(latest["value"]),
                        "date": latest["date"],
                        "source": "alphavantage"
                    })
            
            # Fetch inflation data (CPI)
            params = {
                "function": "CPI",
                "interval": "monthly",
                "apikey": self.api_keys["alphavantage"]
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "data" in data:
                    latest = data["data"][0]
                    indicators.append({
                        "name": "CPI",
                        "value": float(latest["value"]),
                        "date": latest["date"],
                        "source": "alphavantage"
                    })
            
            # Fetch unemployment rate
            params = {
                "function": "UNEMPLOYMENT",
                "apikey": self.api_keys["alphavantage"]
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "data" in data:
                    latest = data["data"][0]
                    indicators.append({
                        "name": "Unemployment",
                        "value": float(latest["value"]),
                        "date": latest["date"],
                        "source": "alphavantage"
                    })
            
            logger.info(f"Fetched {len(indicators)} economic indicators")
            return indicators
        
        except Exception as e:
            logger.error(f"Error fetching economic indicators: {str(e)}")
            return []
    
    async def fetch_crypto_fear_greed_index(self) -> Optional[Dict[str, Any]]:
        """
        Fetch the Crypto Fear & Greed Index.
        
        Returns:
            Dictionary with Fear & Greed Index data
        """
        try:
            url = "https://api.alternative.me/fng/"
            params = {
                "limit": 1,
                "format": "json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "data" in data and len(data["data"]) > 0:
                    latest = data["data"][0]
                    return {
                        "value": int(latest["value"]),
                        "value_classification": latest["value_classification"],
                        "timestamp": latest["timestamp"],
                        "source": "alternative.me"
                    }
            
            logger.warning("No Fear & Greed Index data found")
            return None
        
        except Exception as e:
            logger.error(f"Error fetching Fear & Greed Index: {str(e)}")
            return None
    
    async def fetch_whale_alerts(self) -> List[Dict[str, Any]]:
        """
        Fetch whale transaction alerts from Whale Alert API.
        
        Returns:
            List of whale transactions
        """
        if "whalealert" not in self.api_keys:
            logger.warning("Whale Alert API key not provided")
            return []
        
        try:
            url = "https://api.whale-alert.io/v1/transactions"
            params = {
                "api_key": self.api_keys["whalealert"],
                "min_value": 5000000,  # $5M minimum transaction value
                "limit": 10
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                transactions = []
                if "transactions" in data:
                    for tx in data["transactions"]:
                        transactions.append({
                            "blockchain": tx["blockchain"],
                            "symbol": tx["symbol"],
                            "amount": tx["amount"],
                            "amount_usd": tx["amount_usd"],
                            "from_type": tx.get("from", {}).get("owner_type", "unknown"),
                            "to_type": tx.get("to", {}).get("owner_type", "unknown"),
                            "timestamp": tx["timestamp"],
                            "transaction_type": tx["transaction_type"],
                            "source": "whalealert"
                        })
                
                logger.info(f"Fetched {len(transactions)} whale transactions")
                return transactions
        
        except Exception as e:
            logger.error(f"Error fetching whale alerts: {str(e)}")
            return []
    
    async def fetch_sentiment_analysis(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Perform sentiment analysis using external NLP API.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        if "nlpcloud" not in self.api_keys:
            logger.warning("NLP Cloud API key not provided")
            return None
        
        try:
            url = "https://api.nlpcloud.io/v1/sentiment-analysis/finbert"
            headers = {
                "Authorization": f"Token {self.api_keys['nlpcloud']}",
                "Content-Type": "application/json"
            }
            data = {
                "text": text
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                response.raise_for_status()
                result = response.json()
                
                return {
                    "sentiment": result.get("sentiment", "neutral"),
                    "score": result.get("sentiment_score", 0.5),
                    "source": "nlpcloud"
                }
        
        except Exception as e:
            logger.error(f"Error performing sentiment analysis: {str(e)}")
            return None
    
    async def fetch_price_prediction(self, symbol: str, days: int = 7) -> Optional[Dict[str, Any]]:
        """
        Fetch price prediction from external ML API.
        
        Args:
            symbol: Cryptocurrency symbol
            days: Number of days to predict
            
        Returns:
            Dictionary with price prediction results
        """
        if "predictionapi" not in self.api_keys:
            logger.warning("Prediction API key not provided")
            return None
        
        try:
            url = "https://api.example.com/predict"  # Replace with actual prediction API
            headers = {
                "Authorization": f"Bearer {self.api_keys['predictionapi']}",
                "Content-Type": "application/json"
            }
            data = {
                "symbol": symbol,
                "days": days
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                response.raise_for_status()
                result = response.json()
                
                return {
                    "symbol": symbol,
                    "predictions": result.get("predictions", []),
                    "confidence": result.get("confidence", 0.0),
                    "source": "predictionapi"
                }
        
        except Exception as e:
            logger.error(f"Error fetching price prediction: {str(e)}")
            return None
    
    async def fetch_on_chain_metrics(self, blockchain: str) -> List[Dict[str, Any]]:
        """
        Fetch on-chain metrics from Glassnode API.
        
        Args:
            blockchain: Blockchain name (e.g., "bitcoin", "ethereum")
            
        Returns:
            List of on-chain metrics
        """
        if "glassnode" not in self.api_keys:
            logger.warning("Glassnode API key not provided")
            return []
        
        metrics = []
        
        try:
            base_url = "https://api.glassnode.com/v1/metrics"
            
            # Define metrics to fetch
            metric_endpoints = {
                "active_addresses": "/addresses/active_count",
                "transaction_count": "/transactions/count",
                "transaction_volume": "/transactions/volume_sum",
                "fees": "/fees/volume_sum",
                "difficulty": "/mining/difficulty_latest",
                "hash_rate": "/mining/hash_rate_mean"
            }
            
            # Map blockchain names to Glassnode asset IDs
            asset_map = {
                "bitcoin": "BTC",
                "ethereum": "ETH",
                "litecoin": "LTC"
            }
            
            asset = asset_map.get(blockchain.lower(), blockchain.upper())
            
            # Fetch each metric
            for metric_name, endpoint in metric_endpoints.items():
                url = f"{base_url}{endpoint}"
                params = {
                    "a": asset,
                    "api_key": self.api_keys["glassnode"],
                    "i": "24h",
                    "s": int(datetime.now().timestamp()) - 86400,  # Last 24 hours
                    "u": int(datetime.now().timestamp())
                }
                
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, params=params)
                    response.raise_for_status()
                    data = response.json()
                    
                    if data and len(data) > 0:
                        latest = data[-1]
                        metrics.append({
                            "name": metric_name,
                            "value": latest["v"],
                            "timestamp": datetime.fromtimestamp(latest["t"], tz=timezone.utc).isoformat(),
                            "blockchain": blockchain,
                            "source": "glassnode"
                        })
            
            logger.info(f"Fetched {len(metrics)} on-chain metrics for {blockchain}")
            return metrics
        
        except Exception as e:
            logger.error(f"Error fetching on-chain metrics: {str(e)}")
            return []
    
    async def fetch_futures_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Fetch futures market data from Binance API.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
            
        Returns:
            Dictionary with futures market data
        """
        try:
            url = "https://fapi.binance.com/fapi/v1/premiumIndex"
            params = {
                "symbol": symbol
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                premium_index = response.json()
                
                # Fetch funding rate
                funding_url = "https://fapi.binance.com/fapi/v1/fundingRate"
                funding_params = {
                    "symbol": symbol,
                    "limit": 1
                }
                
                funding_response = await client.get(funding_url, params=funding_params)
                funding_response.raise_for_status()
                funding_data = funding_response.json()
                
                # Fetch open interest
                oi_url = "https://fapi.binance.com/fapi/v1/openInterest"
                oi_params = {
                    "symbol": symbol
                }
                
                oi_response = await client.get(oi_url, params=oi_params)
                oi_response.raise_for_status()
                oi_data = oi_response.json()
                
                return {
                    "symbol": symbol,
                    "mark_price": float(premium_index["markPrice"]),
                    "index_price": float(premium_index["indexPrice"]),
                    "funding_rate": float(funding_data[0]["fundingRate"]) if funding_data else 0,
                    "next_funding_time": funding_data[0]["fundingTime"] if funding_data else 0,
                    "open_interest": float(oi_data["openInterest"]),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "binance"
                }
        
        except Exception as e:
            logger.error(f"Error fetching futures data: {str(e)}")
            return None
    
    def to_context_items(self, data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Convert data to context items for MCP.
        
        Args:
            data: Dictionary with data from various sources
            
        Returns:
            List of context items
        """
        context_items = []
        
        # Process economic indicators
        for indicator in data.get("economic_indicators", []):
            context_items.append({
                "id": f"economic_{indicator['name']}_{indicator['date']}",
                "type": "economic_indicator",
                "content": indicator,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": indicator.get("source", "unknown"),
                "confidence": 1.0
            })
        
        # Process fear & greed index
        if "fear_greed" in data and data["fear_greed"]:
            context_items.append({
                "id": f"fear_greed_{data['fear_greed']['timestamp']}",
                "type": "market_sentiment",
                "content": data["fear_greed"],
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": data["fear_greed"].get("source", "unknown"),
                "confidence": 1.0
            })
        
        # Process whale alerts
        for alert in data.get("whale_alerts", []):
            context_items.append({
                "id": f"whale_{alert['blockchain']}_{alert['timestamp']}",
                "type": "whale_transaction",
                "content": alert,
                "timestamp": datetime.fromtimestamp(alert["timestamp"], tz=timezone.utc).isoformat(),
                "source": alert.get("source", "unknown"),
                "confidence": 1.0
            })
        
        # Process sentiment analysis
        if "sentiment" in data and data["sentiment"]:
            context_items.append({
                "id": f"sentiment_{datetime.now().timestamp()}",
                "type": "text_sentiment",
                "content": data["sentiment"],
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": data["sentiment"].get("source", "unknown"),
                "confidence": float(data["sentiment"].get("score", 0.5))
            })
        
        # Process price predictions
        if "price_prediction" in data and data["price_prediction"]:
            context_items.append({
                "id": f"prediction_{data['price_prediction']['symbol']}_{datetime.now().timestamp()}",
                "type": "price_prediction",
                "content": data["price_prediction"],
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": data["price_prediction"].get("source", "unknown"),
                "confidence": float(data["price_prediction"].get("confidence", 0.5))
            })
        
        # Process on-chain metrics
        for metric in data.get("on_chain_metrics", []):
            context_items.append({
                "id": f"onchain_{metric['blockchain']}_{metric['name']}_{metric['timestamp']}",
                "type": "on_chain_metric",
                "content": metric,
                "timestamp": metric.get("timestamp", datetime.now(timezone.utc).isoformat()),
                "source": metric.get("source", "unknown"),
                "confidence": 1.0
            })
        
        # Process futures data
        if "futures_data" in data and data["futures_data"]:
            context_items.append({
                "id": f"futures_{data['futures_data']['symbol']}_{data['futures_data']['timestamp']}",
                "type": "futures_data",
                "content": data["futures_data"],
                "timestamp": data["futures_data"].get("timestamp", datetime.now(timezone.utc).isoformat()),
                "source": data["futures_data"].get("source", "unknown"),
                "confidence": 1.0
            })
        
        return context_items

async def main():
    """Main function for testing the external services client."""
    # Sample API keys (replace with actual keys in production)
    api_keys = {
        "alphavantage": "YOUR_ALPHAVANTAGE_API_KEY",
        "whalealert": "YOUR_WHALEALERT_API_KEY",
        "nlpcloud": "YOUR_NLPCLOUD_API_KEY",
        "predictionapi": "YOUR_PREDICTION_API_KEY",
        "glassnode": "YOUR_GLASSNODE_API_KEY"
    }
    
    client = ExternalServicesClient(api_keys)
    
    # Fetch data from various sources
    data = {}
    
    # Fetch Fear & Greed Index
    data["fear_greed"] = await client.fetch_crypto_fear_greed_index()
    
    # Convert to context items
    context_items = client.to_context_items(data)
    
    # Print results
    print(f"Generated {len(context_items)} context items")
    
    if context_items:
        print("\nSample Context Item:")
        print(json.dumps(context_items[0], indent=2))

if __name__ == "__main__":
    asyncio.run(main())
