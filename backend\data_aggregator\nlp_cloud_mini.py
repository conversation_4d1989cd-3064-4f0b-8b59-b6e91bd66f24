"""
NLP Cloud Sentiment Analyzer (Mini Version)

This module fetches and analyzes sentiment from news and social media using NLP Cloud.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# NLP Cloud API credentials
NLP_CLOUD_API_KEY = "a95f288fdcaee11608cdf4de5fe5f46f657da731"

class NLPCloudSentimentAnalyzer:
    """Analyzes sentiment from text using NLP Cloud."""

    def __init__(self, api_key: str = None):
        """Initialize the NLP Cloud sentiment analyzer."""
        self.api_key = api_key or NLP_CLOUD_API_KEY
        self.base_url = "https://api.nlpcloud.io/v1"
        self.headers = {
            "Authorization": f"Token {self.api_key}",
            "Content-Type": "application/json"
        }
        logger.info("NLP Cloud sentiment analyzer initialized")

    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of a text.

        Args:
            text: Text to analyze

        Returns:
            Dictionary with sentiment analysis results
        """
        url = f"{self.base_url}/bart-large-mnli/classification"
        payload = {
            "text": text,
            "labels": ["positive", "negative", "neutral"]
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, json=payload, headers=self.headers)
                response.raise_for_status()
                data = response.json()

                # Extract sentiment scores
                scores = data.get("scores", [])
                labels = data.get("labels", [])

                if not scores or not labels:
                    raise ValueError("No scores or labels returned from API")

                # Find the highest scoring sentiment
                max_score_index = scores.index(max(scores))
                sentiment_label = labels[max_score_index]
                score = scores[max_score_index]

                # Map sentiment label to positive/negative/neutral
                if sentiment_label == "positive":
                    sentiment_type = "positive"
                elif sentiment_label == "negative":
                    sentiment_type = "negative"
                else:
                    sentiment_type = "neutral"

                # Create sentiment result
                sentiment = {
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "sentiment": sentiment_type,
                    "score": score,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "nlp_cloud"
                }

                logger.info(f"Analyzed sentiment: {sentiment['sentiment']} (score: {sentiment['score']:.2f})")
                return sentiment

        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")

            # Fallback to keyword-based sentiment analysis
            positive_keywords = ["bullish", "surge", "grow", "positive", "increase", "improve", "gain", "record", "high", "up"]
            negative_keywords = ["bearish", "drop", "fall", "negative", "decrease", "decline", "loss", "low", "down", "resistance"]

            # Count positive and negative keywords
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_keywords if word in text_lower)
            negative_count = sum(1 for word in negative_keywords if word in text_lower)

            # Determine sentiment
            if positive_count > negative_count:
                sentiment_label = "positive"
                score = min(0.5 + (positive_count - negative_count) * 0.1, 0.9)
            elif negative_count > positive_count:
                sentiment_label = "negative"
                score = min(0.5 + (negative_count - positive_count) * 0.1, 0.9)
            else:
                sentiment_label = "neutral"
                score = 0.5

            # Create sentiment result
            return {
                "text": text[:100] + "..." if len(text) > 100 else text,
                "sentiment": sentiment_label,
                "score": score,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "nlp_cloud_fallback"
            }

    async def analyze_crypto_news(self, crypto_symbol: str) -> List[Dict[str, Any]]:
        """
        Analyze sentiment of news about a cryptocurrency.

        Args:
            crypto_symbol: Cryptocurrency symbol (e.g., "BTC")

        Returns:
            List of sentiment analysis results
        """
        # Sample news headlines for testing
        # In a real implementation, these would come from a news API
        sample_news = {
            "BTC": [
                f"{crypto_symbol} price surges to new all-time high as institutional adoption grows",
                f"{crypto_symbol} faces resistance at key level as traders take profits",
                f"Analysts predict bullish outlook for {crypto_symbol} in coming months"
            ],
            "ETH": [
                f"{crypto_symbol} upgrade improves scalability and reduces gas fees",
                f"{crypto_symbol} developer activity reaches record levels",
                f"DeFi growth continues to drive {crypto_symbol} demand"
            ],
            "SOL": [
                f"{crypto_symbol} network experiences downtime due to congestion",
                f"{crypto_symbol} ecosystem expands with new DeFi and NFT projects",
                f"Institutional investors show increased interest in {crypto_symbol}"
            ]
        }

        # Get news for the specified crypto
        news = sample_news.get(crypto_symbol, [
            f"Market sentiment mixed on {crypto_symbol} as traders await regulatory clarity",
            f"{crypto_symbol} shows strong technical indicators despite market volatility",
            f"New developments in {crypto_symbol} ecosystem attract investor attention"
        ])

        # Analyze sentiment for each news headline with delay to avoid rate limiting
        results = []
        for headline in news:
            # Add delay between requests to avoid rate limiting
            await asyncio.sleep(3.0)
            result = await self.analyze_sentiment(headline)
            results.append(result)

        logger.info(f"Analyzed sentiment for {len(results)} {crypto_symbol} news items")
        return results

    def calculate_news_sentiment(self, sentiment_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate overall sentiment from multiple news items.

        Args:
            sentiment_results: List of sentiment analysis results

        Returns:
            Dictionary with overall sentiment
        """
        if not sentiment_results:
            return {
                "overall_sentiment": "neutral",
                "confidence": 0.5,
                "positive_count": 0,
                "negative_count": 0,
                "neutral_count": 0
            }

        # Count sentiments
        positive_count = sum(1 for item in sentiment_results if item.get("sentiment") == "positive")
        negative_count = sum(1 for item in sentiment_results if item.get("sentiment") == "negative")
        neutral_count = len(sentiment_results) - positive_count - negative_count

        # Calculate average score
        total_score = sum(item.get("score", 0) for item in sentiment_results)
        avg_score = total_score / len(sentiment_results)

        # Determine overall sentiment
        if positive_count > negative_count:
            overall_sentiment = "bullish"
            confidence = 0.5 + (positive_count - negative_count) / (2 * len(sentiment_results))
        elif negative_count > positive_count:
            overall_sentiment = "bearish"
            confidence = 0.5 + (negative_count - positive_count) / (2 * len(sentiment_results))
        else:
            overall_sentiment = "neutral"
            confidence = 0.5

        return {
            "overall_sentiment": overall_sentiment,
            "confidence": min(confidence, 0.9),  # Cap confidence at 0.9
            "positive_count": positive_count,
            "negative_count": negative_count,
            "neutral_count": neutral_count,
            "average_score": avg_score
        }

    def to_context_items(self, crypto_symbol: str, sentiment_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Convert sentiment results to context items for MCP.

        Args:
            crypto_symbol: Cryptocurrency symbol
            sentiment_results: List of sentiment analysis results

        Returns:
            List of context items
        """
        context_items = []

        # Add individual news sentiment items
        for result in sentiment_results:
            item = {
                "id": f"news_sentiment_{crypto_symbol}_{uuid.uuid4()}",
                "type": "news_sentiment",
                "content": result,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "nlp_cloud",
                "confidence": abs(result.get("score", 0))
            }
            context_items.append(item)

        # Add overall sentiment
        overall = self.calculate_news_sentiment(sentiment_results)
        overall_item = {
            "id": f"overall_news_sentiment_{crypto_symbol}_{uuid.uuid4()}",
            "type": "overall_news_sentiment",
            "content": {
                "crypto_symbol": crypto_symbol,
                "overall_sentiment": overall["overall_sentiment"],
                "confidence": overall["confidence"],
                "positive_count": overall["positive_count"],
                "negative_count": overall["negative_count"],
                "neutral_count": overall["neutral_count"],
                "average_score": overall["average_score"],
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "nlp_cloud",
            "confidence": overall["confidence"]
        }
        context_items.append(overall_item)

        return context_items

async def main():
    """Main function for testing the NLP Cloud sentiment analyzer."""
    analyzer = NLPCloudSentimentAnalyzer()

    # Test with a single text
    text = "Bitcoin's price action remains bullish as institutional adoption continues to grow."
    sentiment = await analyzer.analyze_sentiment(text)
    print(f"Sentiment for text: {sentiment['sentiment']} (score: {sentiment['score']:.2f})")

    # Test with crypto news
    for symbol in ["BTC", "ETH", "SOL"]:
        print(f"\nAnalyzing {symbol} news sentiment...")
        news_sentiment = await analyzer.analyze_crypto_news(symbol)

        for item in news_sentiment:
            print(f"  - {item['text']}: {item['sentiment']} (score: {item['score']:.2f})")

        overall = analyzer.calculate_news_sentiment(news_sentiment)
        print(f"\nOverall {symbol} sentiment: {overall['overall_sentiment']} (confidence: {overall['confidence']:.2f})")
        print(f"Positive: {overall['positive_count']}, Negative: {overall['negative_count']}, Neutral: {overall['neutral_count']}")

        # Convert to context items
        context_items = analyzer.to_context_items(symbol, news_sentiment)
        print(f"Generated {len(context_items)} context items for {symbol}")

if __name__ == "__main__":
    asyncio.run(main())
