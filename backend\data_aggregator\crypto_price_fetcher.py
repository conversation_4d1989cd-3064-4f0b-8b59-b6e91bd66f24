"""
Cryptocurrency Price Fetcher

This module fetches cryptocurrency price data from the CoinGecko API
and formats it for the MCP server.
"""

import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

import httpx
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"
DEFAULT_CURRENCY = "usd"
DEFAULT_COINS = ["bitcoin", "ethereum", "binancecoin", "ripple", "cardano", "solana"]
REQUEST_TIMEOUT = 30  # seconds
RATE_LIMIT_DELAY = 1.5  # seconds between requests to avoid rate limiting

class CryptoPriceData(BaseModel):
    """Model for cryptocurrency price data."""
    id: str
    symbol: str
    name: str
    current_price: float
    market_cap: float
    market_cap_rank: int
    total_volume: float
    high_24h: float
    low_24h: float
    price_change_24h: float
    price_change_percentage_24h: float
    market_cap_change_24h: float
    market_cap_change_percentage_24h: float
    circulating_supply: float
    total_supply: Optional[float] = None
    max_supply: Optional[float] = None
    last_updated: datetime

class ContextItem(BaseModel):
    """Model for MCP context items."""
    id: str
    type: str
    content: Dict[str, Any]
    timestamp: datetime
    source: str
    confidence: float = 1.0

class CoinGeckoFetcher:
    """Fetches cryptocurrency data from CoinGecko API."""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the fetcher with optional API key."""
        self.api_key = api_key
        self.headers = {}
        if api_key:
            self.headers["X-CoinGecko-Api-Key"] = api_key
        logger.info("CoinGecko fetcher initialized")

    async def fetch_prices(
        self,
        coin_ids: List[str] = DEFAULT_COINS,
        currency: str = DEFAULT_CURRENCY
    ) -> List[CryptoPriceData]:
        """Fetch current prices for specified cryptocurrencies."""
        url = f"{COINGECKO_API_URL}/coins/markets"
        params = {
            "vs_currency": currency,
            "ids": ",".join(coin_ids),
            "order": "market_cap_desc",
            "per_page": 100,
            "page": 1,
            "sparkline": False,
            "price_change_percentage": "24h"
        }

        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                data = response.json()

                # Convert to Pydantic models
                price_data = []
                for item in data:
                    # Convert string timestamps to datetime objects
                    if "last_updated" in item and item["last_updated"]:
                        item["last_updated"] = datetime.fromisoformat(item["last_updated"].replace("Z", "+00:00"))

                    price_data.append(CryptoPriceData(**item))

                logger.info(f"Fetched prices for {len(price_data)} cryptocurrencies")
                return price_data

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e}")
            if e.response.status_code == 429:
                logger.warning("Rate limit exceeded, consider using an API key or increasing delay")
            return []
        except httpx.RequestError as e:
            logger.error(f"Request error occurred: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return []

    def to_context_items(self, price_data: List[CryptoPriceData]) -> List[ContextItem]:
        """Convert price data to MCP context items."""
        context_items = []

        for data in price_data:
            # Create a context item for each cryptocurrency
            item = ContextItem(
                id=f"crypto_price_{data.id}_{uuid.uuid4()}",
                type="crypto_price",
                content={
                    "coin_id": data.id,
                    "symbol": data.symbol,
                    "name": data.name,
                    "price_usd": data.current_price,
                    "market_cap": data.market_cap,
                    "market_cap_rank": data.market_cap_rank,
                    "volume_24h": data.total_volume,
                    "high_24h": data.high_24h,
                    "low_24h": data.low_24h,
                    "price_change_24h": data.price_change_24h,
                    "price_change_percentage_24h": data.price_change_percentage_24h,
                    "market_cap_change_24h": data.market_cap_change_24h,
                    "market_cap_change_percentage_24h": data.market_cap_change_percentage_24h,
                    "circulating_supply": data.circulating_supply,
                    "total_supply": data.total_supply,
                    "max_supply": data.max_supply,
                },
                timestamp=data.last_updated,
                source="coingecko",
                confidence=1.0
            )
            context_items.append(item)

        return context_items

async def main():
    """Main function for testing the fetcher."""
    fetcher = CoinGeckoFetcher()
    price_data = await fetcher.fetch_prices()
    context_items = fetcher.to_context_items(price_data)

    # Print the first context item as an example
    if context_items:
        print(f"Example context item: {context_items[0].model_dump_json(indent=2)}")

    print(f"Fetched {len(context_items)} context items")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
