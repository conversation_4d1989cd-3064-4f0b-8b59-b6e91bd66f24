"""
On-Chain Data Fetcher

This module fetches on-chain data from various blockchain networks to provide
insights into network activity, whale movements, and other blockchain metrics.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

import httpx

from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# API endpoints
ETHERSCAN_API_URL = "https://api.etherscan.io/api"
BLOCKCHAIR_API_URL = "https://api.blockchair.com"
GLASSNODE_API_URL = "https://api.glassnode.com/v1"

# Default assets to track
DEFAULT_ASSETS = ["bitcoin", "ethereum", "solana", "binancecoin", "ripple"]

class OnChainMetric(BaseModel):
    """Model for on-chain metrics."""
    asset_id: str
    asset_symbol: str
    metric_type: str  # e.g., "active_addresses", "transaction_count", "whale_movement"
    value: float
    timestamp: datetime
    timeframe: str  # e.g., "1h", "24h", "7d"
    source: str
    additional_data: Optional[Dict[str, Any]] = None

class OnChainDataFetcher:
    """Fetches on-chain data from various blockchain networks."""

    def __init__(self, api_keys: Dict[str, str] = None):
        """
        Initialize the on-chain data fetcher.
        
        Args:
            api_keys: Dictionary of API keys for different services
        """
        self.api_keys = api_keys or {}
        self.tracked_assets = DEFAULT_ASSETS
        logger.info("On-chain data fetcher initialized")

    async def fetch_active_addresses(self, asset_id: str) -> Optional[OnChainMetric]:
        """
        Fetch active addresses count for a specific asset.
        
        Args:
            asset_id: Asset identifier (e.g., "bitcoin", "ethereum")
            
        Returns:
            OnChainMetric object with active addresses data
        """
        if asset_id == "ethereum":
            return await self._fetch_ethereum_active_addresses()
        elif asset_id == "bitcoin":
            return await self._fetch_bitcoin_active_addresses()
        else:
            logger.warning(f"Active addresses fetching not implemented for {asset_id}")
            return None

    async def _fetch_ethereum_active_addresses(self) -> Optional[OnChainMetric]:
        """Fetch active addresses count for Ethereum."""
        if "etherscan" not in self.api_keys:
            logger.warning("Etherscan API key not provided")
            return None

        try:
            url = f"{ETHERSCAN_API_URL}"
            params = {
                "module": "stats",
                "action": "dailynetutilization",
                "apikey": self.api_keys["etherscan"],
                "startdate": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
                "enddate": datetime.now().strftime("%Y-%m-%d")
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if data["status"] == "1" and data["message"] == "OK":
                    result = data["result"]
                    if result:
                        latest = result[-1]
                        return OnChainMetric(
                            asset_id="ethereum",
                            asset_symbol="ETH",
                            metric_type="active_addresses",
                            value=float(latest["activeAddresses"]),
                            timestamp=datetime.now(timezone.utc),
                            timeframe="24h",
                            source="etherscan",
                            additional_data={
                                "transaction_count": float(latest["transactionCount"]),
                                "gas_used": float(latest["gasUsed"])
                            }
                        )

            logger.warning("No active addresses data found for Ethereum")
            return None

        except Exception as e:
            logger.error(f"Error fetching Ethereum active addresses: {str(e)}")
            return None

    async def _fetch_bitcoin_active_addresses(self) -> Optional[OnChainMetric]:
        """Fetch active addresses count for Bitcoin."""
        if "glassnode" not in self.api_keys:
            logger.warning("Glassnode API key not provided")
            return None

        try:
            url = f"{GLASSNODE_API_URL}/metrics/addresses/active_count"
            params = {
                "api_key": self.api_keys["glassnode"],
                "a": "BTC",
                "i": "24h"
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if data and len(data) > 0:
                    latest = data[-1]
                    return OnChainMetric(
                        asset_id="bitcoin",
                        asset_symbol="BTC",
                        metric_type="active_addresses",
                        value=float(latest["v"]),
                        timestamp=datetime.fromtimestamp(latest["t"], tz=timezone.utc),
                        timeframe="24h",
                        source="glassnode"
                    )

            logger.warning("No active addresses data found for Bitcoin")
            return None

        except Exception as e:
            logger.error(f"Error fetching Bitcoin active addresses: {str(e)}")
            return None

    async def fetch_whale_movements(self, asset_id: str) -> List[OnChainMetric]:
        """
        Fetch whale movement data for a specific asset.
        
        Args:
            asset_id: Asset identifier (e.g., "bitcoin", "ethereum")
            
        Returns:
            List of OnChainMetric objects with whale movement data
        """
        if asset_id == "ethereum":
            return await self._fetch_ethereum_whale_movements()
        elif asset_id == "bitcoin":
            return await self._fetch_bitcoin_whale_movements()
        else:
            logger.warning(f"Whale movement fetching not implemented for {asset_id}")
            return []

    async def _fetch_ethereum_whale_movements(self) -> List[OnChainMetric]:
        """Fetch whale movement data for Ethereum."""
        if "etherscan" not in self.api_keys:
            logger.warning("Etherscan API key not provided")
            return []

        try:
            url = f"{ETHERSCAN_API_URL}"
            params = {
                "module": "account",
                "action": "txlist",
                "apikey": self.api_keys["etherscan"],
                "startblock": 0,
                "endblock": ********,
                "page": 1,
                "offset": 10,
                "sort": "desc"
            }

            # Define whale threshold (in ETH)
            whale_threshold = 1000

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                whale_movements = []
                if data["status"] == "1" and data["message"] == "OK":
                    for tx in data["result"]:
                        value_eth = float(tx["value"]) / 1e18
                        if value_eth >= whale_threshold:
                            whale_movements.append(OnChainMetric(
                                asset_id="ethereum",
                                asset_symbol="ETH",
                                metric_type="whale_movement",
                                value=value_eth,
                                timestamp=datetime.fromtimestamp(int(tx["timeStamp"]), tz=timezone.utc),
                                timeframe="transaction",
                                source="etherscan",
                                additional_data={
                                    "from": tx["from"],
                                    "to": tx["to"],
                                    "hash": tx["hash"]
                                }
                            ))

                return whale_movements

        except Exception as e:
            logger.error(f"Error fetching Ethereum whale movements: {str(e)}")
            return []

    async def _fetch_bitcoin_whale_movements(self) -> List[OnChainMetric]:
        """Fetch whale movement data for Bitcoin."""
        if "blockchair" not in self.api_keys:
            logger.warning("Blockchair API key not provided")
            return []

        try:
            url = f"{BLOCKCHAIR_API_URL}/bitcoin/transactions"
            params = {
                "key": self.api_keys["blockchair"],
                "limit": 10,
                "order": "value_usd(desc)"
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                whale_movements = []
                if "data" in data:
                    for tx_hash, tx in data["data"].items():
                        whale_movements.append(OnChainMetric(
                            asset_id="bitcoin",
                            asset_symbol="BTC",
                            metric_type="whale_movement",
                            value=tx["value"],
                            timestamp=datetime.fromtimestamp(tx["time"], tz=timezone.utc),
                            timeframe="transaction",
                            source="blockchair",
                            additional_data={
                                "hash": tx_hash,
                                "value_usd": tx["value_usd"],
                                "fee": tx["fee"]
                            }
                        ))

                return whale_movements

        except Exception as e:
            logger.error(f"Error fetching Bitcoin whale movements: {str(e)}")
            return []

    async def fetch_network_metrics(self, asset_id: str) -> List[OnChainMetric]:
        """
        Fetch network metrics for a specific asset.
        
        Args:
            asset_id: Asset identifier (e.g., "bitcoin", "ethereum")
            
        Returns:
            List of OnChainMetric objects with network metrics
        """
        if asset_id == "ethereum":
            return await self._fetch_ethereum_network_metrics()
        elif asset_id == "bitcoin":
            return await self._fetch_bitcoin_network_metrics()
        else:
            logger.warning(f"Network metrics fetching not implemented for {asset_id}")
            return []

    async def _fetch_ethereum_network_metrics(self) -> List[OnChainMetric]:
        """Fetch network metrics for Ethereum."""
        if "etherscan" not in self.api_keys:
            logger.warning("Etherscan API key not provided")
            return []

        try:
            metrics = []
            
            # Fetch gas price
            url = f"{ETHERSCAN_API_URL}"
            params = {
                "module": "gastracker",
                "action": "gasoracle",
                "apikey": self.api_keys["etherscan"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if data["status"] == "1" and data["message"] == "OK":
                    result = data["result"]
                    metrics.append(OnChainMetric(
                        asset_id="ethereum",
                        asset_symbol="ETH",
                        metric_type="gas_price",
                        value=float(result["SafeGasPrice"]),
                        timestamp=datetime.now(timezone.utc),
                        timeframe="current",
                        source="etherscan",
                        additional_data={
                            "safe_gas_price": float(result["SafeGasPrice"]),
                            "propose_gas_price": float(result["ProposeGasPrice"]),
                            "fast_gas_price": float(result["FastGasPrice"])
                        }
                    ))
            
            # Fetch network hashrate
            params = {
                "module": "stats",
                "action": "hashrate",
                "apikey": self.api_keys["etherscan"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if data["status"] == "1" and data["message"] == "OK":
                    result = data["result"]
                    metrics.append(OnChainMetric(
                        asset_id="ethereum",
                        asset_symbol="ETH",
                        metric_type="hashrate",
                        value=float(result["hashrate"]),
                        timestamp=datetime.now(timezone.utc),
                        timeframe="current",
                        source="etherscan"
                    ))

            return metrics

        except Exception as e:
            logger.error(f"Error fetching Ethereum network metrics: {str(e)}")
            return []

    async def _fetch_bitcoin_network_metrics(self) -> List[OnChainMetric]:
        """Fetch network metrics for Bitcoin."""
        if "blockchair" not in self.api_keys:
            logger.warning("Blockchair API key not provided")
            return []

        try:
            url = f"{BLOCKCHAIR_API_URL}/bitcoin/stats"
            params = {
                "key": self.api_keys["blockchair"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                metrics = []
                if "data" in data:
                    stats = data["data"]
                    
                    # Hashrate
                    metrics.append(OnChainMetric(
                        asset_id="bitcoin",
                        asset_symbol="BTC",
                        metric_type="hashrate",
                        value=stats["hashrate_24h"],
                        timestamp=datetime.now(timezone.utc),
                        timeframe="24h",
                        source="blockchair"
                    ))
                    
                    # Difficulty
                    metrics.append(OnChainMetric(
                        asset_id="bitcoin",
                        asset_symbol="BTC",
                        metric_type="difficulty",
                        value=stats["difficulty"],
                        timestamp=datetime.now(timezone.utc),
                        timeframe="current",
                        source="blockchair"
                    ))
                    
                    # Mempool size
                    metrics.append(OnChainMetric(
                        asset_id="bitcoin",
                        asset_symbol="BTC",
                        metric_type="mempool_size",
                        value=stats["mempool_transactions"],
                        timestamp=datetime.now(timezone.utc),
                        timeframe="current",
                        source="blockchair"
                    ))

                return metrics

        except Exception as e:
            logger.error(f"Error fetching Bitcoin network metrics: {str(e)}")
            return []

    async def fetch_all_metrics(self, assets: List[str] = None) -> List[OnChainMetric]:
        """
        Fetch all on-chain metrics for specified assets.
        
        Args:
            assets: List of asset identifiers
            
        Returns:
            List of OnChainMetric objects
        """
        assets = assets or self.tracked_assets
        all_metrics = []
        
        for asset in assets:
            # Fetch active addresses
            active_addresses = await self.fetch_active_addresses(asset)
            if active_addresses:
                all_metrics.append(active_addresses)
            
            # Fetch whale movements
            whale_movements = await self.fetch_whale_movements(asset)
            all_metrics.extend(whale_movements)
            
            # Fetch network metrics
            network_metrics = await self.fetch_network_metrics(asset)
            all_metrics.extend(network_metrics)
        
        logger.info(f"Fetched {len(all_metrics)} on-chain metrics for {len(assets)} assets")
        return all_metrics

    def to_context_items(self, metrics: List[OnChainMetric]) -> List[Dict[str, Any]]:
        """
        Convert on-chain metrics to context items for MCP.
        
        Args:
            metrics: List of OnChainMetric objects
            
        Returns:
            List of context items
        """
        context_items = []
        
        for metric in metrics:
            context_item = {
                "id": f"onchain_{metric.asset_id}_{metric.metric_type}_{uuid.uuid4()}",
                "type": "onchain_metric",
                "content": {
                    "asset_id": metric.asset_id,
                    "asset_symbol": metric.asset_symbol,
                    "metric_type": metric.metric_type,
                    "value": metric.value,
                    "timeframe": metric.timeframe
                },
                "timestamp": metric.timestamp.isoformat(),
                "source": metric.source,
                "confidence": 1.0
            }
            
            if metric.additional_data:
                context_item["content"].update(metric.additional_data)
            
            context_items.append(context_item)
        
        return context_items

async def main():
    """Main function for testing the on-chain data fetcher."""
    # Sample API keys (replace with actual keys in production)
    api_keys = {
        "etherscan": "YOUR_ETHERSCAN_API_KEY",
        "blockchair": "YOUR_BLOCKCHAIR_API_KEY",
        "glassnode": "YOUR_GLASSNODE_API_KEY"
    }
    
    fetcher = OnChainDataFetcher(api_keys)
    
    # Fetch metrics for Bitcoin and Ethereum
    metrics = await fetcher.fetch_all_metrics(["bitcoin", "ethereum"])
    
    # Convert to context items
    context_items = fetcher.to_context_items(metrics)
    
    # Print results
    print(f"Fetched {len(metrics)} on-chain metrics")
    print(f"Generated {len(context_items)} context items")
    
    if context_items:
        print("\nSample context item:")
        print(context_items[0])

if __name__ == "__main__":
    asyncio.run(main())
