"""
Cryptocurrency Data Fetcher for Project Ruby.

This module handles fetching and processing cryptocurrency data from various APIs.
"""

import os
import requests
import pandas as pd
import numpy as np
import json
import time
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CryptoDataFetcher:
    """Fetches cryptocurrency data from various APIs."""
    
    def __init__(self):
        """Initialize the data fetcher with API keys."""
        # Load API keys from environment variables or use provided keys
        self.cryptocompare_api_key = os.environ.get(
            'CRYPTOCOMPARE_API_KEY', 
            '****************************************************************'
        )
        self.coingecko_api_key = os.environ.get(
            'COINGECKO_API_KEY',
            'CG-Ag17QEEjKxkZAo4yFr66pE9L'
        )
        
        # Base URLs for APIs
        self.cryptocompare_base_url = "https://min-api.cryptocompare.com/data"
        self.coingecko_base_url = "https://api.coingecko.com/api/v3"
        
        # Create data directory if it doesn't exist
        self.data_dir = os.path.join('frontend', 'public', 'data')
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Cache for storing fetched data
        self.data_cache = {}
        self.cache_expiry = {}
        
    def fetch_historical_data(self, symbol, timeframe='day', limit=100, force_refresh=False):
        """
        Fetch historical price data for a cryptocurrency.
        
        Args:
            symbol: Cryptocurrency symbol (e.g., BTC)
            timeframe: Time interval (minute, hour, day)
            limit: Number of data points to fetch
            force_refresh: Whether to force a refresh of cached data
            
        Returns:
            DataFrame with historical price data
        """
        cache_key = f"{symbol}_{timeframe}_{limit}"
        current_time = datetime.now()
        
        # Check if we have cached data that's still valid
        if not force_refresh and cache_key in self.cache_expiry:
            if current_time < self.cache_expiry[cache_key] and cache_key in self.data_cache:
                logger.info(f"Using cached data for {cache_key}")
                return self.data_cache[cache_key]
        
        try:
            # Try CryptoCompare first
            df = self._fetch_from_cryptocompare(symbol, timeframe, limit)
            
            # If that fails, try CoinGecko
            if df is None or df.empty:
                df = self._fetch_from_coingecko(symbol, timeframe, limit)
                
            # If we got data, cache it
            if df is not None and not df.empty:
                self.data_cache[cache_key] = df
                
                # Set cache expiry based on timeframe
                if timeframe == 'minute':
                    expiry = current_time + timedelta(minutes=5)
                elif timeframe == 'hour':
                    expiry = current_time + timedelta(hours=1)
                else:  # day
                    expiry = current_time + timedelta(hours=6)
                    
                self.cache_expiry[cache_key] = expiry
                
                # Save to disk as backup
                self._save_to_disk(df, f"{symbol}_{timeframe}_data.csv")
                
                return df
            else:
                # If both APIs failed, try to load from disk
                df = self._load_from_disk(f"{symbol}_{timeframe}_data.csv")
                if df is not None:
                    logger.warning(f"Using data from disk for {symbol} as API calls failed")
                    return df
                else:
                    logger.error(f"Failed to fetch data for {symbol} from all sources")
                    return self._generate_mock_data(symbol)
                
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {str(e)}")
            
            # Try to load from disk if API calls fail
            df = self._load_from_disk(f"{symbol}_{timeframe}_data.csv")
            if df is not None:
                logger.warning(f"Using data from disk for {symbol} as API calls failed")
                return df
            else:
                logger.error(f"Failed to fetch data for {symbol} from all sources")
                return self._generate_mock_data(symbol)
    
    def _fetch_from_cryptocompare(self, symbol, timeframe, limit):
        """Fetch data from CryptoCompare API."""
        try:
            # Map timeframe to API endpoint
            if timeframe == 'minute':
                endpoint = f"{self.cryptocompare_base_url}/v2/histominute"
            elif timeframe == 'hour':
                endpoint = f"{self.cryptocompare_base_url}/v2/histohour"
            else:  # day
                endpoint = f"{self.cryptocompare_base_url}/v2/histoday"
                
            params = {
                'fsym': symbol,
                'tsym': 'USD',
                'limit': limit,
                'api_key': self.cryptocompare_api_key
            }
            
            response = requests.get(endpoint, params=params)
            data = response.json()
            
            if data['Response'] == 'Success':
                df = pd.DataFrame(data['Data']['Data'])
                df['time'] = pd.to_datetime(df['time'], unit='s')
                df.set_index('time', inplace=True)
                
                # Rename columns to standard format
                df.rename(columns={
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'volumefrom': 'volume'
                }, inplace=True)
                
                return df
            else:
                logger.warning(f"CryptoCompare API error: {data.get('Message', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching from CryptoCompare: {str(e)}")
            return None
    
    def _fetch_from_coingecko(self, symbol, timeframe, limit):
        """Fetch data from CoinGecko API."""
        try:
            # Map symbol to CoinGecko ID
            symbol_map = {
                'BTC': 'bitcoin',
                'ETH': 'ethereum',
                'SOL': 'solana',
                'ADA': 'cardano',
                'DOGE': 'dogecoin',
                'XRP': 'ripple',
                'DOT': 'polkadot',
                'UNI': 'uniswap',
                'LINK': 'chainlink',
                'AVAX': 'avalanche-2'
            }
            
            coin_id = symbol_map.get(symbol.upper())
            if not coin_id:
                logger.warning(f"No CoinGecko ID mapping for {symbol}")
                return None
                
            # Map timeframe to days
            if timeframe == 'minute':
                days = 1
            elif timeframe == 'hour':
                days = 7
            else:  # day
                days = 365
                
            endpoint = f"{self.coingecko_base_url}/coins/{coin_id}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': days,
                'interval': 'daily' if timeframe == 'day' else None,
                'x_cg_demo_api_key': self.coingecko_api_key
            }
            
            response = requests.get(endpoint, params={k: v for k, v in params.items() if v is not None})
            data = response.json()
            
            if 'prices' in data:
                # Create DataFrame from price data
                prices = data['prices']
                volumes = data['total_volumes']
                
                df = pd.DataFrame(prices, columns=['timestamp', 'close'])
                df['volume'] = [v[1] for v in volumes]
                
                # Convert timestamp to datetime
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                
                # Resample to match requested timeframe
                if timeframe == 'day':
                    df = df.resample('D').last()
                elif timeframe == 'hour':
                    df = df.resample('H').last()
                elif timeframe == 'minute':
                    df = df.resample('T').last()
                
                # Take only the requested number of points
                df = df.tail(limit)
                
                # Add mock OHLC data based on close price
                if 'open' not in df.columns:
                    df['open'] = df['close'].shift(1)
                    df['high'] = df['close'] * (1 + np.random.uniform(0, 0.02, size=len(df)))
                    df['low'] = df['close'] * (1 - np.random.uniform(0, 0.02, size=len(df)))
                    
                    # Fill NaN values in first row
                    df.iloc[0, df.columns.get_loc('open')] = df.iloc[0, df.columns.get_loc('close')] * 0.99
                
                return df
            else:
                logger.warning(f"CoinGecko API error: {data.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching from CoinGecko: {str(e)}")
            return None
    
    def _save_to_disk(self, df, filename):
        """Save DataFrame to disk as CSV."""
        try:
            file_path = os.path.join(self.data_dir, filename)
            df.to_csv(file_path)
            logger.info(f"Saved data to {file_path}")
        except Exception as e:
            logger.error(f"Error saving data to disk: {str(e)}")
    
    def _load_from_disk(self, filename):
        """Load DataFrame from disk."""
        try:
            file_path = os.path.join(self.data_dir, filename)
            if os.path.exists(file_path):
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                logger.info(f"Loaded data from {file_path}")
                return df
            else:
                return None
        except Exception as e:
            logger.error(f"Error loading data from disk: {str(e)}")
            return None
    
    def _generate_mock_data(self, symbol):
        """Generate mock data if all else fails."""
        logger.warning(f"Generating mock data for {symbol}")
        
        # Set seed based on symbol for consistent mock data
        np.random.seed(sum(ord(c) for c in symbol))
        
        # Generate dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=100)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Generate price data
        base_price = {
            'BTC': 60000,
            'ETH': 3000,
            'SOL': 100,
            'ADA': 0.5,
            'DOGE': 0.1,
            'XRP': 0.5,
            'DOT': 10,
            'UNI': 5,
            'LINK': 15,
            'AVAX': 20
        }.get(symbol.upper(), 100)
        
        # Generate random walk
        returns = np.random.normal(0.001, 0.02, size=len(dates))
        prices = base_price * (1 + np.cumsum(returns))
        
        # Create DataFrame
        df = pd.DataFrame({
            'open': prices * (1 - np.random.uniform(0, 0.01, size=len(dates))),
            'high': prices * (1 + np.random.uniform(0, 0.02, size=len(dates))),
            'low': prices * (1 - np.random.uniform(0, 0.02, size=len(dates))),
            'close': prices,
            'volume': np.random.uniform(1000000, 10000000, size=len(dates))
        }, index=dates)
        
        return df
    
    def add_technical_indicators(self, df):
        """
        Add technical indicators to the DataFrame.
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with added technical indicators
        """
        try:
            # Make a copy to avoid modifying the original
            df = df.copy()
            
            # Calculate RSI
            delta = df['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            
            rs = avg_gain / avg_loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # Calculate MACD
            exp1 = df['close'].ewm(span=12, adjust=False).mean()
            exp2 = df['close'].ewm(span=26, adjust=False).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9, adjust=False).mean()
            df['macd_hist'] = df['macd'] - df['macd_signal']
            
            # Calculate Bollinger Bands
            df['sma20'] = df['close'].rolling(window=20).mean()
            df['std20'] = df['close'].rolling(window=20).std()
            df['upper_band'] = df['sma20'] + (df['std20'] * 2)
            df['lower_band'] = df['sma20'] - (df['std20'] * 2)
            
            # Calculate Moving Averages
            df['sma50'] = df['close'].rolling(window=50).mean()
            df['sma200'] = df['close'].rolling(window=200).mean()
            
            # Calculate ATR (Average True Range)
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = np.max(ranges, axis=1)
            df['atr'] = true_range.rolling(14).mean()
            
            # Calculate Stochastic Oscillator
            low_14 = df['low'].rolling(window=14).min()
            high_14 = df['high'].rolling(window=14).max()
            df['stoch_k'] = 100 * ((df['close'] - low_14) / (high_14 - low_14))
            df['stoch_d'] = df['stoch_k'].rolling(window=3).mean()
            
            # Fill NaN values
            df.fillna(method='bfill', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding technical indicators: {str(e)}")
            return df
    
    def get_current_price(self, symbol):
        """
        Get the current price of a cryptocurrency.
        
        Args:
            symbol: Cryptocurrency symbol (e.g., BTC)
            
        Returns:
            Current price as float
        """
        try:
            # Try CryptoCompare first
            endpoint = f"{self.cryptocompare_base_url}/price"
            params = {
                'fsym': symbol,
                'tsyms': 'USD',
                'api_key': self.cryptocompare_api_key
            }
            
            response = requests.get(endpoint, params=params)
            data = response.json()
            
            if 'USD' in data:
                return float(data['USD'])
            else:
                # Try CoinGecko as fallback
                symbol_map = {
                    'BTC': 'bitcoin',
                    'ETH': 'ethereum',
                    'SOL': 'solana',
                    'ADA': 'cardano',
                    'DOGE': 'dogecoin',
                    'XRP': 'ripple',
                    'DOT': 'polkadot',
                    'UNI': 'uniswap',
                    'LINK': 'chainlink',
                    'AVAX': 'avalanche-2'
                }
                
                coin_id = symbol_map.get(symbol.upper())
                if not coin_id:
                    logger.warning(f"No CoinGecko ID mapping for {symbol}")
                    return self._get_mock_price(symbol)
                    
                endpoint = f"{self.coingecko_base_url}/simple/price"
                params = {
                    'ids': coin_id,
                    'vs_currencies': 'usd',
                    'x_cg_demo_api_key': self.coingecko_api_key
                }
                
                response = requests.get(endpoint, params=params)
                data = response.json()
                
                if coin_id in data and 'usd' in data[coin_id]:
                    return float(data[coin_id]['usd'])
                else:
                    logger.warning(f"Failed to get current price for {symbol}")
                    return self._get_mock_price(symbol)
                    
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {str(e)}")
            return self._get_mock_price(symbol)
    
    def _get_mock_price(self, symbol):
        """Get mock price for a symbol."""
        prices = {
            'BTC': 60000,
            'ETH': 3000,
            'SOL': 100,
            'ADA': 0.5,
            'DOGE': 0.1,
            'XRP': 0.5,
            'DOT': 10,
            'UNI': 5,
            'LINK': 15,
            'AVAX': 20
        }
        return prices.get(symbol.upper(), 100)


# For testing
if __name__ == "__main__":
    fetcher = CryptoDataFetcher()
    
    # Test fetching historical data
    df = fetcher.fetch_historical_data('BTC', timeframe='day', limit=30)
    print(df.head())
    
    # Test adding technical indicators
    df_with_indicators = fetcher.add_technical_indicators(df)
    print(df_with_indicators.columns)
    
    # Test getting current price
    price = fetcher.get_current_price('BTC')
    print(f"Current BTC price: ${price}")
