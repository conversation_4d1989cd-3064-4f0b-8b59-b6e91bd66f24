{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/404", "regex": "^/404(?:/)?$", "routeKeys": {}, "namedRegex": "^/404(?:/)?$"}, {"page": "/governance", "regex": "^/governance(?:/)?$", "routeKeys": {}, "namedRegex": "^/governance(?:/)?$"}, {"page": "/nav", "regex": "^/nav(?:/)?$", "routeKeys": {}, "namedRegex": "^/nav(?:/)?$"}, {"page": "/sentiment", "regex": "^/sentiment(?:/)?$", "routeKeys": {}, "namedRegex": "^/sentiment(?:/)?$"}, {"page": "/simple", "regex": "^/simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/simple(?:/)?$"}, {"page": "/simple-governance", "regex": "^/simple\\-governance(?:/)?$", "routeKeys": {}, "namedRegex": "^/simple\\-governance(?:/)?$"}, {"page": "/simple-sentiment", "regex": "^/simple\\-sentiment(?:/)?$", "routeKeys": {}, "namedRegex": "^/simple\\-sentiment(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}