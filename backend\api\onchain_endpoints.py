"""
On-Chain Signals API Endpoints

This module provides API endpoints for accessing on-chain enhanced trading signals.
"""

import os
import json
import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional, Any

# Import signal generator
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from signal_generator.onchain_signals import OnChainSignalGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('onchain_api')

# Create router
router = APIRouter(
    prefix="/api/onchain",
    tags=["onchain"],
    responses={404: {"description": "Not found"}},
)

# Data paths
SIGNALS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'signals')
ANALYSIS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'analysis')

# Models
class OnChainMetrics(BaseModel):
    active_addresses: Dict[str, Any]
    transaction_count: Dict[str, Any]
    network_health: Dict[str, Any]

class TradeLevels(BaseModel):
    current_price: Optional[float]
    entry: str
    take_profit: str
    stop_loss: str

class TechnicalIndicators(BaseModel):
    rsi: Any
    macd: str
    ma_cross: str

class OnChainIndicators(BaseModel):
    network_health: str
    active_addresses_trend: str
    transaction_count_trend: str

class SignalIndicators(BaseModel):
    technical: TechnicalIndicators
    onchain: OnChainIndicators

class TimeframeSignal(BaseModel):
    direction: str
    strength: str
    confidence: str
    indicators: SignalIndicators
    reasoning: List[str]
    trade_levels: TradeLevels

class CoinSignals(BaseModel):
    signals: Dict[str, TimeframeSignal]

class AllSignals(BaseModel):
    coins: Dict[str, CoinSignals]

@router.get("/metrics/{coin}", response_model=OnChainMetrics)
async def get_onchain_metrics(coin: str):
    """Get on-chain metrics for a specific coin."""
    try:
        # Check if coin is valid
        if coin.upper() not in ["BTC", "ETH", "SOL"]:
            raise HTTPException(status_code=404, detail=f"Metrics not found for coin: {coin}")

        # Load latest analysis
        analysis_file = os.path.join(ANALYSIS_DIR, "onchain_analysis_latest.json")
        if not os.path.exists(analysis_file):
            raise HTTPException(status_code=404, detail="On-chain analysis not found")

        with open(analysis_file, 'r') as f:
            analysis = json.load(f)

        # Get metrics for the requested coin
        coin_metrics = analysis.get(coin.upper(), {})
        if not coin_metrics:
            raise HTTPException(status_code=404, detail=f"Metrics not found for coin: {coin}")

        return coin_metrics
    except Exception as e:
        logger.error(f"Error getting on-chain metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/signals", response_model=AllSignals)
async def get_all_signals():
    """Get all on-chain enhanced signals for all coins and timeframes."""
    try:
        # Load latest signals
        signals_file = os.path.join(SIGNALS_DIR, "combined_signals_latest.json")
        if not os.path.exists(signals_file):
            # Generate signals if they don't exist
            generator = OnChainSignalGenerator()
            generator.load_onchain_insights()
            generator.load_technical_signals()
            generator.generate_combined_signals()
            generator.save_signals()

            ui_signals = generator.get_signals_for_ui()
            return ui_signals

        with open(signals_file, 'r') as f:
            signals = json.load(f)

        # Format signals for UI if needed
        generator = OnChainSignalGenerator()
        generator.combined_signals = signals
        ui_signals = generator.get_signals_for_ui()

        return ui_signals
    except Exception as e:
        logger.error(f"Error getting signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/signals/{coin}", response_model=CoinSignals)
async def get_coin_signals(coin: str):
    """Get on-chain enhanced signals for a specific coin."""
    try:
        # Check if coin is valid
        if coin.upper() not in ["BTC", "ETH", "SOL"]:
            raise HTTPException(status_code=404, detail=f"Signals not found for coin: {coin}")

        # Get all signals
        all_signals = await get_all_signals()

        # Get signals for the requested coin
        coin_signals = all_signals.get(coin.upper(), {})
        if not coin_signals:
            raise HTTPException(status_code=404, detail=f"Signals not found for coin: {coin}")

        return coin_signals
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting coin signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/signals/{coin}/{timeframe}", response_model=TimeframeSignal)
async def get_timeframe_signal(coin: str, timeframe: str):
    """Get on-chain enhanced signal for a specific coin and timeframe."""
    try:
        # Check if coin and timeframe are valid
        if coin.upper() not in ["BTC", "ETH", "SOL"]:
            raise HTTPException(status_code=404, detail=f"Signals not found for coin: {coin}")

        if timeframe not in ["1h", "2h", "4h", "8h"]:
            raise HTTPException(status_code=404, detail=f"Signals not found for timeframe: {timeframe}")

        # Get coin signals
        coin_signals = await get_coin_signals(coin)

        # Get signal for the requested timeframe
        timeframe_signal = coin_signals.get(timeframe, {})
        if not timeframe_signal:
            raise HTTPException(status_code=404, detail=f"Signal not found for {coin} on {timeframe} timeframe")

        return timeframe_signal
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting timeframe signal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/refresh")
async def refresh_signals():
    """Refresh all on-chain enhanced signals."""
    try:
        # Generate new signals
        generator = OnChainSignalGenerator()
        generator.load_onchain_insights()
        generator.load_technical_signals()
        generator.generate_combined_signals()
        generator.save_signals()

        return {"status": "success", "message": "Signals refreshed successfully"}
    except Exception as e:
        logger.error(f"Error refreshing signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
