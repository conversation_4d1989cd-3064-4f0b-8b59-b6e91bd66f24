"""
CoinMarketCap API Client

This module provides functions to fetch cryptocurrency data from the CoinMarketCap API.
"""

import os
import json
import logging
import time
import requests
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('coinmarketcap_client')

# Data storage path
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')
os.makedirs(DATA_DIR, exist_ok=True)

# Import API key from config
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
try:
    from config.api_keys import COINMARKETCAP_API_KEY
except ImportError:
    COINMARKETCAP_API_KEY = "26858902-d753-4225-8fc2-f5af98144a9d"  # Fallback to hardcoded key

# API configuration
API_KEY = COINMARKETCAP_API_KEY
BASE_URL = "https://pro-api.coinmarketcap.com/v1"
SANDBOX_URL = "https://sandbox-api.coinmarketcap.com/v1"

# Use sandbox for testing to avoid using up API credits
USE_SANDBOX = False

# Coin mapping (symbol to CoinMarketCap ID)
# These IDs are dynamic and should be fetched from the API
# This is just a starting point with common cryptocurrencies
COIN_MAPPING = {
    "BTC": 1,      # Bitcoin
    "ETH": 1027,   # Ethereum
    "SOL": 5426,   # Solana
    "ADA": 2010,   # Cardano
    "DOT": 6636,   # Polkadot
    "AVAX": 5805   # Avalanche
}

class CoinMarketCapClient:
    """Client for fetching data from CoinMarketCap API."""

    def __init__(self, api_key=None):
        """Initialize the CoinMarketCap API client."""
        self.api_key = api_key or API_KEY
        self.base_url = SANDBOX_URL if USE_SANDBOX else BASE_URL
        self.headers = {
            "X-CMC_PRO_API_KEY": self.api_key,
            "Accept": "application/json"
        }
        self.rate_limit_remaining = 10000  # Default value
        self.rate_limit_reset_at = 0
        self.coin_mapping = COIN_MAPPING.copy()

        logger.info(f"Initialized CoinMarketCap client with {'sandbox' if USE_SANDBOX else 'production'} API")

    def _handle_rate_limit(self):
        """Handle API rate limiting."""
        if self.rate_limit_remaining <= 1:
            # Wait until rate limit resets
            current_time = time.time()
            if current_time < self.rate_limit_reset_at:
                sleep_time = self.rate_limit_reset_at - current_time + 1
                logger.info(f"Rate limit reached. Waiting for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

    def _update_rate_limit_info(self, response):
        """Update rate limit information from response headers."""
        try:
            if 'X-RateLimit-Remaining' in response.headers:
                self.rate_limit_remaining = int(response.headers['X-RateLimit-Remaining'])

            if 'X-RateLimit-Reset' in response.headers:
                self.rate_limit_reset_at = int(response.headers['X-RateLimit-Reset'])
        except Exception as e:
            logger.warning(f"Error updating rate limit info: {str(e)}")

    def _make_request(self, endpoint, params=None):
        """
        Make a request to the CoinMarketCap API.

        Args:
            endpoint: API endpoint to call
            params: Query parameters

        Returns:
            Response data as JSON
        """
        try:
            self._handle_rate_limit()

            url = f"{self.base_url}/{endpoint}"
            response = requests.get(url, headers=self.headers, params=params)

            self._update_rate_limit_info(response)

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting and retrying...")
                time.sleep(60)  # Wait for 1 minute
                return self._make_request(endpoint, params)
            else:
                logger.error(f"API request failed with status code {response.status_code}: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error making request to {endpoint}: {str(e)}")
            return None

    def update_coin_mapping(self):
        """
        Update the coin mapping from symbol to CoinMarketCap ID.

        Returns:
            Dictionary with updated mapping
        """
        try:
            # Call the cryptocurrency/map endpoint
            params = {
                "listing_status": "active",
                "limit": 5000  # Get a large number of cryptocurrencies
            }

            response = self._make_request("cryptocurrency/map", params)

            if not response or "data" not in response:
                logger.error("Failed to update coin mapping")
                return self.coin_mapping

            # Update the mapping
            for coin in response["data"]:
                symbol = coin["symbol"]
                coin_id = coin["id"]
                self.coin_mapping[symbol] = coin_id

            logger.info(f"Updated coin mapping with {len(self.coin_mapping)} coins")
            return self.coin_mapping
        except Exception as e:
            logger.error(f"Error updating coin mapping: {str(e)}")
            return self.coin_mapping

    def get_coin_id(self, symbol):
        """
        Get the CoinMarketCap ID for a symbol.

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            CoinMarketCap ID or None if not found
        """
        # Check if we have the ID in our mapping
        if symbol in self.coin_mapping:
            return self.coin_mapping[symbol]

        # If not, try to update the mapping
        self.update_coin_mapping()

        # Check again
        if symbol in self.coin_mapping:
            return self.coin_mapping[symbol]

        logger.warning(f"Could not find CoinMarketCap ID for {symbol}")
        return None

    def get_latest_listings(self, limit=100, convert="USD"):
        """
        Get latest cryptocurrency listings.

        Args:
            limit: Number of cryptocurrencies to return
            convert: Currency to convert prices to

        Returns:
            Dictionary with latest listings
        """
        try:
            params = {
                "start": 1,
                "limit": limit,
                "convert": convert,
                "sort": "market_cap",
                "sort_dir": "desc"
            }

            response = self._make_request("cryptocurrency/listings/latest", params)

            if not response or "data" not in response:
                logger.error("Failed to get latest listings")
                return None

            return response["data"]
        except Exception as e:
            logger.error(f"Error getting latest listings: {str(e)}")
            return None

    def get_quotes(self, symbols, convert="USD"):
        """
        Get quotes for specific cryptocurrencies.

        Args:
            symbols: List of cryptocurrency symbols
            convert: Currency to convert prices to

        Returns:
            Dictionary with quotes data
        """
        try:
            # Convert symbols to IDs
            ids = []
            for symbol in symbols:
                coin_id = self.get_coin_id(symbol)
                if coin_id:
                    ids.append(str(coin_id))

            if not ids:
                logger.error("No valid cryptocurrency IDs found")
                return None

            params = {
                "id": ",".join(ids),
                "convert": convert
            }

            response = self._make_request("cryptocurrency/quotes/latest", params)

            if not response or "data" not in response:
                logger.error("Failed to get quotes")
                return None

            return response["data"]
        except Exception as e:
            logger.error(f"Error getting quotes: {str(e)}")
            return None

    def get_historical_quotes(self, symbol, time_start, time_end=None, interval="daily", convert="USD"):
        """
        Get historical quotes for a specific cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol
            time_start: Start time in ISO 8601 format
            time_end: End time in ISO 8601 format (default: now)
            interval: Time interval (daily, hourly, etc.)
            convert: Currency to convert prices to

        Returns:
            Dictionary with historical quotes data
        """
        try:
            # Get the coin ID
            coin_id = self.get_coin_id(symbol)
            if not coin_id:
                logger.error(f"Could not find CoinMarketCap ID for {symbol}")
                return None

            # Set default end time to now if not provided
            if not time_end:
                time_end = datetime.now().isoformat()

            params = {
                "id": coin_id,
                "time_start": time_start,
                "time_end": time_end,
                "interval": interval,
                "convert": convert
            }

            response = self._make_request("cryptocurrency/quotes/historical", params)

            if not response or "data" not in response:
                logger.error("Failed to get historical quotes")
                return None

            return response["data"]
        except Exception as e:
            logger.error(f"Error getting historical quotes: {str(e)}")
            return None

    def get_market_pairs(self, symbol, limit=100, convert="USD"):
        """
        Get market pairs for a specific cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol
            limit: Number of market pairs to return
            convert: Currency to convert prices to

        Returns:
            Dictionary with market pairs data
        """
        try:
            # Get the coin ID
            coin_id = self.get_coin_id(symbol)
            if not coin_id:
                logger.error(f"Could not find CoinMarketCap ID for {symbol}")
                return None

            params = {
                "id": coin_id,
                "limit": limit,
                "convert": convert
            }

            response = self._make_request("cryptocurrency/market-pairs/latest", params)

            if not response or "data" not in response:
                logger.error("Failed to get market pairs")
                return None

            return response["data"]
        except Exception as e:
            logger.error(f"Error getting market pairs: {str(e)}")
            return None

    def get_global_metrics(self, convert="USD"):
        """
        Get global cryptocurrency market metrics.

        Args:
            convert: Currency to convert prices to

        Returns:
            Dictionary with global metrics data
        """
        try:
            params = {
                "convert": convert
            }

            response = self._make_request("global-metrics/quotes/latest", params)

            if not response or "data" not in response:
                logger.error("Failed to get global metrics")
                return None

            return response["data"]
        except Exception as e:
            logger.error(f"Error getting global metrics: {str(e)}")
            return None

    def save_market_data(self, symbols=None):
        """
        Fetch and save market data for specific cryptocurrencies.

        Args:
            symbols: List of cryptocurrency symbols (default: BTC, ETH, SOL)

        Returns:
            Dictionary with saved data paths
        """
        try:
            # Set default symbols if not provided
            if not symbols:
                symbols = ["BTC", "ETH", "SOL"]

            # Get current timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Get quotes for the specified symbols
            quotes = self.get_quotes(symbols)
            if not quotes:
                logger.error("Failed to get quotes")
                return {}

            saved_files = {}

            # Save quotes data for each symbol
            for symbol in symbols:
                coin_id = self.get_coin_id(symbol)
                if not coin_id or str(coin_id) not in quotes:
                    logger.warning(f"No data found for {symbol}")
                    continue

                # Create directory for this coin if it doesn't exist
                coin_dir = os.path.join(DATA_DIR, symbol.lower())
                os.makedirs(coin_dir, exist_ok=True)

                # Save quotes data
                quotes_data = quotes[str(coin_id)]
                quotes_file = os.path.join(coin_dir, f"cmc_quotes_{timestamp}.json")
                with open(quotes_file, 'w') as f:
                    json.dump(quotes_data, f, indent=2)

                # Also save as latest
                latest_quotes_file = os.path.join(coin_dir, "cmc_quotes_latest.json")
                with open(latest_quotes_file, 'w') as f:
                    json.dump(quotes_data, f, indent=2)

                saved_files[symbol] = {
                    "quotes": quotes_file
                }

                # Get and save market pairs data
                market_pairs = self.get_market_pairs(symbol)
                if market_pairs:
                    pairs_file = os.path.join(coin_dir, f"cmc_market_pairs_{timestamp}.json")
                    with open(pairs_file, 'w') as f:
                        json.dump(market_pairs, f, indent=2)

                    # Also save as latest
                    latest_pairs_file = os.path.join(coin_dir, "cmc_market_pairs_latest.json")
                    with open(latest_pairs_file, 'w') as f:
                        json.dump(market_pairs, f, indent=2)

                    saved_files[symbol]["market_pairs"] = pairs_file

            # Get and save global metrics
            global_metrics = self.get_global_metrics()
            if global_metrics:
                metrics_file = os.path.join(DATA_DIR, f"cmc_global_metrics_{timestamp}.json")
                with open(metrics_file, 'w') as f:
                    json.dump(global_metrics, f, indent=2)

                # Also save as latest
                latest_metrics_file = os.path.join(DATA_DIR, "cmc_global_metrics_latest.json")
                with open(latest_metrics_file, 'w') as f:
                    json.dump(global_metrics, f, indent=2)

                saved_files["global"] = metrics_file

            logger.info(f"Saved market data for {len(saved_files)} coins")
            return saved_files
        except Exception as e:
            logger.error(f"Error saving market data: {str(e)}")
            return {}

    def get_latest_market_data(self, symbol):
        """
        Get the latest saved market data for a specific cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            Dictionary with latest market data
        """
        try:
            coin_dir = os.path.join(DATA_DIR, symbol.lower())

            # Check if directory exists
            if not os.path.exists(coin_dir):
                logger.warning(f"No data directory found for {symbol}")
                return {}

            result = {}

            # Load latest quotes data
            quotes_file = os.path.join(coin_dir, "cmc_quotes_latest.json")
            if os.path.exists(quotes_file):
                with open(quotes_file, 'r') as f:
                    result["quotes"] = json.load(f)

            # Load latest market pairs data
            pairs_file = os.path.join(coin_dir, "cmc_market_pairs_latest.json")
            if os.path.exists(pairs_file):
                with open(pairs_file, 'r') as f:
                    result["market_pairs"] = json.load(f)

            return result
        except Exception as e:
            logger.error(f"Error getting latest market data for {symbol}: {str(e)}")
            return {}

    def get_latest_global_metrics(self):
        """
        Get the latest saved global metrics.

        Returns:
            Dictionary with latest global metrics
        """
        try:
            metrics_file = os.path.join(DATA_DIR, "cmc_global_metrics_latest.json")

            if not os.path.exists(metrics_file):
                logger.warning("No global metrics file found")
                return {}

            with open(metrics_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error getting latest global metrics: {str(e)}")
            return {}

# Singleton instance
_instance = None

def get_instance():
    """Get the singleton instance of CoinMarketCapClient."""
    global _instance
    if _instance is None:
        _instance = CoinMarketCapClient()
    return _instance

def main():
    """Main function to test the CoinMarketCap client."""
    client = CoinMarketCapClient()

    # Update coin mapping
    client.update_coin_mapping()

    # Save market data for BTC, ETH, and SOL
    results = client.save_market_data(["BTC", "ETH", "SOL"])

    # Print results
    for symbol, files in results.items():
        if files:
            logger.info(f"Saved data for {symbol}")
        else:
            logger.warning(f"Failed to save data for {symbol}")

    return True

if __name__ == "__main__":
    main()
