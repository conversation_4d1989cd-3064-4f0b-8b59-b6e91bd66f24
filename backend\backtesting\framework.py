"""
Backtesting Framework

This module provides a framework for backtesting trading strategies
using historical data.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable

from data_aggregator.technical_analysis import TechnicalAnalysis

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class BacktestResult:
    """Results of a backtest."""
    
    def __init__(self, 
                 strategy_name: str,
                 symbol: str,
                 timeframe: str,
                 start_date: datetime,
                 end_date: datetime,
                 initial_capital: float,
                 trades: List[Dict[str, Any]],
                 equity_curve: pd.Series,
                 positions: pd.DataFrame):
        """Initialize backtest results."""
        self.strategy_name = strategy_name
        self.symbol = symbol
        self.timeframe = timeframe
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.trades = trades
        self.equity_curve = equity_curve
        self.positions = positions
        
        # Calculate performance metrics
        self.calculate_metrics()
    
    def calculate_metrics(self):
        """Calculate performance metrics."""
        if len(self.trades) == 0:
            self.total_trades = 0
            self.winning_trades = 0
            self.losing_trades = 0
            self.win_rate = 0.0
            self.profit_factor = 0.0
            self.total_return = 0.0
            self.annualized_return = 0.0
            self.max_drawdown = 0.0
            self.sharpe_ratio = 0.0
            return
        
        # Basic trade metrics
        self.total_trades = len(self.trades)
        self.winning_trades = sum(1 for trade in self.trades if trade['profit'] > 0)
        self.losing_trades = sum(1 for trade in self.trades if trade['profit'] <= 0)
        self.win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0.0
        
        # Profit metrics
        total_profit = sum(trade['profit'] for trade in self.trades if trade['profit'] > 0)
        total_loss = sum(abs(trade['profit']) for trade in self.trades if trade['profit'] < 0)
        self.profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
        
        # Return metrics
        self.total_return = (self.equity_curve.iloc[-1] / self.initial_capital - 1) * 100
        
        # Calculate annualized return
        days = (self.end_date - self.start_date).days
        if days > 0:
            self.annualized_return = ((1 + self.total_return / 100) ** (365 / days) - 1) * 100
        else:
            self.annualized_return = 0.0
        
        # Calculate maximum drawdown
        peak = self.equity_curve.expanding().max()
        drawdown = (self.equity_curve / peak - 1) * 100
        self.max_drawdown = abs(drawdown.min())
        
        # Calculate Sharpe ratio (assuming risk-free rate of 0)
        if len(self.equity_curve) > 1:
            daily_returns = self.equity_curve.pct_change().dropna()
            if daily_returns.std() > 0:
                self.sharpe_ratio = (daily_returns.mean() / daily_returns.std()) * (252 ** 0.5)  # Annualized
            else:
                self.sharpe_ratio = 0.0
        else:
            self.sharpe_ratio = 0.0
    
    def summary(self) -> Dict[str, Any]:
        """Get summary of backtest results."""
        return {
            "strategy_name": self.strategy_name,
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "start_date": self.start_date.strftime("%Y-%m-%d"),
            "end_date": self.end_date.strftime("%Y-%m-%d"),
            "initial_capital": self.initial_capital,
            "final_capital": self.equity_curve.iloc[-1] if len(self.equity_curve) > 0 else self.initial_capital,
            "total_return": f"{self.total_return:.2f}%",
            "annualized_return": f"{self.annualized_return:.2f}%",
            "total_trades": self.total_trades,
            "winning_trades": self.winning_trades,
            "losing_trades": self.losing_trades,
            "win_rate": f"{self.win_rate * 100:.2f}%",
            "profit_factor": f"{self.profit_factor:.2f}",
            "max_drawdown": f"{self.max_drawdown:.2f}%",
            "sharpe_ratio": f"{self.sharpe_ratio:.2f}"
        }
    
    def print_summary(self):
        """Print summary of backtest results."""
        summary = self.summary()
        
        print("\n===== BACKTEST RESULTS =====")
        print(f"Strategy: {summary['strategy_name']}")
        print(f"Symbol: {summary['symbol']}")
        print(f"Timeframe: {summary['timeframe']}")
        print(f"Period: {summary['start_date']} to {summary['end_date']}")
        print(f"Initial Capital: ${summary['initial_capital']:.2f}")
        print(f"Final Capital: ${summary['final_capital']:.2f}")
        print(f"Total Return: {summary['total_return']}")
        print(f"Annualized Return: {summary['annualized_return']}")
        print(f"Total Trades: {summary['total_trades']}")
        print(f"Winning Trades: {summary['winning_trades']} ({summary['win_rate']})")
        print(f"Losing Trades: {summary['losing_trades']}")
        print(f"Profit Factor: {summary['profit_factor']}")
        print(f"Maximum Drawdown: {summary['max_drawdown']}")
        print(f"Sharpe Ratio: {summary['sharpe_ratio']}")
        print("============================")

class Backtester:
    """Framework for backtesting trading strategies."""
    
    def __init__(self, price_data: pd.DataFrame, initial_capital: float = 10000.0):
        """
        Initialize the backtester.
        
        Args:
            price_data: DataFrame with OHLCV price data
            initial_capital: Initial capital for the backtest
        """
        self.price_data = price_data
        self.initial_capital = initial_capital
        
        # Ensure price data has required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in price_data.columns]
        if missing_columns:
            logger.warning(f"Price data is missing columns: {missing_columns}")
        
        logger.info(f"Initialized backtester with {len(price_data)} price data points")
    
    def run(self, 
            strategy: Callable[[pd.DataFrame, int, Dict[str, Any]], Dict[str, Any]],
            strategy_params: Dict[str, Any] = None,
            strategy_name: str = "Custom Strategy",
            symbol: str = "UNKNOWN",
            timeframe: str = "UNKNOWN") -> BacktestResult:
        """
        Run a backtest.
        
        Args:
            strategy: Strategy function that takes (price_data, current_index, params) and returns signal dict
            strategy_params: Parameters for the strategy
            strategy_name: Name of the strategy
            symbol: Symbol being traded
            timeframe: Timeframe of the price data
            
        Returns:
            BacktestResult object with backtest results
        """
        if strategy_params is None:
            strategy_params = {}
        
        # Initialize backtest variables
        capital = self.initial_capital
        position = 0  # 0 = no position, 1 = long, -1 = short
        position_size = 0
        entry_price = 0
        entry_date = None
        
        trades = []
        equity_curve = []
        positions_data = []
        
        # Run backtest
        for i in range(len(self.price_data)):
            current_price = self.price_data.iloc[i]['close']
            current_date = self.price_data.index[i]
            
            # Get strategy signal
            signal = strategy(self.price_data, i, strategy_params)
            
            # Process signal
            if position == 0:  # No position
                if signal.get('action') == 'buy':
                    # Enter long position
                    position = 1
                    position_size = capital * signal.get('size', 1.0) / current_price
                    entry_price = current_price
                    entry_date = current_date
                    
                    logger.debug(f"[{current_date}] BUY {position_size} @ {current_price}")
                
                elif signal.get('action') == 'sell' and strategy_params.get('allow_short', False):
                    # Enter short position
                    position = -1
                    position_size = capital * signal.get('size', 1.0) / current_price
                    entry_price = current_price
                    entry_date = current_date
                    
                    logger.debug(f"[{current_date}] SHORT {position_size} @ {current_price}")
            
            elif position == 1:  # Long position
                if signal.get('action') == 'sell':
                    # Exit long position
                    profit = (current_price - entry_price) * position_size
                    capital += profit
                    
                    # Record trade
                    trades.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position_size': position_size,
                        'profit': profit,
                        'return': (current_price / entry_price - 1) * 100,
                        'type': 'long'
                    })
                    
                    logger.debug(f"[{current_date}] SELL {position_size} @ {current_price}, Profit: {profit:.2f}")
                    
                    # Reset position
                    position = 0
                    position_size = 0
                    entry_price = 0
                    entry_date = None
            
            elif position == -1 and strategy_params.get('allow_short', False):  # Short position
                if signal.get('action') == 'buy':
                    # Exit short position
                    profit = (entry_price - current_price) * position_size
                    capital += profit
                    
                    # Record trade
                    trades.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position_size': position_size,
                        'profit': profit,
                        'return': (entry_price / current_price - 1) * 100,
                        'type': 'short'
                    })
                    
                    logger.debug(f"[{current_date}] COVER {position_size} @ {current_price}, Profit: {profit:.2f}")
                    
                    # Reset position
                    position = 0
                    position_size = 0
                    entry_price = 0
                    entry_date = None
            
            # Calculate equity
            if position == 0:
                equity = capital
            elif position == 1:
                equity = capital + (current_price - entry_price) * position_size
            else:  # position == -1
                equity = capital + (entry_price - current_price) * position_size
            
            equity_curve.append(equity)
            
            # Record position
            positions_data.append({
                'date': current_date,
                'position': position,
                'position_size': position_size,
                'entry_price': entry_price,
                'current_price': current_price,
                'equity': equity
            })
        
        # Close any open position at the end
        if position != 0:
            current_price = self.price_data.iloc[-1]['close']
            current_date = self.price_data.index[-1]
            
            if position == 1:  # Long position
                profit = (current_price - entry_price) * position_size
                capital += profit
                
                # Record trade
                trades.append({
                    'entry_date': entry_date,
                    'exit_date': current_date,
                    'entry_price': entry_price,
                    'exit_price': current_price,
                    'position_size': position_size,
                    'profit': profit,
                    'return': (current_price / entry_price - 1) * 100,
                    'type': 'long'
                })
                
                logger.debug(f"[{current_date}] CLOSE LONG {position_size} @ {current_price}, Profit: {profit:.2f}")
            
            elif position == -1:  # Short position
                profit = (entry_price - current_price) * position_size
                capital += profit
                
                # Record trade
                trades.append({
                    'entry_date': entry_date,
                    'exit_date': current_date,
                    'entry_price': entry_price,
                    'exit_price': current_price,
                    'position_size': position_size,
                    'profit': profit,
                    'return': (entry_price / current_price - 1) * 100,
                    'type': 'short'
                })
                
                logger.debug(f"[{current_date}] CLOSE SHORT {position_size} @ {current_price}, Profit: {profit:.2f}")
        
        # Create result objects
        equity_curve_series = pd.Series(equity_curve, index=self.price_data.index)
        positions_df = pd.DataFrame(positions_data)
        if not positions_df.empty:
            positions_df.set_index('date', inplace=True)
        
        # Create and return backtest result
        result = BacktestResult(
            strategy_name=strategy_name,
            symbol=symbol,
            timeframe=timeframe,
            start_date=self.price_data.index[0],
            end_date=self.price_data.index[-1],
            initial_capital=self.initial_capital,
            trades=trades,
            equity_curve=equity_curve_series,
            positions=positions_df
        )
        
        return result

# Example strategy functions
def simple_moving_average_crossover(price_data: pd.DataFrame, current_index: int, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Simple moving average crossover strategy.
    
    Args:
        price_data: DataFrame with price data
        current_index: Current index in the price data
        params: Strategy parameters
        
    Returns:
        Signal dictionary
    """
    # Get parameters
    fast_period = params.get('fast_period', 10)
    slow_period = params.get('slow_period', 50)
    
    # Need enough data for the slow moving average
    if current_index < slow_period:
        return {'action': 'hold'}
    
    # Calculate moving averages
    prices = price_data['close'].values
    fast_ma = np.mean(prices[current_index - fast_period + 1:current_index + 1])
    slow_ma = np.mean(prices[current_index - slow_period + 1:current_index + 1])
    
    # Previous moving averages
    if current_index > 0:
        prev_prices = price_data['close'].values
        prev_fast_ma = np.mean(prev_prices[current_index - fast_period:current_index])
        prev_slow_ma = np.mean(prev_prices[current_index - slow_period:current_index])
    else:
        prev_fast_ma = fast_ma
        prev_slow_ma = slow_ma
    
    # Generate signals
    if fast_ma > slow_ma and prev_fast_ma <= prev_slow_ma:
        return {'action': 'buy', 'size': 1.0}
    elif fast_ma < slow_ma and prev_fast_ma >= prev_slow_ma:
        return {'action': 'sell', 'size': 1.0}
    else:
        return {'action': 'hold'}

def rsi_strategy(price_data: pd.DataFrame, current_index: int, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    RSI-based trading strategy.
    
    Args:
        price_data: DataFrame with price data
        current_index: Current index in the price data
        params: Strategy parameters
        
    Returns:
        Signal dictionary
    """
    # Get parameters
    period = params.get('period', 14)
    oversold = params.get('oversold', 30)
    overbought = params.get('overbought', 70)
    
    # Need enough data for RSI
    if current_index < period:
        return {'action': 'hold'}
    
    # Calculate RSI
    prices = price_data['close'].values[:current_index + 1]
    rsi = TechnicalAnalysis.calculate_rsi(prices, period)
    
    # Previous RSI
    if current_index > 0:
        prev_prices = price_data['close'].values[:current_index]
        prev_rsi = TechnicalAnalysis.calculate_rsi(prev_prices, period)
    else:
        prev_rsi = rsi
    
    # Generate signals
    if rsi < oversold and prev_rsi >= oversold:
        return {'action': 'buy', 'size': 1.0}
    elif rsi > overbought and prev_rsi <= overbought:
        return {'action': 'sell', 'size': 1.0}
    else:
        return {'action': 'hold'}

def main():
    """Main function for testing the backtesting framework."""
    # Create sample price data
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
    prices = np.linspace(100, 150, 100) + np.random.normal(0, 5, 100)  # Uptrend with noise
    volumes = np.random.randint(1000, 5000, 100)
    
    # Create DataFrame
    price_data = pd.DataFrame({
        'open': prices,
        'high': prices + np.random.uniform(0, 2, 100),
        'low': prices - np.random.uniform(0, 2, 100),
        'close': prices,
        'volume': volumes
    }, index=dates)
    
    # Initialize backtester
    backtester = Backtester(price_data)
    
    # Run backtest with SMA crossover strategy
    sma_result = backtester.run(
        strategy=simple_moving_average_crossover,
        strategy_params={'fast_period': 10, 'slow_period': 30},
        strategy_name="SMA Crossover",
        symbol="SAMPLE",
        timeframe="1d"
    )
    
    # Print results
    sma_result.print_summary()
    
    # Run backtest with RSI strategy
    rsi_result = backtester.run(
        strategy=rsi_strategy,
        strategy_params={'period': 14, 'oversold': 30, 'overbought': 70},
        strategy_name="RSI Strategy",
        symbol="SAMPLE",
        timeframe="1d"
    )
    
    # Print results
    rsi_result.print_summary()

if __name__ == "__main__":
    main()
