"""
Consensus Engine for Project Ruby.

This module implements multiple AI models with different architectures
and combines their predictions to create more robust trading signals.
"""

import numpy as np
import pandas as pd
import json
import os
import logging
from datetime import datetime, timedelta
import random
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import data fetcher
try:
    from backend.data.crypto_data_fetcher import CryptoDataFetcher
    has_data_fetcher = True
    logger.info("CryptoDataFetcher imported successfully")
except ImportError:
    has_data_fetcher = False
    logger.warning("CryptoDataFetcher not found, using mock data")

class BaseModel:
    """Base class for all prediction models"""
    def __init__(self, lookback, features):
        self.lookback = lookback
        self.features = features
        self.model = None

    def preprocess_data(self, data):
        """Preprocess data for model input"""
        # In a real implementation, this would scale and prepare the data
        # For this demo, we'll use the data as is
        return data

    def predict(self, symbol, data=None):
        """Generate prediction for a symbol"""
        if data is None:
            data = self.fetch_data(symbol)

        # In a real implementation, this would use the actual model
        # For this demo, we'll generate realistic mock predictions

        # Generate a realistic prediction
        current_price = data.get('price', 50000)

        # Add some randomness but with a bias based on model type
        bias = self.get_model_bias(symbol)
        random_factor = random.uniform(-0.02, 0.02)
        prediction = current_price * (1 + bias + random_factor)

        # Determine direction
        direction = 'up' if prediction > current_price else 'down'

        # Calculate confidence (higher for stronger signals)
        confidence = 0.5 + abs(bias) * 2 + random.uniform(0, 0.2)
        confidence = min(0.95, max(0.5, confidence))

        return {
            'model_type': self.__class__.__name__,
            'prediction': float(prediction),
            'direction': direction,
            'confidence': confidence,
            'features_used': self.features,
            'lookback_period': self.lookback
        }

    def get_model_bias(self, symbol):
        """Get model bias for a symbol (for demo purposes)"""
        # This would be replaced with actual model logic
        return 0.0

    def fetch_data(self, symbol):
        """Fetch data for a symbol"""
        # In a real implementation, this would fetch actual data
        # For this demo, we'll use our timeframe signals data if available

        try:
            data_path = os.path.join('frontend', 'public', 'data', 'timeframe_signals.json')
            if os.path.exists(data_path):
                with open(data_path, 'r') as f:
                    signals_data = json.load(f)

                if symbol in signals_data:
                    return signals_data[symbol]

            # Fallback to mock data
            return {
                'symbol': symbol,
                'price': self.get_mock_price(symbol),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error fetching data: {str(e)}")
            return {
                'symbol': symbol,
                'price': self.get_mock_price(symbol),
                'timestamp': datetime.now().isoformat()
            }

    def get_mock_price(self, symbol):
        """Get mock price for a symbol"""
        prices = {
            'BTC': 60000,
            'ETH': 3000,
            'SOL': 100,
            'ADA': 0.5
        }
        return prices.get(symbol, 1000)


class LSTMModel(BaseModel):
    """Long Short-Term Memory model for time series prediction"""
    def __init__(self, lookback=60, features=['close', 'volume', 'rsi']):
        super().__init__(lookback, features)

    def get_model_bias(self, symbol):
        """LSTM models tend to be good at capturing long-term trends"""
        # In a real implementation, this would be based on actual model behavior
        # For demo, we'll make LSTMs slightly bullish on BTC and ETH
        if symbol in ['BTC', 'ETH']:
            return 0.01
        elif symbol in ['SOL']:
            return -0.005
        else:
            return 0.0


class CNNModel(BaseModel):
    """Convolutional Neural Network model for time series prediction"""
    def __init__(self, lookback=30, features=['close', 'volume', 'macd']):
        super().__init__(lookback, features)

    def get_model_bias(self, symbol):
        """CNNs tend to be good at capturing patterns"""
        # In a real implementation, this would be based on actual model behavior
        # For demo, we'll make CNNs slightly bearish on most coins
        if symbol in ['SOL', 'ADA']:
            return -0.015
        elif symbol in ['BTC']:
            return 0.005
        else:
            return -0.005


class GRUModel(BaseModel):
    """Gated Recurrent Unit model for time series prediction"""
    def __init__(self, lookback=14, features=['close', 'volume', 'bollinger']):
        super().__init__(lookback, features)

    def get_model_bias(self, symbol):
        """GRUs tend to be good at capturing recent changes"""
        # In a real implementation, this would be based on actual model behavior
        # For demo, we'll make GRUs neutral to slightly bullish
        if symbol in ['ETH', 'ADA']:
            return 0.008
        elif symbol in ['BTC']:
            return 0.012
        else:
            return 0.0


class TransformerModel(BaseModel):
    """Transformer model for time series prediction"""
    def __init__(self, lookback=20, features=['close', 'volume', 'rsi', 'macd']):
        super().__init__(lookback, features)

    def get_model_bias(self, symbol):
        """Transformers tend to be good at capturing complex relationships"""
        # In a real implementation, this would be based on actual model behavior
        # For demo, we'll make Transformers more volatile
        if symbol in ['BTC']:
            return 0.02
        elif symbol in ['SOL']:
            return -0.02
        elif symbol in ['ETH']:
            return 0.01
        else:
            return 0.0


class ConsensusEngine:
    """Engine that combines predictions from multiple models"""
    def __init__(self):
        self.models = [
            LSTMModel(lookback=60, features=['close', 'volume', 'rsi']),
            CNNModel(lookback=30, features=['close', 'volume', 'macd']),
            GRUModel(lookback=14, features=['close', 'volume', 'bollinger']),
            TransformerModel(lookback=20, features=['close', 'volume', 'rsi', 'macd'])
        ]

        # Initialize data fetcher if available
        self.data_fetcher = CryptoDataFetcher() if has_data_fetcher else None

        # Cache for storing predictions
        self.predictions_cache = {}
        self.last_update = {}

        # Create data directory if it doesn't exist
        self.data_dir = os.path.join('frontend', 'public', 'data')
        os.makedirs(self.data_dir, exist_ok=True)

        # Try to load cached predictions
        self._load_cached_predictions()

    def _load_cached_predictions(self):
        """Load cached predictions from disk"""
        try:
            cache_path = os.path.join(self.data_dir, 'consensus_predictions.json')
            if os.path.exists(cache_path):
                with open(cache_path, 'r') as f:
                    cache_data = json.load(f)

                self.predictions_cache = cache_data.get('predictions', {})

                # Convert string timestamps to datetime objects
                self.last_update = {
                    symbol: datetime.fromisoformat(timestamp)
                    for symbol, timestamp in cache_data.get('last_update', {}).items()
                }

                logger.info(f"Loaded cached predictions for {len(self.predictions_cache)} symbols")
        except Exception as e:
            logger.error(f"Error loading cached predictions: {str(e)}")

    def _save_cached_predictions(self):
        """Save cached predictions to disk"""
        try:
            cache_path = os.path.join(self.data_dir, 'consensus_predictions.json')

            # Convert datetime objects to ISO format strings
            last_update_iso = {
                symbol: timestamp.isoformat()
                for symbol, timestamp in self.last_update.items()
            }

            cache_data = {
                'predictions': self.predictions_cache,
                'last_update': last_update_iso
            }

            with open(cache_path, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.info(f"Saved cached predictions for {len(self.predictions_cache)} symbols")
        except Exception as e:
            logger.error(f"Error saving cached predictions: {str(e)}")

    def generate_prediction(self, symbol, force_refresh=False):
        """Generate consensus prediction for a symbol"""
        # Check if we have a recent prediction
        current_time = datetime.now()
        if not force_refresh and symbol in self.last_update:
            time_diff = (current_time - self.last_update[symbol]).total_seconds() / 60
            if time_diff < 15 and symbol in self.predictions_cache:  # Cache for 15 minutes
                logger.info(f"Using cached prediction for {symbol} (updated {time_diff:.1f} minutes ago)")
                return self.predictions_cache[symbol]

        # Fetch data
        if self.data_fetcher:
            # Use real data fetcher if available
            try:
                data = self.data_fetcher.fetch_historical_data(symbol, timeframe='day', limit=100)
                if data is not None and not data.empty:
                    # Add technical indicators
                    data = self.data_fetcher.add_technical_indicators(data)

                    # Set index name to symbol for reference in models
                    data.index.name = symbol

                    # Get current price
                    current_price = self.data_fetcher.get_current_price(symbol)
                else:
                    # Fallback to mock data
                    logger.warning(f"Failed to fetch real data for {symbol}, using mock data")
                    data = self.fetch_data(symbol)
                    current_price = data.get('price', self.get_mock_price(symbol))
            except Exception as e:
                logger.error(f"Error fetching real data for {symbol}: {str(e)}")
                data = self.fetch_data(symbol)
                current_price = data.get('price', self.get_mock_price(symbol))
        else:
            # Use mock data
            data = self.fetch_data(symbol)
            current_price = data.get('price', self.get_mock_price(symbol))

        # Get predictions from all models
        predictions = []
        for model in self.models:
            try:
                # Always use mock prediction for now
                # The real ML models need more work to handle DataFrame input
                pred = model.predict(symbol, data)

                if pred:
                    predictions.append(pred)
            except Exception as e:
                logger.error(f"Error in model {model.__class__.__name__}: {str(e)}")

        if not predictions:
            return {"error": "All models failed to generate predictions"}

        # Calculate consensus
        consensus = self.calculate_consensus(predictions)
        confidence = self.calculate_confidence(predictions)
        agreement = self.calculate_agreement(predictions)

        result = {
            'symbol': symbol,
            'timestamp': current_time.isoformat(),
            'consensus': consensus,
            'confidence': confidence,
            'agreement_level': agreement,
            'model_predictions': predictions,
            'timeframe': '1d',  # Default timeframe
            'direction': self.determine_direction(predictions),
            'strength': self.calculate_strength(predictions, agreement),
            'price': current_price
        }

        # Cache the result
        self.predictions_cache[symbol] = result
        self.last_update[symbol] = current_time

        # Save cached predictions
        self._save_cached_predictions()

        return result

    def calculate_consensus(self, predictions):
        """Calculate consensus from multiple predictions"""
        # Count directions
        up_votes = sum(1 for p in predictions if p['direction'] == 'up')
        down_votes = sum(1 for p in predictions if p['direction'] == 'down')

        # Weight by confidence
        weighted_up = sum(p['confidence'] for p in predictions if p['direction'] == 'up')
        weighted_down = sum(p['confidence'] for p in predictions if p['direction'] == 'down')

        if weighted_up > weighted_down:
            return "bullish"
        elif weighted_down > weighted_up:
            return "bearish"
        else:
            return "neutral"

    def calculate_confidence(self, predictions):
        """Calculate overall confidence level"""
        # Average confidence weighted by model performance
        return sum(p['confidence'] for p in predictions) / len(predictions)

    def calculate_agreement(self, predictions):
        """Calculate agreement level between models"""
        directions = [p['direction'] for p in predictions]

        if all(d == directions[0] for d in directions):
            return 1.0  # Perfect agreement

        # Count most common direction
        up_count = sum(1 for d in directions if d == 'up')
        down_count = sum(1 for d in directions if d == 'down')

        # Calculate agreement as percentage of models agreeing with majority
        majority = max(up_count, down_count)
        return majority / len(predictions)

    def determine_direction(self, predictions):
        """Determine overall direction from predictions"""
        up_votes = sum(1 for p in predictions if p['direction'] == 'up')
        down_votes = sum(1 for p in predictions if p['direction'] == 'down')

        if up_votes > down_votes:
            return "up"
        elif down_votes > up_votes:
            return "down"
        else:
            return "neutral"

    def calculate_strength(self, predictions, agreement):
        """Calculate signal strength based on predictions and agreement"""
        # Average confidence
        avg_confidence = sum(p['confidence'] for p in predictions) / len(predictions)

        # Combine agreement and confidence
        strength = (avg_confidence + agreement) / 2

        return strength

    def fetch_data(self, symbol):
        """Fetch historical data for a symbol"""
        # In a real implementation, this would fetch actual data
        # For this demo, we'll use our timeframe signals data if available

        try:
            data_path = os.path.join('frontend', 'public', 'data', 'timeframe_signals.json')
            if os.path.exists(data_path):
                with open(data_path, 'r') as f:
                    signals_data = json.load(f)

                if symbol in signals_data:
                    return signals_data[symbol]

            # Fallback to mock data
            return {
                'symbol': symbol,
                'price': self.get_mock_price(symbol),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error fetching data: {str(e)}")
            return {
                'symbol': symbol,
                'price': self.get_mock_price(symbol),
                'timestamp': datetime.now().isoformat()
            }

    def get_mock_price(self, symbol):
        """Get mock price for a symbol"""
        prices = {
            'BTC': 60000,
            'ETH': 3000,
            'SOL': 100,
            'ADA': 0.5
        }
        return prices.get(symbol, 1000)


    def generate_predictions_for_symbols(self, symbols=None, force_refresh=False):
        """
        Generate consensus predictions for multiple symbols.

        Args:
            symbols: List of cryptocurrency symbols
            force_refresh: Whether to force a refresh of cached predictions

        Returns:
            Dictionary of consensus predictions
        """
        if symbols is None:
            symbols = ["BTC", "ETH", "SOL", "ADA"]

        predictions = {}
        for symbol in symbols:
            try:
                prediction = self.generate_prediction(symbol, force_refresh)
                predictions[symbol] = prediction
            except Exception as e:
                logger.error(f"Error generating prediction for {symbol}: {str(e)}")
                predictions[symbol] = {"error": str(e)}

        return predictions


# For testing
if __name__ == "__main__":
    engine = ConsensusEngine()

    # Test generating prediction for BTC
    btc_prediction = engine.generate_prediction("BTC")
    print(json.dumps(btc_prediction, indent=2))

    # Test generating predictions for multiple symbols
    predictions = engine.generate_predictions_for_symbols(["BTC", "ETH", "SOL", "ADA"])
    for symbol, prediction in predictions.items():
        print(f"\n{symbol} Prediction:")
        print(json.dumps(prediction, indent=2))
