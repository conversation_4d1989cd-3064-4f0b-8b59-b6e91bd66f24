"""
CoinGecko On-Chain Data Fetcher

This module extends the CoinGecko API integration to fetch on-chain metrics
and other advanced data available through the CoinGecko Pro API.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

import httpx

# Add the project root to the Python path
import sys
import os
sys.path.append(os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# Import the base CoinGecko fetcher
from data_aggregator.crypto_price_fetcher import CoinGeckoFetcher, ContextItem

# Import API key
from config.api_keys import COINGECKO_API_KEY

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
COINGECKO_API_URL = "https://api.coingecko.com/api/v3"  # Use free API instead of Pro API
DEFAULT_COINS = ["bitcoin", "ethereum", "binancecoin", "ripple", "cardano", "solana"]
REQUEST_TIMEOUT = 30  # seconds
RATE_LIMIT_DELAY = 3.0  # seconds between requests to avoid rate limiting

class CoinGeckoOnChainFetcher(CoinGeckoFetcher):
    """Fetches on-chain and advanced data from CoinGecko Pro API."""

    def __init__(self, api_key: str = COINGECKO_API_KEY):
        """Initialize the fetcher with the CoinGecko API key."""
        super().__init__(api_key)
        self.api_url = COINGECKO_API_URL  # Always use the free API for now
        logger.info("CoinGecko On-Chain fetcher initialized")

    async def fetch_coin_details(self, coin_id: str) -> Dict[str, Any]:
        """
        Fetch detailed information for a specific coin, including on-chain metrics.

        Args:
            coin_id: CoinGecko coin ID (e.g., "bitcoin")

        Returns:
            Dictionary with detailed coin information
        """
        url = f"{self.api_url}/coins/{coin_id}"
        params = {
            "localization": "false",
            "tickers": "false",
            "market_data": "true",
            "community_data": "true",
            "developer_data": "true",
            "sparkline": "false"
        }

        try:
            # Add delay to avoid rate limiting
            await asyncio.sleep(RATE_LIMIT_DELAY)

            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(url, params=params, headers=self.headers)
                response.raise_for_status()
                data = response.json()

                logger.info(f"Fetched detailed data for {coin_id}")
                return data

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error occurred: {e}")
            if e.response.status_code == 429:
                logger.warning("Rate limit exceeded, consider increasing delay")
            return {}
        except Exception as e:
            logger.error(f"Error fetching coin details for {coin_id}: {str(e)}")
            return {}

    async def fetch_onchain_metrics(self, coin_id: str) -> Dict[str, Any]:
        """
        Extract on-chain metrics from coin details.

        Args:
            coin_id: CoinGecko coin ID (e.g., "bitcoin")

        Returns:
            Dictionary with on-chain metrics
        """
        coin_details = await self.fetch_coin_details(coin_id)

        if not coin_details:
            return {}

        # Extract relevant on-chain and market metrics
        metrics = {
            "coin_id": coin_id,
            "symbol": coin_details.get("symbol", "").upper(),
            "name": coin_details.get("name", ""),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "coingecko"
        }

        # Market data
        market_data = coin_details.get("market_data", {})
        if market_data:
            metrics.update({
                "market_cap_usd": market_data.get("market_cap", {}).get("usd", 0),
                "total_volume_usd": market_data.get("total_volume", {}).get("usd", 0),
                "circulating_supply": market_data.get("circulating_supply", 0),
                "total_supply": market_data.get("total_supply", 0),
                "max_supply": market_data.get("max_supply", 0),
                "current_price_usd": market_data.get("current_price", {}).get("usd", 0),
                "ath_price_usd": market_data.get("ath", {}).get("usd", 0),
                "ath_change_percentage": market_data.get("ath_change_percentage", {}).get("usd", 0),
                "atl_price_usd": market_data.get("atl", {}).get("usd", 0),
                "atl_change_percentage": market_data.get("atl_change_percentage", {}).get("usd", 0),
                "price_change_percentage_24h": market_data.get("price_change_percentage_24h", 0),
                "price_change_percentage_7d": market_data.get("price_change_percentage_7d", 0),
                "price_change_percentage_30d": market_data.get("price_change_percentage_30d", 0),
                "market_cap_change_percentage_24h": market_data.get("market_cap_change_percentage_24h", 0)
            })

        # Community data (social metrics)
        community_data = coin_details.get("community_data", {})
        if community_data:
            metrics.update({
                "twitter_followers": community_data.get("twitter_followers", 0),
                "reddit_subscribers": community_data.get("reddit_subscribers", 0),
                "telegram_channel_user_count": community_data.get("telegram_channel_user_count", 0),
                "facebook_likes": community_data.get("facebook_likes", 0),
                "github_stars": community_data.get("github_stars", 0)
            })

        # Developer data
        developer_data = coin_details.get("developer_data", {})
        if developer_data:
            metrics.update({
                "github_commits_4_weeks": developer_data.get("commit_count_4_weeks", 0),
                "github_stars": developer_data.get("stars", 0),
                "github_forks": developer_data.get("forks", 0),
                "github_contributors": developer_data.get("pull_request_contributors", 0),
                "github_pull_requests_merged": developer_data.get("pull_requests_merged", 0),
                "github_pull_request_contributors": developer_data.get("pull_request_contributors", 0)
            })

        # Public interest stats
        public_interest_stats = coin_details.get("public_interest_stats", {})
        if public_interest_stats:
            metrics.update({
                "alexa_rank": public_interest_stats.get("alexa_rank", 0),
                "bing_matches": public_interest_stats.get("bing_matches", 0)
            })

        # Calculate derived metrics
        if metrics.get("circulating_supply") and metrics.get("total_supply"):
            metrics["circulating_supply_percentage"] = (
                metrics["circulating_supply"] / metrics["total_supply"] * 100
                if metrics["total_supply"] > 0 else 0
            )

        # Market cap to volume ratio (higher values may indicate less liquidity)
        if metrics.get("market_cap_usd") and metrics.get("total_volume_usd"):
            metrics["market_cap_to_volume_ratio"] = (
                metrics["market_cap_usd"] / metrics["total_volume_usd"]
                if metrics["total_volume_usd"] > 0 else 0
            )

        return metrics

    async def fetch_all_onchain_metrics(self, coin_ids: List[str] = DEFAULT_COINS) -> List[Dict[str, Any]]:
        """
        Fetch on-chain metrics for multiple coins.

        Args:
            coin_ids: List of CoinGecko coin IDs

        Returns:
            List of dictionaries with on-chain metrics
        """
        tasks = [self.fetch_onchain_metrics(coin_id) for coin_id in coin_ids]
        results = await asyncio.gather(*tasks)

        # Filter out empty results
        metrics = [result for result in results if result]

        logger.info(f"Fetched on-chain metrics for {len(metrics)} coins")
        return metrics

    def analyze_onchain_sentiment(self, metrics: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Analyze on-chain sentiment based on metrics.

        Args:
            metrics: List of on-chain metrics

        Returns:
            Dictionary with sentiment analysis for each coin
        """
        sentiment = {}

        for data in metrics:
            coin_symbol = data.get("symbol", "").upper()

            # Initialize sentiment
            sentiment[coin_symbol] = {
                "overall_sentiment": "neutral",
                "market_sentiment": "neutral",
                "social_sentiment": "neutral",
                "development_sentiment": "neutral",
                "confidence": 0.5
            }

            # Analyze market metrics
            market_score = 0
            market_factors = 0

            # Price momentum
            if "price_change_percentage_24h" in data:
                market_factors += 1
                if data["price_change_percentage_24h"] > 5:
                    market_score += 1
                elif data["price_change_percentage_24h"] < -5:
                    market_score -= 1

            if "price_change_percentage_7d" in data:
                market_factors += 1
                if data["price_change_percentage_7d"] > 10:
                    market_score += 1
                elif data["price_change_percentage_7d"] < -10:
                    market_score -= 1

            # Volume to market cap ratio (higher is better)
            if "market_cap_to_volume_ratio" in data:
                market_factors += 1
                if data["market_cap_to_volume_ratio"] < 5:
                    market_score += 1
                elif data["market_cap_to_volume_ratio"] > 20:
                    market_score -= 1

            # ATH distance
            if "ath_change_percentage" in data:
                market_factors += 1
                if data["ath_change_percentage"] > -20:
                    market_score += 1
                elif data["ath_change_percentage"] < -70:
                    market_score += 0.5  # Very far from ATH might be positive for future potential

            # Determine market sentiment
            if market_factors > 0:
                market_sentiment_score = market_score / market_factors
                if market_sentiment_score > 0.5:
                    sentiment[coin_symbol]["market_sentiment"] = "bullish"
                elif market_sentiment_score < -0.5:
                    sentiment[coin_symbol]["market_sentiment"] = "bearish"
                elif market_sentiment_score > 0:
                    sentiment[coin_symbol]["market_sentiment"] = "slightly_bullish"
                elif market_sentiment_score < 0:
                    sentiment[coin_symbol]["market_sentiment"] = "slightly_bearish"

            # Analyze social metrics
            social_score = 0
            social_factors = 0

            # Social following growth
            if "twitter_followers" in data and data["twitter_followers"] > 500000:
                social_factors += 1
                social_score += 1

            if "reddit_subscribers" in data and data["reddit_subscribers"] > 100000:
                social_factors += 1
                social_score += 1

            # Determine social sentiment
            if social_factors > 0:
                social_sentiment_score = social_score / social_factors
                if social_sentiment_score > 0.5:
                    sentiment[coin_symbol]["social_sentiment"] = "bullish"
                elif social_sentiment_score < -0.5:
                    sentiment[coin_symbol]["social_sentiment"] = "bearish"
                elif social_sentiment_score > 0:
                    sentiment[coin_symbol]["social_sentiment"] = "slightly_bullish"
                elif social_sentiment_score < 0:
                    sentiment[coin_symbol]["social_sentiment"] = "slightly_bearish"

            # Analyze development metrics
            dev_score = 0
            dev_factors = 0

            # GitHub activity
            if "github_commits_4_weeks" in data:
                dev_factors += 1
                if data["github_commits_4_weeks"] > 100:
                    dev_score += 1
                elif data["github_commits_4_weeks"] < 10:
                    dev_score -= 1

            if "github_contributors" in data:
                dev_factors += 1
                if data["github_contributors"] > 50:
                    dev_score += 1
                elif data["github_contributors"] < 5:
                    dev_score -= 1

            # Determine development sentiment
            if dev_factors > 0:
                dev_sentiment_score = dev_score / dev_factors
                if dev_sentiment_score > 0.5:
                    sentiment[coin_symbol]["development_sentiment"] = "bullish"
                elif dev_sentiment_score < -0.5:
                    sentiment[coin_symbol]["development_sentiment"] = "bearish"
                elif dev_sentiment_score > 0:
                    sentiment[coin_symbol]["development_sentiment"] = "slightly_bullish"
                elif dev_sentiment_score < 0:
                    sentiment[coin_symbol]["development_sentiment"] = "slightly_bearish"

            # Determine overall sentiment
            sentiment_values = {
                "bullish": 2,
                "slightly_bullish": 1,
                "neutral": 0,
                "slightly_bearish": -1,
                "bearish": -2
            }

            market_value = sentiment_values.get(sentiment[coin_symbol]["market_sentiment"], 0)
            social_value = sentiment_values.get(sentiment[coin_symbol]["social_sentiment"], 0)
            dev_value = sentiment_values.get(sentiment[coin_symbol]["development_sentiment"], 0)

            # Weight market sentiment more heavily
            overall_score = (market_value * 2 + social_value + dev_value) / 4

            if overall_score > 1:
                sentiment[coin_symbol]["overall_sentiment"] = "bullish"
                sentiment[coin_symbol]["confidence"] = 0.7
            elif overall_score > 0:
                sentiment[coin_symbol]["overall_sentiment"] = "slightly_bullish"
                sentiment[coin_symbol]["confidence"] = 0.6
            elif overall_score < -1:
                sentiment[coin_symbol]["overall_sentiment"] = "bearish"
                sentiment[coin_symbol]["confidence"] = 0.7
            elif overall_score < 0:
                sentiment[coin_symbol]["overall_sentiment"] = "slightly_bearish"
                sentiment[coin_symbol]["confidence"] = 0.6
            else:
                sentiment[coin_symbol]["confidence"] = 0.5

        return sentiment

    def to_onchain_context_items(self, metrics: List[Dict[str, Any]]) -> List[ContextItem]:
        """
        Convert on-chain metrics to MCP context items.

        Args:
            metrics: List of on-chain metrics

        Returns:
            List of context items
        """
        context_items = []

        for data in metrics:
            # Create a context item for each cryptocurrency
            item = ContextItem(
                id=f"onchain_{data.get('coin_id', 'unknown')}_{uuid.uuid4()}",
                type="onchain_metrics",
                content=data,
                timestamp=datetime.now(timezone.utc),
                source="coingecko",
                confidence=1.0
            )
            context_items.append(item)

        return context_items

async def main():
    """Main function for testing the fetcher."""
    fetcher = CoinGeckoOnChainFetcher()

    # Fetch on-chain metrics for Bitcoin and Ethereum
    metrics = await fetcher.fetch_all_onchain_metrics(["bitcoin", "ethereum"])

    # Analyze on-chain sentiment
    sentiment = fetcher.analyze_onchain_sentiment(metrics)

    # Convert to context items
    context_items = fetcher.to_onchain_context_items(metrics)

    # Print results
    print(f"Fetched on-chain metrics for {len(metrics)} coins")

    for data in metrics:
        print(f"\n{data.get('name', 'Unknown')} ({data.get('symbol', 'Unknown')}):")
        print(f"  Market Cap: ${data.get('market_cap_usd', 0):,.2f}")
        print(f"  24h Volume: ${data.get('total_volume_usd', 0):,.2f}")
        print(f"  Circulating Supply: {data.get('circulating_supply', 0):,.0f} ({data.get('circulating_supply_percentage', 0):.2f}%)")
        print(f"  GitHub Commits (4w): {data.get('github_commits_4_weeks', 0)}")
        print(f"  Twitter Followers: {data.get('twitter_followers', 0):,}")

    print("\nSentiment Analysis:")
    for symbol, analysis in sentiment.items():
        print(f"\n{symbol}:")
        for key, value in analysis.items():
            print(f"  {key}: {value}")

    print(f"\nGenerated {len(context_items)} context items")

if __name__ == "__main__":
    asyncio.run(main())
