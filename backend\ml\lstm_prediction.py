"""
LSTM Price Prediction Model

This module implements LSTM neural networks for cryptocurrency price prediction.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import Adam
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class LSTMPricePredictor:
    """LSTM neural network model for cryptocurrency price prediction."""
    
    def __init__(self, sequence_length: int = 10, prediction_horizon: int = 7):
        """
        Initialize the LSTM price prediction model.
        
        Args:
            sequence_length: Number of time steps to use for prediction
            prediction_horizon: Number of time steps to predict ahead
        """
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.model = None
        self.feature_scaler = MinMaxScaler()
        self.target_scaler = MinMaxScaler()
        self.feature_columns = []
        
        logger.info(f"Initialized LSTM price predictor with sequence length {sequence_length} and prediction horizon {prediction_horizon}")
    
    def _create_model(self, input_shape: Tuple[int, int]) -> Sequential:
        """
        Create the LSTM model.
        
        Args:
            input_shape: Shape of input data (sequence_length, n_features)
            
        Returns:
            Compiled Keras Sequential model
        """
        model = Sequential([
            LSTM(units=50, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(units=50, return_sequences=False),
            Dropout(0.2),
            Dense(units=25),
            Dense(units=self.prediction_horizon)
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse'
        )
        
        logger.info(f"Created LSTM model with input shape {input_shape}")
        return model
    
    def _prepare_data(self, price_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare data for LSTM model.
        
        Args:
            price_data: DataFrame with price data
            
        Returns:
            Tuple of (X, y) arrays
        """
        # Add technical indicators
        df = self._add_technical_indicators(price_data)
        
        # Drop NaN values
        df = df.dropna()
        
        # Define feature columns
        self.feature_columns = [col for col in df.columns if col != 'target']
        
        # Create target variable (future prices)
        for i in range(1, self.prediction_horizon + 1):
            df[f'target_{i}'] = df['close'].shift(-i)
        
        # Drop rows with NaN targets
        df = df.dropna()
        
        # Scale features
        features = df[self.feature_columns].values
        scaled_features = self.feature_scaler.fit_transform(features)
        
        # Scale targets
        targets = df[[f'target_{i}' for i in range(1, self.prediction_horizon + 1)]].values
        scaled_targets = self.target_scaler.fit_transform(targets)
        
        # Create sequences
        X, y = [], []
        for i in range(len(df) - self.sequence_length):
            X.append(scaled_features[i:i + self.sequence_length])
            y.append(scaled_targets[i + self.sequence_length - 1])
        
        return np.array(X), np.array(y)
    
    def _add_technical_indicators(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to price data.
        
        Args:
            price_data: DataFrame with price data
            
        Returns:
            DataFrame with added technical indicators
        """
        df = price_data.copy()
        
        # Add moving averages
        df["sma_5"] = df["close"].rolling(window=5).mean()
        df["sma_10"] = df["close"].rolling(window=10).mean()
        df["sma_20"] = df["close"].rolling(window=20).mean()
        
        # Add exponential moving averages
        df["ema_5"] = df["close"].ewm(span=5, adjust=False).mean()
        df["ema_10"] = df["close"].ewm(span=10, adjust=False).mean()
        df["ema_20"] = df["close"].ewm(span=20, adjust=False).mean()
        
        # Add price momentum
        df["momentum_5"] = df["close"] / df["close"].shift(5) - 1
        df["momentum_10"] = df["close"] / df["close"].shift(10) - 1
        
        # Add volatility
        df["volatility_5"] = df["close"].rolling(window=5).std()
        df["volatility_10"] = df["close"].rolling(window=10).std()
        
        # Add volume indicators
        if "volume" in df.columns:
            df["volume_ma_5"] = df["volume"].rolling(window=5).mean()
            df["volume_ma_10"] = df["volume"].rolling(window=10).mean()
        
        # Add price changes
        df["price_change"] = df["close"].pct_change()
        df["price_change_5"] = df["close"].pct_change(periods=5)
        
        # Add day of week (cyclical feature)
        if isinstance(df.index, pd.DatetimeIndex):
            df["day_of_week_sin"] = np.sin(2 * np.pi * df.index.dayofweek / 7)
            df["day_of_week_cos"] = np.cos(2 * np.pi * df.index.dayofweek / 7)
        
        return df
    
    def train(self, price_data: pd.DataFrame, test_size: float = 0.2, epochs: int = 50, batch_size: int = 32) -> Dict[str, Any]:
        """
        Train the LSTM model.
        
        Args:
            price_data: DataFrame with price data
            test_size: Proportion of data to use for testing
            epochs: Number of training epochs
            batch_size: Batch size for training
            
        Returns:
            Dictionary with training results
        """
        # Prepare data
        X, y = self._prepare_data(price_data)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, shuffle=False
        )
        
        # Create model
        self.model = self._create_model(input_shape=(X.shape[1], X.shape[2]))
        
        # Define early stopping
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True
        )
        
        # Train model
        logger.info(f"Training LSTM model with {len(X_train)} samples")
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=[early_stopping],
            verbose=1
        )
        
        # Evaluate model
        y_pred_train = self.model.predict(X_train)
        y_pred_test = self.model.predict(X_test)
        
        # Inverse transform predictions
        y_train_inv = self.target_scaler.inverse_transform(y_train)
        y_test_inv = self.target_scaler.inverse_transform(y_test)
        y_pred_train_inv = self.target_scaler.inverse_transform(y_pred_train)
        y_pred_test_inv = self.target_scaler.inverse_transform(y_pred_test)
        
        # Calculate metrics for each prediction horizon
        train_metrics = []
        test_metrics = []
        
        for i in range(self.prediction_horizon):
            # Training metrics
            train_rmse = np.sqrt(mean_squared_error(y_train_inv[:, i], y_pred_train_inv[:, i]))
            train_mae = mean_absolute_error(y_train_inv[:, i], y_pred_train_inv[:, i])
            train_r2 = r2_score(y_train_inv[:, i], y_pred_train_inv[:, i])
            
            # Testing metrics
            test_rmse = np.sqrt(mean_squared_error(y_test_inv[:, i], y_pred_test_inv[:, i]))
            test_mae = mean_absolute_error(y_test_inv[:, i], y_pred_test_inv[:, i])
            test_r2 = r2_score(y_test_inv[:, i], y_pred_test_inv[:, i])
            
            train_metrics.append({
                "horizon": i + 1,
                "rmse": train_rmse,
                "mae": train_mae,
                "r2": train_r2
            })
            
            test_metrics.append({
                "horizon": i + 1,
                "rmse": test_rmse,
                "mae": test_mae,
                "r2": test_r2
            })
        
        # Calculate directional accuracy
        train_dir_acc = self._calculate_directional_accuracy(y_train_inv[:, 0], y_pred_train_inv[:, 0])
        test_dir_acc = self._calculate_directional_accuracy(y_test_inv[:, 0], y_pred_test_inv[:, 0])
        
        logger.info(f"Training completed. Test RMSE: {test_metrics[0]['rmse']:.4f}, Test MAE: {test_metrics[0]['mae']:.4f}, Test R²: {test_metrics[0]['r2']:.4f}")
        
        return {
            "model_type": "lstm",
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "train_metrics": train_metrics,
            "test_metrics": test_metrics,
            "train_dir_acc": train_dir_acc,
            "test_dir_acc": test_dir_acc,
            "history": {
                "loss": history.history["loss"],
                "val_loss": history.history["val_loss"]
            }
        }
    
    def _calculate_directional_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        Calculate directional accuracy.
        
        Args:
            y_true: True values
            y_pred: Predicted values
            
        Returns:
            Directional accuracy (0.0 to 1.0)
        """
        # Calculate direction of true and predicted values
        true_direction = np.sign(np.diff(y_true))
        pred_direction = np.sign(np.diff(y_pred))
        
        # Calculate accuracy
        correct_direction = np.sum(true_direction == pred_direction)
        total_direction = len(true_direction)
        
        return correct_direction / total_direction if total_direction > 0 else 0.0
    
    def predict(self, price_data: pd.DataFrame) -> List[float]:
        """
        Make price predictions.
        
        Args:
            price_data: DataFrame with price data
            
        Returns:
            List of predicted prices for each prediction horizon
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        # Add technical indicators
        df = self._add_technical_indicators(price_data)
        
        # Drop NaN values
        df = df.dropna()
        
        # Get the last sequence
        features = df[self.feature_columns].values
        scaled_features = self.feature_scaler.transform(features)
        last_sequence = scaled_features[-self.sequence_length:]
        
        # Reshape for LSTM input
        X = np.array([last_sequence])
        
        # Make prediction
        scaled_prediction = self.model.predict(X)[0]
        
        # Inverse transform prediction
        prediction = self.target_scaler.inverse_transform(scaled_prediction.reshape(1, -1))[0]
        
        logger.info(f"Made predictions for {self.prediction_horizon} days ahead")
        return prediction.tolist()
    
    def save_model(self, filepath: str) -> None:
        """
        Save the model to a file.
        
        Args:
            filepath: Path to save the model
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        self.model.save(filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str) -> None:
        """
        Load the model from a file.
        
        Args:
            filepath: Path to load the model from
        """
        self.model = tf.keras.models.load_model(filepath)
        logger.info(f"Model loaded from {filepath}")

def create_sample_data(days: int = 500) -> pd.DataFrame:
    """
    Create sample price data for testing.
    
    Args:
        days: Number of days of data to generate
        
    Returns:
        DataFrame with sample price data
    """
    np.random.seed(42)
    
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # Create price series with trend, seasonality, and noise
    t = np.arange(len(date_range))
    
    # Trend component
    trend = 100 + 0.1 * t
    
    # Seasonality component (weekly)
    seasonality = 5 * np.sin(2 * np.pi * t / 7)
    
    # Noise component
    noise = np.random.normal(0, 3, len(date_range))
    
    # Combine components
    price = trend + seasonality + noise
    
    # Create volume
    volume = 1000000 + 500000 * np.random.random(len(date_range))
    
    # Create DataFrame
    df = pd.DataFrame({
        'open': price * 0.99,
        'high': price * 1.02,
        'low': price * 0.98,
        'close': price,
        'volume': volume
    }, index=date_range)
    
    return df

def main():
    """Main function for testing the LSTM price predictor."""
    # Create sample data
    price_data = create_sample_data(days=500)
    
    # Initialize and train model
    model = LSTMPricePredictor(sequence_length=10, prediction_horizon=7)
    results = model.train(price_data, epochs=20, batch_size=32)
    
    # Print training results
    print("\n===== TRAINING RESULTS =====")
    print(f"Model type: {results['model_type']}")
    print(f"Training samples: {results['train_samples']}")
    print(f"Testing samples: {results['test_samples']}")
    
    print("\nTest Metrics:")
    for metrics in results['test_metrics']:
        print(f"Day {metrics['horizon']}:")
        print(f"  RMSE: {metrics['rmse']:.4f}")
        print(f"  MAE: {metrics['mae']:.4f}")
        print(f"  R²: {metrics['r2']:.4f}")
    
    print(f"\nDirectional Accuracy: {results['test_dir_acc']:.4f}")
    
    # Make predictions
    predictions = model.predict(price_data)
    
    # Print predictions
    print("\n===== PRICE PREDICTIONS =====")
    last_price = price_data['close'].iloc[-1]
    print(f"Last Price: {last_price:.2f}")
    
    for i, pred in enumerate(predictions):
        change = (pred - last_price) / last_price * 100
        print(f"Day {i+1}: {pred:.2f} ({change:.2f}%)")

if __name__ == "__main__":
    main()
