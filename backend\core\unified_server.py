"""
Unified Server for MCP Trading Platform

This module consolidates all server functionality into a single FastAPI application
to eliminate duplication and improve maintainability.
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2Pass<PERSON><PERSON>ear<PERSON>, OAuth2PasswordRequestForm
import uvicorn

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import configuration
from config.settings import get_settings, Settings

# Import routers
from api.market_endpoints import router as market_router
from api.onchain_endpoints import router as onchain_router
from api.consensus_endpoints import router as consensus_router
from api.multi_timeframe_endpoints import router as multi_timeframe_router
from api.paper_trading_endpoints import router as paper_trading_router
from api.chatbot_endpoints import router as chatbot_router
from api.cc_endpoints import router as cc_router
from api.cmc_endpoints import router as cmc_router
from api.realtime_price_endpoints import router as realtime_router
from api.timeframe_endpoints import router as timeframe_router
from api.real_data_endpoints import router as real_data_router

# Import core services
from core.cache_manager import CacheManager
from core.data_manager import DataManager
from core.signal_manager import SignalManager
from core.auth_manager import AuthManager

# Configure logging
def setup_logging(settings: Settings):
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, settings.logging.level.upper()),
        format=settings.logging.format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(settings.logging.file_path) if settings.logging.file_path else logging.NullHandler()
        ]
    )

class UnifiedServer:
    """Unified server that consolidates all platform functionality."""

    def __init__(self, settings: Settings = None):
        self.settings = settings or get_settings()
        self.app = FastAPI(
            title="MCP Trading Platform API",
            description="Unified API for the MCP Trading Platform",
            version="2.0.0",
            debug=self.settings.debug
        )

        # Initialize core services
        self.cache_manager = CacheManager(self.settings.cache)
        self.data_manager = DataManager(self.settings)
        self.signal_manager = SignalManager(self.settings)
        self.auth_manager = AuthManager(self.settings.security)

        # Set up logging
        setup_logging(self.settings)
        self.logger = logging.getLogger(__name__)

        # Configure the application
        self._configure_middleware()
        self._register_routes()
        self._setup_static_files()

    def _configure_middleware(self):
        """Configure middleware for the application."""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=self.settings.server.cors_origins,
            allow_credentials=True,
            allow_methods=self.settings.server.cors_methods,
            allow_headers=["*"],
        )

        # Add custom middleware for request logging, rate limiting, etc.
        @self.app.middleware("http")
        async def log_requests(request, call_next):
            start_time = datetime.now()
            response = await call_next(request)
            process_time = (datetime.now() - start_time).total_seconds()

            self.logger.info(
                f"{request.method} {request.url.path} - "
                f"Status: {response.status_code} - "
                f"Time: {process_time:.3f}s"
            )
            return response

    def _register_routes(self):
        """Register all API routes."""

        # Health check endpoint
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {
                "status": "healthy",
                "timestamp": datetime.now(),
                "version": "2.0.0",
                "environment": self.settings.environment
            }

        # Root endpoint
        @self.app.get("/")
        async def root():
            """Root endpoint with API information."""
            return {
                "name": "MCP Trading Platform API",
                "version": "2.0.0",
                "status": "running",
                "environment": self.settings.environment,
                "endpoints": {
                    "health": "/health",
                    "docs": "/docs",
                    "market": "/api/market",
                    "onchain": "/api/onchain",
                    "consensus": "/api/consensus",
                    "signals": "/api/signals",
                    "trading": "/api/trading"
                }
            }

        # Authentication endpoints
        @self.app.post("/auth/token")
        async def login(form_data: OAuth2PasswordRequestForm = Depends()):
            """Authenticate user and return access token."""
            return await self.auth_manager.authenticate_user(form_data.username, form_data.password)

        # Register API routers with prefixes
        self.app.include_router(market_router, prefix="/api/market", tags=["market"])
        self.app.include_router(onchain_router, prefix="/api/onchain", tags=["onchain"])
        self.app.include_router(consensus_router, prefix="/api/consensus", tags=["consensus"])
        self.app.include_router(multi_timeframe_router, prefix="/api/multi-timeframe", tags=["multi-timeframe"])
        self.app.include_router(paper_trading_router, prefix="/api/trading", tags=["trading"])
        self.app.include_router(chatbot_router, prefix="/api/chatbot", tags=["chatbot"])
        self.app.include_router(cc_router, prefix="/api/cc", tags=["cryptocompare"])
        self.app.include_router(cmc_router, prefix="/api/cmc", tags=["coinmarketcap"])
        self.app.include_router(realtime_router, prefix="/api/realtime", tags=["realtime"])
        self.app.include_router(timeframe_router, prefix="/api/timeframe", tags=["timeframe"])
        self.app.include_router(real_data_router, prefix="/api/real-data", tags=["real-data"])

        self.logger.info("All API routes registered successfully")

    def _setup_static_files(self):
        """Set up static file serving."""
        # Serve frontend static files if in development mode
        if self.settings.is_development():
            frontend_path = os.path.join(os.path.dirname(__file__), "..", "..", "frontend", "public")
            if os.path.exists(frontend_path):
                self.app.mount("/static", StaticFiles(directory=frontend_path), name="static")
                self.logger.info(f"Serving static files from {frontend_path}")

    async def startup(self):
        """Startup tasks."""
        self.logger.info("Starting MCP Trading Platform Unified Server...")

        # Check if we're using real data
        use_real_data = os.getenv('USE_REAL_DATA', 'true').lower() == 'true'

        if use_real_data:
            self.logger.info("🔴 REAL DATA MODE - Using live API data")
            # Verify API keys are available
            api_keys_available = []
            if self.settings.api.coingecko_api_key:
                api_keys_available.append("CoinGecko")
            if self.settings.api.cryptocompare_api_key:
                api_keys_available.append("CryptoCompare")
            if self.settings.api.coinmarketcap_api_key:
                api_keys_available.append("CoinMarketCap")
            if self.settings.api.binance_api_key:
                api_keys_available.append("Binance")

            if api_keys_available:
                self.logger.info(f"✅ Real data sources available: {', '.join(api_keys_available)}")
            else:
                self.logger.warning("⚠️  No API keys found - falling back to demo data")
        else:
            self.logger.info("🟡 DEMO MODE - Using demo/cached data")

        # Initialize core services
        await self.cache_manager.initialize()
        await self.data_manager.initialize()
        await self.signal_manager.initialize()

        self.logger.info("Unified server startup completed successfully")

    async def shutdown(self):
        """Shutdown tasks."""
        self.logger.info("Shutting down MCP Trading Platform Unified Server...")

        # Clean up core services
        await self.cache_manager.cleanup()
        await self.data_manager.cleanup()
        await self.signal_manager.cleanup()

        self.logger.info("Unified server shutdown completed")

    def run(self):
        """Run the unified server."""
        uvicorn.run(
            self.app,
            host=self.settings.server.host,
            port=self.settings.server.port,
            workers=self.settings.server.workers,
            reload=self.settings.server.reload,
            log_level=self.settings.logging.level.lower()
        )

# Global server instance
unified_server = None

def get_server() -> UnifiedServer:
    """Get the global unified server instance."""
    global unified_server
    if unified_server is None:
        unified_server = UnifiedServer()
    return unified_server

def create_app(settings: Settings = None) -> FastAPI:
    """Create and configure the FastAPI application."""
    server = UnifiedServer(settings)

    # Add startup and shutdown event handlers
    @server.app.on_event("startup")
    async def startup_event():
        await server.startup()

    @server.app.on_event("shutdown")
    async def shutdown_event():
        await server.shutdown()

    return server.app

if __name__ == "__main__":
    # Run the unified server
    server = get_server()
    server.run()
