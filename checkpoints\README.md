# MCP Trading Platform Checkpoints

This directory contains checkpoints of various implementations for the MCP Trading Platform.

## Available Checkpoints

### Charts Implementation (April 22, 2025)
- Location: `charts-implementation/`
- Description: Implementation of live trading charts with TradingView integration
- Features: Real-time charts, multiple timeframes, technical indicators

## How to Use Checkpoints

Each checkpoint directory contains:
- The relevant implementation files
- A README.md with details about the implementation
- Any scripts or utilities related to the feature

To restore a checkpoint:
1. Copy the files from the checkpoint directory to their appropriate locations
2. Follow the instructions in the checkpoint's README.md file

## Creating New Checkpoints

When implementing a new feature:
1. Create a new directory in `checkpoints/` with a descriptive name
2. Copy the relevant implementation files to the new directory
3. Create a README.md file with details about the implementation
4. Document any dependencies or special instructions
