"""
Simple script to fetch real-time cryptocurrency prices from CryptoCompare API
"""

import requests
import json
from datetime import datetime

# API configuration
API_KEY = "ed7f849e1c1b383f5ee903d4b122465a9252a680ed9dabf8f4758eff846ca368"
BASE_URL = "https://min-api.cryptocompare.com"

def get_current_prices(symbols=["BTC", "ETH", "SOL"], currency="USD"):
    """
    Get current prices for specified cryptocurrencies.
    
    Args:
        symbols: List of cryptocurrency symbols
        currency: Currency to convert to
        
    Returns:
        Dictionary with price data
    """
    url = f"{BASE_URL}/data/pricemultifull"
    params = {
        "fsyms": ",".join(symbols),
        "tsyms": currency
    }
    headers = {
        "authorization": f"Apikey {API_KEY}"
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            return data
        else:
            print(f"Error: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Error fetching prices: {str(e)}")
        return None

def format_price_data(data):
    """
    Format price data into a readable format.
    
    Args:
        data: Price data from CryptoCompare API
        
    Returns:
        Formatted price data
    """
    if not data or "RAW" not in data:
        return "Error: No data available"
    
    result = []
    raw_data = data["RAW"]
    display_data = data["DISPLAY"]
    
    for symbol in raw_data:
        for currency in raw_data[symbol]:
            raw = raw_data[symbol][currency]
            display = display_data[symbol][currency]
            
            price = raw["PRICE"]
            change_24h_pct = raw["CHANGEPCT24HOUR"]
            market_cap = raw["MKTCAP"]
            volume_24h = raw["VOLUME24HOUR"]
            
            formatted_price = display["PRICE"]
            formatted_change = display["CHANGEPCT24HOUR"]
            formatted_market_cap = display["MKTCAP"]
            formatted_volume = display["VOLUME24HOUR"]
            
            change_direction = "up" if change_24h_pct >= 0 else "down"
            
            result.append({
                "symbol": symbol,
                "currency": currency,
                "price": price,
                "price_formatted": formatted_price,
                "change_24h_pct": change_24h_pct,
                "change_24h_formatted": formatted_change,
                "change_direction": change_direction,
                "market_cap": market_cap,
                "market_cap_formatted": formatted_market_cap,
                "volume_24h": volume_24h,
                "volume_24h_formatted": formatted_volume,
                "last_updated": datetime.now().isoformat()
            })
    
    return result

def save_prices_to_file(prices, filename="crypto_prices.json"):
    """
    Save price data to a JSON file.
    
    Args:
        prices: Price data
        filename: Output filename
    """
    with open(filename, "w") as f:
        json.dump(prices, f, indent=2)
    print(f"Prices saved to {filename}")

def main():
    """Main function."""
    print("Fetching cryptocurrency prices...")
    data = get_current_prices()
    if data:
        prices = format_price_data(data)
        print("\nCurrent Cryptocurrency Prices:")
        for price in prices:
            print(f"\n{price['symbol']}/{price['currency']}:")
            print(f"  Price: {price['price_formatted']}")
            print(f"  24h Change: {price['change_24h_formatted']} ({price['change_direction']})")
            print(f"  Market Cap: {price['market_cap_formatted']}")
            print(f"  24h Volume: {price['volume_24h_formatted']}")
        
        # Save to file
        save_prices_to_file(prices)
    else:
        print("Failed to fetch prices.")

if __name__ == "__main__":
    main()
