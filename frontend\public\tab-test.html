<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Functionality Test | Project Ruby</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/ruby-core.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: var(--ruby-bg-dark);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .test-title {
            color: var(--ruby-gold);
            font-size: 1.3em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .test-title i {
            margin-right: 10px;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .test-link {
            display: block;
            padding: 15px;
            background: var(--ruby-bg-light);
            border: 1px solid var(--ruby-border);
            border-radius: 8px;
            text-decoration: none;
            color: var(--ruby-text-primary);
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .test-link:hover {
            background: var(--ruby-gold);
            color: var(--ruby-bg-dark);
            transform: translateY(-2px);
        }
        
        .test-link i {
            display: block;
            font-size: 1.5em;
            margin-bottom: 8px;
            color: var(--ruby-gold);
        }
        
        .test-link:hover i {
            color: var(--ruby-bg-dark);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-good { background: #22c55e; }
        .status-warning { background: #f59e0b; }
        .status-error { background: #ef4444; }
        
        .test-instructions {
            background: var(--ruby-bg-light);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid var(--ruby-gold);
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .checklist li::before {
            content: "☐";
            margin-right: 10px;
            font-size: 1.2em;
            color: var(--ruby-gold);
        }
        
        .checklist li.checked::before {
            content: "✅";
        }
    </style>
</head>
<body class="ruby-theme">
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: var(--ruby-gold); margin-bottom: 10px;">
                <i class="fas fa-vial"></i> Tab Functionality Test
            </h1>
            <p style="color: var(--ruby-text-secondary);">
                Test all tabs and navigation to ensure everything works without crashes
            </p>
        </header>

        <div class="test-instructions">
            <h3 style="color: var(--ruby-gold); margin-bottom: 10px;">
                <i class="fas fa-clipboard-list"></i> Testing Instructions
            </h3>
            <p>Click on each page below and test all tabs. Check off items as you verify they work:</p>
            <ul class="checklist" id="test-checklist">
                <li>All navigation links work (no 404 errors)</li>
                <li>Analysis page: All 4 tabs switch properly (Signals, Sentiment, Consensus, Timeframes)</li>
                <li>Portfolio page: All 4 tabs switch properly (Overview, Trading, History, Management)</li>
                <li>Trading interface: Coin selector buttons work</li>
                <li>Trading interface: Buy/Sell buttons show confirmation</li>
                <li>No JavaScript errors in browser console</li>
                <li>All pages load without crashes</li>
                <li>Navigation between pages is seamless</li>
            </ul>
        </div>

        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-mouse-pointer"></i> 
                Main Pages Test
                <span class="status-indicator status-good"></span>
            </h2>
            <div class="test-links">
                <a href="dashboard.html" class="test-link">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="trading.html" class="test-link">
                    <i class="fas fa-chart-line"></i>
                    Trading
                </a>
                <a href="analysis.html" class="test-link">
                    <i class="fas fa-brain"></i>
                    Analysis (4 Tabs)
                </a>
                <a href="charts.html" class="test-link">
                    <i class="fas fa-chart-candlestick"></i>
                    Charts
                </a>
                <a href="predictions.html" class="test-link">
                    <i class="fas fa-crystal-ball"></i>
                    Predictions
                </a>
                <a href="chatbot.html" class="test-link">
                    <i class="fas fa-robot"></i>
                    Assistant
                </a>
                <a href="portfolio.html" class="test-link">
                    <i class="fas fa-wallet"></i>
                    Portfolio (4 Tabs)
                </a>
                <a href="settings.html" class="test-link">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-tabs"></i> 
                Tab-Specific Tests
                <span class="status-indicator status-warning"></span>
            </h2>
            <div class="test-links">
                <a href="analysis.html#signals" class="test-link">
                    <i class="fas fa-signal"></i>
                    Analysis → Signals Tab
                </a>
                <a href="analysis.html#sentiment" class="test-link">
                    <i class="fas fa-comments"></i>
                    Analysis → Sentiment Tab
                </a>
                <a href="analysis.html#consensus" class="test-link">
                    <i class="fas fa-brain"></i>
                    Analysis → Consensus Tab
                </a>
                <a href="analysis.html#timeframes" class="test-link">
                    <i class="fas fa-layer-group"></i>
                    Analysis → Timeframes Tab
                </a>
                <a href="portfolio.html#overview" class="test-link">
                    <i class="fas fa-chart-pie"></i>
                    Portfolio → Overview Tab
                </a>
                <a href="portfolio.html#trading" class="test-link">
                    <i class="fas fa-exchange-alt"></i>
                    Portfolio → Trading Tab
                </a>
                <a href="portfolio.html#history" class="test-link">
                    <i class="fas fa-history"></i>
                    Portfolio → History Tab
                </a>
                <a href="portfolio.html#management" class="test-link">
                    <i class="fas fa-coins"></i>
                    Portfolio → Management Tab
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-tools"></i> 
                Interactive Features Test
            </h2>
            <div style="color: var(--ruby-text-secondary); line-height: 1.6;">
                <p><strong>Portfolio Trading Interface:</strong></p>
                <ul style="margin-left: 20px;">
                    <li>Click different coin buttons (BTC, ETH, SOL, ADA) - price should update</li>
                    <li>Enter an amount and click Buy/Sell - should show confirmation dialog</li>
                    <li>Try with empty amount - should show validation error</li>
                </ul>
                
                <p><strong>Analysis Tabs:</strong></p>
                <ul style="margin-left: 20px;">
                    <li>Click each tab - content should switch without page reload</li>
                    <li>Timeframes tab should show multi-timeframe data</li>
                    <li>Console should log tab switching (check browser dev tools)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-check-circle"></i> 
                Test Results
            </h2>
            <div id="test-results">
                <p style="color: var(--ruby-text-secondary);">
                    Click the checkboxes above as you complete each test. 
                    All items should be checked for a successful test.
                </p>
                <button onclick="generateTestReport()" style="
                    background: var(--ruby-gold); 
                    color: var(--ruby-bg-dark); 
                    border: none; 
                    padding: 10px 20px; 
                    border-radius: 5px; 
                    cursor: pointer; 
                    margin-top: 15px;
                ">
                    Generate Test Report
                </button>
            </div>
        </div>
    </div>

    <script>
        // Make checklist interactive
        document.querySelectorAll('.checklist li').forEach(item => {
            item.addEventListener('click', () => {
                item.classList.toggle('checked');
                updateTestStatus();
            });
        });

        function updateTestStatus() {
            const total = document.querySelectorAll('.checklist li').length;
            const checked = document.querySelectorAll('.checklist li.checked').length;
            const percentage = Math.round((checked / total) * 100);
            
            console.log(`Test progress: ${checked}/${total} (${percentage}%)`);
        }

        function generateTestReport() {
            const total = document.querySelectorAll('.checklist li').length;
            const checked = document.querySelectorAll('.checklist li.checked').length;
            const percentage = Math.round((checked / total) * 100);
            
            let status = 'FAILED';
            if (percentage === 100) status = 'PASSED';
            else if (percentage >= 80) status = 'MOSTLY PASSED';
            
            const report = `
🧪 TAB FUNCTIONALITY TEST REPORT
================================
Status: ${status}
Progress: ${checked}/${total} tests completed (${percentage}%)
Date: ${new Date().toLocaleString()}

${percentage === 100 ? '✅ All tabs working correctly!' : 
  percentage >= 80 ? '⚠️ Most tabs working, minor issues detected' : 
  '❌ Significant issues detected, needs fixing'}
            `;
            
            alert(report);
            console.log(report);
        }

        // Auto-check URL hash for tab testing
        if (window.location.hash) {
            console.log('Testing tab:', window.location.hash);
        }
    </script>
</body>
</html>
