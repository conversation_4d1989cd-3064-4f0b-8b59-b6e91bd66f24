"""
Simple FastAPI server for the chatbot
"""

import json
import re
import random
from datetime import datetime
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional

# Create FastAPI app
app = FastAPI(title="Chatbot API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]

class ChatResponse(BaseModel):
    response: str
    data: Optional[Dict[str, Any]] = None

# Knowledge base for common questions
KNOWLEDGE_BASE = {
    "greeting": [
        "Hello! I'm your Project Ruby Trading Assistant. How can I help you with cryptocurrency trading today?",
        "Hi there! I'm here to help with your crypto trading questions. What would you like to know?",
        "Welcome to Project Ruby! I can provide information about crypto prices, trading signals, and on-chain metrics."
    ],
    "capabilities": [
        "I can help you with:\n\n- Current cryptocurrency prices and market data\n\n- Trading signals and recommendations\n\n- On-chain metrics and analysis\n\n- Technical indicators and chart patterns\n\n- Historical price data and trends\n\n- AI consensus predictions from multiple models\n\n- Multi-timeframe analysis across different time periods"
    ],
    "about_platform": [
        "Project Ruby is an AI-driven trading platform that combines market data, on-chain metrics, and technical analysis to provide trading signals and insights for cryptocurrencies."
    ],
    "about_consensus": [
        "The AI Consensus System combines predictions from multiple AI models with different architectures (LSTM, CNN, GRU, and Transformer) to provide more robust trading signals. By analyzing how models agree or disagree, we can identify stronger signals and potential market uncertainty. You can view the consensus predictions on the Consensus page."
    ],
    "about_timeframes": [
        "The Multi-Timeframe Analysis engine analyzes cryptocurrency price data across multiple timeframes (5m, 15m, 1h, 4h, 1d, 1w) to generate more reliable trading signals. By confirming patterns across different timeframes, we can filter out noise and identify stronger entry and exit points. You can view the multi-timeframe analysis on the Timeframes page."
    ]
}

# Mock data for cryptocurrencies
CRYPTO_DATA = {
    "BTC": {
        "name": "Bitcoin",
        "price": 65432.10,
        "price_change_24h": 2.5,
        "market_cap": 1250000000000,
        "volume_24h": **********0,
        "signal": {
            "1h": {
                "direction": "LONG",
                "strength": "75%",
                "confidence": "High",
                "indicators": {
                    "technical": {
                        "rsi": "Bullish (68)",
                        "macd": "Bullish",
                        "ma_cross": "Golden Cross"
                    },
                    "onchain": {
                        "network_health": "bullish",
                        "active_addresses_trend": "increasing",
                        "transaction_count_trend": "increasing"
                    }
                }
            }
        },
        "metrics": {
            "active_addresses": {
                "value": 1200000,
                "trend": "bullish"
            },
            "transaction_volume": {
                "value": 12000000000,
                "trend": "bullish"
            },
            "transaction_count": {
                "value": 350000,
                "trend": "neutral"
            }
        }
    },
    "ETH": {
        "name": "Ethereum",
        "price": 3456.78,
        "price_change_24h": 1.8,
        "market_cap": 420000000000,
        "volume_24h": 18000000000,
        "signal": {
            "1h": {
                "direction": "NEUTRAL",
                "strength": "55%",
                "confidence": "Medium",
                "indicators": {
                    "technical": {
                        "rsi": "Neutral (52)",
                        "macd": "Neutral",
                        "ma_cross": "No Cross"
                    },
                    "onchain": {
                        "network_health": "neutral",
                        "active_addresses_trend": "stable",
                        "transaction_count_trend": "increasing"
                    }
                }
            }
        },
        "metrics": {
            "active_addresses": {
                "value": 650000,
                "trend": "neutral"
            },
            "transaction_volume": {
                "value": **********,
                "trend": "neutral"
            },
            "transaction_count": {
                "value": 1200000,
                "trend": "bullish"
            }
        }
    },
    "SOL": {
        "name": "Solana",
        "price": 123.45,
        "price_change_24h": -1.2,
        "market_cap": 52000000000,
        "volume_24h": **********,
        "signal": {
            "1h": {
                "direction": "SHORT",
                "strength": "65%",
                "confidence": "Medium",
                "indicators": {
                    "technical": {
                        "rsi": "Bearish (32)",
                        "macd": "Bearish",
                        "ma_cross": "Death Cross"
                    },
                    "onchain": {
                        "network_health": "bearish",
                        "active_addresses_trend": "decreasing",
                        "transaction_count_trend": "stable"
                    }
                }
            }
        },
        "metrics": {
            "active_addresses": {
                "value": 180000,
                "trend": "bearish"
            },
            "transaction_volume": {
                "value": **********,
                "trend": "bearish"
            },
            "transaction_count": {
                "value": 60000000,
                "trend": "neutral"
            }
        }
    }
}

def get_coin_from_message(message):
    """Extract cryptocurrency name or symbol from message."""
    # Common cryptocurrency names and their variations
    coin_patterns = {
        "BTC": r"\b(btc|bitcoin|bitcoins)\b",
        "ETH": r"\b(eth|ethereum|ether)\b",
        "SOL": r"\b(sol|solana)\b"
    }

    for symbol, pattern in coin_patterns.items():
        if re.search(pattern, message.lower()):
            return symbol

    return None

def format_price_response(coin, price_data):
    """Format price data into a readable response."""
    if not price_data:
        return f"I'm sorry, I couldn't find price data for {coin}."

    price = price_data["price"]
    change_24h = price_data["price_change_24h"]
    market_cap = price_data["market_cap"]
    volume = price_data["volume_24h"]

    # Determine if price is up or down
    change_24h_text = "up" if change_24h > 0 else "down"

    response = f"The current price of {coin} is ${price:,.2f}, {change_24h_text} {abs(change_24h):.2f}% in the last 24 hours. "
    response += f"It has a market cap of ${market_cap/**********:.2f} billion and 24-hour trading volume of ${volume/**********:.2f} billion."

    return response

def format_signal_response(coin, signal_data):
    """Format trading signal into a readable response."""
    if not signal_data:
        return f"I'm sorry, I couldn't find trading signals for {coin}."

    # Get 1-hour signal
    signal = signal_data["1h"]
    direction = signal.get("direction", "NEUTRAL")
    strength = signal.get("strength", "Moderate (50%)")

    # Get indicators
    indicators = signal.get("indicators", {})
    technical = indicators.get("technical", {})
    onchain = indicators.get("onchain", {})

    rsi = technical.get("rsi", "Neutral")
    macd = technical.get("macd", "Neutral")
    network_health = onchain.get("network_health", "neutral")
    addresses_trend = onchain.get("active_addresses_trend", "stable")

    # Format response
    response = f"The current trading signal for {coin} is {direction} with {strength} strength. "

    if direction == "LONG":
        response += "Technical indicators suggest a bullish trend. "
    elif direction == "SHORT":
        response += "Technical indicators suggest a bearish trend. "
    else:
        response += "Technical indicators are neutral. "

    response += f"RSI is {rsi} and MACD is {macd}. "

    # Add on-chain insights
    response += f"On-chain metrics show {network_health} network health with {addresses_trend} active addresses."

    return response

def format_onchain_response(coin, metrics):
    """Format on-chain metrics into a readable response."""
    if not metrics:
        return f"I'm sorry, I couldn't find on-chain metrics for {coin}."

    active_addresses = metrics.get("active_addresses", {})
    transaction_volume = metrics.get("transaction_volume", {})
    transaction_count = metrics.get("transaction_count", {})

    addresses_value = active_addresses.get("value", 0)
    addresses_trend = active_addresses.get("trend", "neutral")

    volume_value = transaction_volume.get("value", 0)
    volume_trend = transaction_volume.get("trend", "neutral")

    count_value = transaction_count.get("value", 0)
    count_trend = transaction_count.get("trend", "neutral")

    # Format numbers
    formatted_addresses = f"{addresses_value:,}"
    formatted_volume = f"${volume_value/**********:.2f} billion"
    formatted_count = f"{count_value:,}"

    # Format trends
    trend_words = {
        "bullish": "increasing",
        "strongly_bullish": "rapidly increasing",
        "bearish": "decreasing",
        "strongly_bearish": "rapidly decreasing",
        "neutral": "stable"
    }

    addresses_trend_word = trend_words.get(addresses_trend, "stable")
    volume_trend_word = trend_words.get(volume_trend, "stable")
    count_trend_word = trend_words.get(count_trend, "stable")

    response = f"On-chain metrics for {coin} show {formatted_addresses} active addresses ({addresses_trend_word}), "
    response += f"{formatted_volume} in transaction volume ({volume_trend_word}), and "
    response += f"{formatted_count} transactions ({count_trend_word})."

    return response

def process_message(message):
    """Process a user message and generate a response."""
    message_lower = message.lower()

    # Check for greetings
    if re.search(r'\b(hi|hello|hey|greetings)\b', message_lower):
        return {"response": random.choice(KNOWLEDGE_BASE["greeting"])}

    # Check for capability questions
    if re.search(r'\b(what can you do|help me|capabilities|features)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["capabilities"][0]}

    # Check for platform questions
    if re.search(r'\b(what is|about|platform|ruby)\b', message_lower) and re.search(r'\b(ruby|platform|this)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["about_platform"][0]}

    # Check for consensus questions
    if re.search(r'\b(consensus|multiple models|ai models|model agreement)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["about_consensus"][0]}

    # Check for timeframe questions
    if re.search(r'\b(timeframe|timeframes|multiple timeframes|time frames)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["about_timeframes"][0]}

    # Check for price questions
    if re.search(r'\b(price|worth|value|cost|rate)\b', message_lower):
        coin = get_coin_from_message(message)
        if coin and coin in CRYPTO_DATA:
            price_data = {
                "price": CRYPTO_DATA[coin]["price"],
                "price_change_24h": CRYPTO_DATA[coin]["price_change_24h"],
                "market_cap": CRYPTO_DATA[coin]["market_cap"],
                "volume_24h": CRYPTO_DATA[coin]["volume_24h"]
            }
            response = format_price_response(coin, price_data)
            return {"response": response, "data": {"type": "price", "coin": coin, "price_data": price_data}}

    # Check for trading signal questions
    if re.search(r'\b(signal|recommendation|trade|buy|sell|long|short)\b', message_lower):
        coin = get_coin_from_message(message)
        if coin and coin in CRYPTO_DATA:
            signal_data = CRYPTO_DATA[coin]["signal"]
            response = format_signal_response(coin, signal_data)
            return {"response": response, "data": {"type": "signal", "coin": coin, "signal_data": signal_data}}

    # Check for on-chain metrics questions
    if re.search(r'\b(on-chain|onchain|blockchain|network|metrics)\b', message_lower):
        coin = get_coin_from_message(message)
        if coin and coin in CRYPTO_DATA:
            metrics = CRYPTO_DATA[coin]["metrics"]
            response = format_onchain_response(coin, metrics)
            return {"response": response, "data": {"type": "onchain", "coin": coin, "metrics": metrics}}

    # Check for general coin questions
    coin = get_coin_from_message(message)
    if coin and coin in CRYPTO_DATA:
        # Get price data
        price_data = {
            "price": CRYPTO_DATA[coin]["price"],
            "price_change_24h": CRYPTO_DATA[coin]["price_change_24h"],
            "market_cap": CRYPTO_DATA[coin]["market_cap"],
            "volume_24h": CRYPTO_DATA[coin]["volume_24h"]
        }
        price_response = format_price_response(coin, price_data)

        # Get trading signal
        signal_data = CRYPTO_DATA[coin]["signal"]
        signal_response = format_signal_response(coin, signal_data)

        # Combine responses
        response = f"{price_response}\n\n{signal_response}"

        return {
            "response": response,
            "data": {
                "type": "combined",
                "coin": coin,
                "price_data": price_data,
                "signal_data": signal_data
            }
        }

    # Default response
    return {
        "response": "I'm not sure I understand your question. You can ask me about cryptocurrency prices, trading signals, or on-chain metrics. For example, 'What's the price of Bitcoin?' or 'What's the trading signal for Ethereum?'"
    }

@app.post("/api/chatbot/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Process a chat message and generate a response.
    """
    try:
        # Get the last user message
        last_message = None
        for message in reversed(request.messages):
            if message.role == "user":
                last_message = message.content
                break

        if not last_message:
            raise HTTPException(status_code=400, detail="No user message found")

        # Process the message
        result = process_message(last_message)

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/health")
async def health_check():
    """Check if the chatbot API is healthy."""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
