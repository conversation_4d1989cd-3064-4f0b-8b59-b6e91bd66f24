"""
Real-time Price API Endpoints

This module provides API endpoints for accessing real-time cryptocurrency price data.
"""

import os
import json
import logging
import time
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional, Any

# Import clients
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from data_aggregator.cryptocompare_client import CryptoCompareClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('realtime_price_api')

# Create router
router = APIRouter(
    prefix="/api/realtime",
    tags=["realtime"],
    responses={404: {"description": "Not found"}},
)

# Initialize client
cryptocompare_client = CryptoCompareClient()

# Data paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')
os.makedirs(DATA_DIR, exist_ok=True)

# In-memory cache for latest prices
price_cache = {
    "BTC": {"price": 97350.42, "last_updated": datetime.now().isoformat()},
    "ETH": {"price": 3485.75, "last_updated": datetime.now().isoformat()},
    "SOL": {"price": 248.65, "last_updated": datetime.now().isoformat()},
    "ADA": {"price": 0.62, "last_updated": datetime.now().isoformat()},
    "BNB": {"price": 615.23, "last_updated": datetime.now().isoformat()}
}

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.update_task_running = False

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket client connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info(f"WebSocket client disconnected. Total connections: {len(self.active_connections)}")

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error sending message to WebSocket client: {str(e)}")

manager = ConnectionManager()

# Background task to update prices
async def update_prices_task():
    """Background task to update cryptocurrency prices periodically."""
    while True:
        try:
            # Update prices for all supported coins
            for symbol in ["BTC", "ETH", "SOL", "ADA", "BNB"]:
                try:
                    # Get price data from CryptoCompare
                    price_data = cryptocompare_client.get_price(symbol)
                    
                    if price_data and symbol in price_data and "USD" in price_data[symbol]:
                        # Extract price
                        price = price_data[symbol]["USD"]["PRICE"]
                        
                        # Update cache
                        price_cache[symbol] = {
                            "price": price,
                            "last_updated": datetime.now().isoformat()
                        }
                        
                        # Save to file
                        save_price_to_file(symbol, price)
                        
                        logger.info(f"Updated price for {symbol}: ${price}")
                    else:
                        logger.warning(f"Failed to get price data for {symbol}")
                except Exception as e:
                    logger.error(f"Error updating price for {symbol}: {str(e)}")
            
            # Broadcast updated prices to all connected clients
            await manager.broadcast(json.dumps({
                "type": "price_update",
                "data": price_cache,
                "timestamp": datetime.now().isoformat()
            }))
            
            # Wait before next update
            await asyncio.sleep(60)  # Update every 60 seconds
        except Exception as e:
            logger.error(f"Error in price update task: {str(e)}")
            await asyncio.sleep(10)  # Wait a bit before retrying

def save_price_to_file(symbol, price):
    """Save price data to a JSON file."""
    try:
        # Create directory for symbol if it doesn't exist
        symbol_dir = os.path.join(DATA_DIR, symbol.lower())
        os.makedirs(symbol_dir, exist_ok=True)
        
        # Create file path
        file_path = os.path.join(symbol_dir, "realtime_price.json")
        
        # Save data
        data = {
            "symbol": symbol,
            "price": price,
            "last_updated": datetime.now().isoformat()
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
            
        return True
    except Exception as e:
        logger.error(f"Error saving price data for {symbol}: {str(e)}")
        return False

# Update the signals file with current prices
def update_signals_file():
    """Update the signals file with current prices."""
    try:
        # Read the existing signals file
        signals_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend', 'public', 'data', 'updated_signals.json')
        
        if not os.path.exists(signals_file):
            logger.error(f"Signals file not found: {signals_file}")
            return False
        
        with open(signals_file, 'r') as f:
            signals_data = json.load(f)
        
        # Update prices and timestamps
        current_time = datetime.now().isoformat()
        
        for symbol, data in price_cache.items():
            if symbol in signals_data:
                signals_data[symbol]["price"] = data["price"]
                signals_data[symbol]["timestamp"] = current_time
        
        # Save updated file
        with open(signals_file, 'w') as f:
            json.dump(signals_data, f, indent=2)
            
        logger.info(f"Updated signals file with current prices")
        return True
    except Exception as e:
        logger.error(f"Error updating signals file: {str(e)}")
        return False

# Endpoints
@router.get("/prices")
async def get_all_prices():
    """Get current prices for all supported cryptocurrencies."""
    return {
        "prices": price_cache,
        "timestamp": datetime.now().isoformat()
    }

@router.get("/price/{symbol}")
async def get_price(symbol: str):
    """
    Get the current price for a specific cryptocurrency.
    
    Args:
        symbol: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    symbol = symbol.upper()
    
    if symbol not in price_cache:
        # Try to get price from CryptoCompare
        try:
            price_data = cryptocompare_client.get_price(symbol)
            
            if price_data and symbol in price_data and "USD" in price_data[symbol]:
                # Extract price
                price = price_data[symbol]["USD"]["PRICE"]
                
                # Update cache
                price_cache[symbol] = {
                    "price": price,
                    "last_updated": datetime.now().isoformat()
                }
                
                # Save to file
                save_price_to_file(symbol, price)
            else:
                return {"error": f"Price data not found for {symbol}"}
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {str(e)}")
            return {"error": f"Failed to get price for {symbol}: {str(e)}"}
    
    return {
        "symbol": symbol,
        "price": price_cache[symbol]["price"],
        "last_updated": price_cache[symbol]["last_updated"]
    }

@router.get("/update")
async def update_all_prices(background_tasks: BackgroundTasks):
    """
    Manually trigger an update of all cryptocurrency prices.
    """
    try:
        # Update prices for all supported coins
        updated_coins = []
        
        for symbol in ["BTC", "ETH", "SOL", "ADA", "BNB"]:
            try:
                # Get price data from CryptoCompare
                price_data = cryptocompare_client.get_price(symbol)
                
                if price_data and symbol in price_data and "USD" in price_data[symbol]:
                    # Extract price
                    price = price_data[symbol]["USD"]["PRICE"]
                    
                    # Update cache
                    price_cache[symbol] = {
                        "price": price,
                        "last_updated": datetime.now().isoformat()
                    }
                    
                    # Save to file
                    save_price_to_file(symbol, price)
                    
                    updated_coins.append(symbol)
                    logger.info(f"Updated price for {symbol}: ${price}")
                else:
                    logger.warning(f"Failed to get price data for {symbol}")
            except Exception as e:
                logger.error(f"Error updating price for {symbol}: {str(e)}")
        
        # Update signals file in the background
        background_tasks.add_task(update_signals_file)
        
        return {
            "status": "success",
            "updated_coins": updated_coins,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error updating prices: {str(e)}")
        return {"error": f"Failed to update prices: {str(e)}"}

# WebSocket endpoint for real-time price updates
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    
    # Start the update task if not already running
    if not manager.update_task_running and len(manager.active_connections) == 1:
        import asyncio
        asyncio.create_task(update_prices_task())
        manager.update_task_running = True
    
    try:
        # Send initial prices
        await websocket.send_text(json.dumps({
            "type": "price_update",
            "data": price_cache,
            "timestamp": datetime.now().isoformat()
        }))
        
        # Keep connection alive
        while True:
            data = await websocket.receive_text()
            # Process any client messages if needed
            if data == "ping":
                await websocket.send_text("pong")
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        manager.disconnect(websocket)
