"""
Economic Indicators Data Fetcher (Mini Version)

This module fetches economic indicators data from various sources to provide
macroeconomic context for trading decisions.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class EconomicIndicatorsFetcher:
    """Fetches economic indicators data from various sources."""

    def __init__(self, api_keys: Dict[str, str] = None):
        """
        Initialize the economic indicators fetcher.
        
        Args:
            api_keys: Dictionary of API keys for different services
        """
        self.api_keys = api_keys or {}
        logger.info("Economic indicators fetcher initialized")

    async def fetch_inflation_data(self) -> Dict[str, Any]:
        """
        Fetch inflation data (CPI) from Alpha Vantage.
        
        Returns:
            Dictionary with inflation data
        """
        if "alphavantage" not in self.api_keys:
            logger.warning("Alpha Vantage API key not provided")
            return {}

        try:
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "CPI",
                "interval": "monthly",
                "apikey": self.api_keys["alphavantage"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if "data" in data and len(data["data"]) >= 2:
                    latest = data["data"][0]
                    previous = data["data"][1]
                    
                    # Calculate inflation rate (year-over-year)
                    year_ago_index = next((item for item in data["data"] if item["date"][:4] == str(int(latest["date"][:4]) - 1) and item["date"][5:7] == latest["date"][5:7]), None)
                    
                    if year_ago_index:
                        inflation_rate = (float(latest["value"]) - float(year_ago_index["value"])) / float(year_ago_index["value"]) * 100
                        
                        return {
                            "indicator": "inflation",
                            "value": inflation_rate,
                            "date": latest["date"],
                            "previous": (float(previous["value"]) - float(year_ago_index["value"])) / float(year_ago_index["value"]) * 100,
                            "source": "alphavantage"
                        }

            logger.warning("No inflation data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching inflation data: {str(e)}")
            return {}

    async def fetch_interest_rate_data(self) -> Dict[str, Any]:
        """
        Fetch interest rate data (Federal Funds Rate) from Alpha Vantage.
        
        Returns:
            Dictionary with interest rate data
        """
        if "alphavantage" not in self.api_keys:
            logger.warning("Alpha Vantage API key not provided")
            return {}

        try:
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "FEDERAL_FUNDS_RATE",
                "interval": "monthly",
                "apikey": self.api_keys["alphavantage"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if "data" in data and len(data["data"]) >= 2:
                    latest = data["data"][0]
                    previous = data["data"][1]
                    
                    return {
                        "indicator": "interest_rate",
                        "value": float(latest["value"]),
                        "date": latest["date"],
                        "previous": float(previous["value"]),
                        "source": "alphavantage"
                    }

            logger.warning("No interest rate data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching interest rate data: {str(e)}")
            return {}

    async def fetch_unemployment_data(self) -> Dict[str, Any]:
        """
        Fetch unemployment data from Alpha Vantage.
        
        Returns:
            Dictionary with unemployment data
        """
        if "alphavantage" not in self.api_keys:
            logger.warning("Alpha Vantage API key not provided")
            return {}

        try:
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "UNEMPLOYMENT",
                "apikey": self.api_keys["alphavantage"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()

                if "data" in data and len(data["data"]) >= 2:
                    latest = data["data"][0]
                    previous = data["data"][1]
                    
                    return {
                        "indicator": "unemployment",
                        "value": float(latest["value"]),
                        "date": latest["date"],
                        "previous": float(previous["value"]),
                        "source": "alphavantage"
                    }

            logger.warning("No unemployment data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching unemployment data: {str(e)}")
            return {}

    async def fetch_all_indicators(self) -> List[Dict[str, Any]]:
        """
        Fetch all economic indicators.
        
        Returns:
            List of dictionaries with economic indicators data
        """
        tasks = [
            self.fetch_inflation_data(),
            self.fetch_interest_rate_data(),
            self.fetch_unemployment_data()
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Filter out empty results
        indicators = [result for result in results if result]
        
        logger.info(f"Fetched {len(indicators)} economic indicators")
        return indicators

    def to_context_items(self, indicators: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Convert economic indicators to context items for MCP.
        
        Args:
            indicators: List of dictionaries with economic indicators data
            
        Returns:
            List of context items
        """
        context_items = []
        
        for indicator in indicators:
            context_item = {
                "id": f"economic_{indicator['indicator']}_{uuid.uuid4()}",
                "type": "economic_indicator",
                "content": indicator,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": indicator.get("source", "unknown"),
                "confidence": 1.0
            }
            
            context_items.append(context_item)
        
        return context_items

async def main():
    """Main function for testing the economic indicators fetcher."""
    # Sample API keys (replace with actual keys in production)
    api_keys = {
        "alphavantage": "YOUR_ALPHAVANTAGE_API_KEY"
    }
    
    fetcher = EconomicIndicatorsFetcher(api_keys)
    
    # Fetch all indicators
    indicators = await fetcher.fetch_all_indicators()
    
    # Convert to context items
    context_items = fetcher.to_context_items(indicators)
    
    # Print results
    print(f"Fetched {len(indicators)} economic indicators")
    print(f"Generated {len(context_items)} context items")
    
    if indicators:
        print("\nEconomic Indicators:")
        for indicator in indicators:
            print(f"\n{indicator['indicator']}:")
            for key, value in indicator.items():
                if key != "indicator":
                    print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
