"""
Main Governance Collector class that orchestrates data collection from various
governance platforms and processes the data for signal generation.
"""

import logging
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .snapshot_api import SnapshotAPI
from .tally_api import TallyAPI
from .models import GovernanceProposal, VotingData
from .signal_generator import GovernanceSignalGenerator

logger = logging.getLogger(__name__)

class GovernanceCollector:
    """
    Collects governance data from various platforms and generates trading signals.

    This collector integrates with multiple DAO governance platforms to track
    proposals, votes, and governance activities that may impact asset prices.
    """

    def __init__(self, mcp_client, config: Dict[str, Any] = None):
        """
        Initialize the governance collector with configuration.

        Args:
            mcp_client: The Model Context Protocol client for sending data
            config: Configuration dictionary with API keys and settings
        """
        self.mcp_client = mcp_client
        self.config = config or {}

        # Initialize API clients
        self.snapshot_api = SnapshotAPI(
            api_url=self.config.get('snapshot_api_url', 'https://hub.snapshot.org/graphql')
        )

        self.tally_api = TallyAPI(
            api_key=self.config.get('tally_api_key', ''),
            api_url=self.config.get('tally_api_url', 'https://api.tally.xyz')
        )

        # Initialize signal generator
        self.signal_generator = GovernanceSignalGenerator()

        # Track last collection time
        self.last_collection_time = datetime.now() - timedelta(days=1)

        logger.info("Governance Collector initialized")

    async def collect_data(self) -> List[Dict[str, Any]]:
        """
        Collect governance data from all configured platforms.

        Returns:
            List of collected governance data items
        """
        logger.info("Starting governance data collection")

        # Collect data from Snapshot
        snapshot_proposals = await self.snapshot_api.get_recent_proposals(
            hours_ago=self.config.get('lookback_hours', 24)
        )
        logger.info(f"Retrieved {len(snapshot_proposals)} proposals from Snapshot")

        # Collect data from Tally (on-chain governance)
        tally_proposals = await self.tally_api.get_recent_proposals(
            hours_ago=self.config.get('lookback_hours', 24)
        )
        logger.info(f"Retrieved {len(tally_proposals)} proposals from Tally")

        # Collect governance stats for major protocols
        # These are the contract addresses for major governance systems
        major_protocols = [
            "0xc0da02939e1441f497fd74f78ce7decb17b66529",  # Compound
            "0x408ed6354d4973f66138c91495f2f2fcbd8724c3",  # Uniswap
            "0xec568fffba86c094cf06b22134b23074dfe2252c"   # Aave
        ]

        protocol_stats = {}
        for protocol_id in major_protocols:
            stats = await self.tally_api.get_governance_stats(protocol_id)
            if stats:
                protocol_stats[protocol_id] = stats
                # Store stats in MCP for later analysis
                await self._store_protocol_stats(stats)

        logger.info(f"Collected governance stats for {len(protocol_stats)} protocols")

        # Combine and process the data
        all_proposals = snapshot_proposals + tally_proposals

        # Update last collection time
        self.last_collection_time = datetime.now()

        logger.info(f"Collected {len(all_proposals)} governance proposals")
        return all_proposals

    async def _store_protocol_stats(self, stats: Dict[str, Any]) -> bool:
        """Store protocol governance stats in MCP."""
        try:
            # Create a context item for the protocol stats
            context_item = {
                "id": f"gov_stats_{stats['protocol_id']}_{uuid.uuid4()}",
                "type": "governance_stats",
                "content": stats,
                "timestamp": datetime.now().isoformat(),
                "source": "tally",
                "confidence": 1.0
            }

            # Send to MCP via the client
            return await self.mcp_client.add_context(context_item)
        except Exception as e:
            logger.error(f"Error storing protocol stats: {e}")
            return False

    async def process_and_send_to_mcp(self) -> None:
        """
        Process collected governance data and send to MCP server.
        """
        # Collect the data
        governance_data = await self.collect_data()

        # Generate signals from the data
        signals = self.signal_generator.generate_signals(governance_data)

        # Send to MCP server
        for signal in signals:
            await self.mcp_client.add_context({
                "type": "governance_signal",
                "source": signal["source"],
                "proposal_id": signal["proposal_id"],
                "signal_type": signal["signal_type"],
                "strength": signal["strength"],
                "assets_affected": signal["assets_affected"],
                "timestamp": datetime.now().isoformat(),
                "description": signal["description"]
            })

        logger.info(f"Processed and sent {len(signals)} governance signals to MCP")

    async def run_collection_loop(self, interval_seconds: int = 3600) -> None:
        """
        Run the collection loop at specified intervals.

        Args:
            interval_seconds: Seconds between collection runs
        """
        logger.info(f"Starting governance collection loop with {interval_seconds}s interval")

        while True:
            try:
                await self.process_and_send_to_mcp()
            except Exception as e:
                logger.error(f"Error in governance collection: {str(e)}")

            # Wait for next interval
            await asyncio.sleep(interval_seconds)
