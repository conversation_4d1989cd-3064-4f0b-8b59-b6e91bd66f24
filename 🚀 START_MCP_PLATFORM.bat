@echo off
title MCP Trading Platform - One-Click Launcher
color 0A

echo.
echo ========================================
echo   🚀 MCP TRADING PLATFORM LAUNCHER
echo ========================================
echo   Starting unified infrastructure...
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if .env exists, if not create from example
if not exist ".env" (
    if exist ".env.example" (
        echo 📝 Creating .env configuration file...
        copy ".env.example" ".env" > nul
        echo ✅ Configuration file created
    )
)

REM Install/update dependencies
echo 📚 Installing dependencies (this may take a moment)...
pip install --quiet --upgrade pip
pip install --quiet fastapi uvicorn pydantic python-jose passlib httpx redis pandas numpy
if errorlevel 1 (
    echo ⚠️  Some dependencies failed to install, continuing anyway...
)

REM Create necessary directories
if not exist "backend\logs" mkdir "backend\logs"
if not exist "data" mkdir "data"

REM Check if servers are already running
netstat -ano | findstr :8004 > nul
if %errorlevel% equ 0 (
    echo ⚠️  Backend server already running on port 8004
    goto :frontend_check
)

netstat -ano | findstr :8085 > nul
if %errorlevel% equ 0 (
    echo ⚠️  Frontend server already running on port 8085
    goto :open_browser
)

:frontend_check
REM Try to start the new unified server first
echo 🖥️  Starting unified backend server...
if exist "backend\main.py" (
    start "MCP Unified Backend" cmd /k "cd backend && python main.py"
    timeout /t 3 > nul

    REM Check if unified server started successfully
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8004/health' -TimeoutSec 3 | Out-Null; exit 0 } catch { exit 1 }" > nul 2>&1
    if not errorlevel 1 (
        echo ✅ Unified backend server started successfully!
        goto :start_frontend
    )
)

REM Fallback to original server
echo 🔄 Falling back to original server...
if exist "backend\price_server.py" (
    start "MCP Backend Server" cmd /k "cd backend && python price_server.py"
    timeout /t 3 > nul
) else (
    echo ❌ No backend server found
    pause
    exit /b 1
)

:start_frontend
REM Start the frontend server
echo 🌐 Starting frontend server...
start "MCP Frontend Server" cmd /k "cd frontend\public && python -m http.server 8085"
timeout /t 2 > nul

:open_browser
REM Wait a bit more for everything to initialize
echo ⏳ Initializing platform...
timeout /t 3 > nul

REM Open the browser
echo 🌍 Opening browser...
if exist "frontend\public\dashboard.html" (
    start http://localhost:8085/dashboard.html
) else (
    start http://localhost:8085/index.html
)

echo.
echo ========================================
echo ✅ MCP TRADING PLATFORM IS RUNNING!
echo ========================================
echo.
echo 🔗 QUICK ACCESS LINKS:
echo    Dashboard: http://localhost:8085/dashboard.html
echo    API Docs:  http://localhost:8004/docs
echo    Health:    http://localhost:8004/health
echo    Real Data Test: http://localhost:8004/api/real-data/validate-apis
echo    Live Prices: http://localhost:8004/api/real-data/real-prices?symbols=BTC,ETH,SOL
echo.
echo 👤 DEMO LOGIN CREDENTIALS:
echo    Free User:    user / password
echo    Premium User: premium / premium
echo    Admin User:   admin / admin123
echo.
echo 📊 AVAILABLE FEATURES:
echo    • Real-time Trading Signals
echo    • Price Predictions & Analysis
echo    • Sentiment Analysis
echo    • Paper Trading
echo    • Multi-timeframe Analysis
echo    • Interactive Charts
echo.
echo 💡 TIPS:
echo    • No API keys needed for demo mode
echo    • Edit .env file to add your API keys
echo    • Check logs in backend\logs\ for troubleshooting
echo.
echo ⚠️  Press any key to STOP all servers and exit...
pause > nul

echo.
echo 🛑 Stopping all servers...
taskkill /f /im python.exe 2>nul
echo ✅ All servers stopped.
echo 👋 Thank you for using MCP Trading Platform!
echo.
pause
