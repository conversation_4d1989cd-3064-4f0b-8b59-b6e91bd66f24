# @chakra-ui/anatomy

This package declares the anatomy for the Chakra UI components.

It was inspired the `::part()` selector from the
[W3C CSS Shadow Parts Draft](https://www.w3.org/TR/css-shadow-parts-1/) with
data-attributes.

> This is an internal utility, not intended for public usage.

## Installation

```sh
yarn add @chakra-ui/anatomy
# or
npm i @chakra-ui/anatomy
```

## Contribution

Yes please! See the
[contributing guidelines](https://github.com/chakra-ui/core/blob/main/CONTRIBUTING.md)
for details.

## Licence

This project is licensed under the terms of the
[MIT license](https://github.com/chakra-ui/core/blob/main/LICENSE).
