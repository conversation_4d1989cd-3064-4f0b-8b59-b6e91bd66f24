<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Charts | MCP Trading Platform</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Live Charts Page Styles */
        .chart-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .chart-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 15px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-group label {
            margin-bottom: 5px;
            font-weight: 500;
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .control-group label i {
            margin-right: 5px;
            color: var(--primary-color);
        }

        .control-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            min-width: 180px;
        }

        .control-group select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            outline: none;
        }

        #trading-chart-container {
            height: 600px;
            width: 100%;
            border-radius: 4px;
            overflow: hidden;
        }

        .chart-info {
            background-color: #e3f2fd;
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .chart-info h3 {
            margin-top: 0;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .chart-info h3 i {
            margin-right: 8px;
        }

        .chart-info p {
            margin: 0 0 10px 0;
        }

        .chart-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .chart-info li {
            margin-bottom: 5px;
        }

        .chart-info li:last-child {
            margin-bottom: 0;
        }

        .chart-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .feature-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-top: 3px solid var(--primary-color);
        }

        .feature-card h4 {
            margin-top: 0;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            font-size: 16px;
        }

        .feature-card h4 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .feature-card p {
            margin: 0;
            color: #555;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Loading state */
        .chart-loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 400px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .chart-controls {
                flex-direction: column;
                gap: 10px;
            }

            #trading-chart-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div>
                <h1>Live Trading Charts</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading.html">Trading</a>
                    <a href="governance.html">Governance</a>
                    <a href="sentiment.html">Sentiment</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="charts.html" style="font-weight: bold;">Charts</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>
            <div class="user-info">
                <span>Welcome, Demo User (premium)</span>
                <button class="btn btn-red" onclick="alert('Logout functionality would be implemented here')">Logout</button>
            </div>
        </header>

        <div class="chart-info">
            <h3><i class="fas fa-chart-line"></i> Professional Trading Charts</h3>
            <p>
                Access real-time trading charts with advanced technical analysis tools:
            </p>
            <ul>
                <li><strong>Multiple Timeframes:</strong> From 1-minute to monthly charts</li>
                <li><strong>Technical Indicators:</strong> Moving averages, RSI, MACD, Bollinger Bands, and more</li>
                <li><strong>Drawing Tools:</strong> Trend lines, Fibonacci retracements, and support/resistance levels</li>
                <li><strong>Real-time Data:</strong> Live price updates from major exchanges</li>
            </ul>
            <p class="mt-1"><em>Use the controls above the chart to customize your view and analysis.</em></p>
        </div>

        <div class="chart-controls">
            <div class="control-group">
                <label for="coin-selector"><i class="fas fa-coins"></i> Cryptocurrency</label>
                <select id="coin-selector">
                    <option value="BTCUSD">Bitcoin (BTC)</option>
                    <option value="ETHUSD">Ethereum (ETH)</option>
                    <option value="SOLUSD">Solana (SOL)</option>
                    <option value="ADAUSD">Cardano (ADA)</option>
                    <option value="DOTUSD">Polkadot (DOT)</option>
                    <option value="AVAXUSD">Avalanche (AVAX)</option>
                </select>
            </div>
            <div class="control-group">
                <label for="timeframe-selector"><i class="fas fa-clock"></i> Timeframe</label>
                <select id="timeframe-selector">
                    <option value="1">1 Minute</option>
                    <option value="5">5 Minutes</option>
                    <option value="15">15 Minutes</option>
                    <option value="30">30 Minutes</option>
                    <option value="60">1 Hour</option>
                    <option value="240">4 Hours</option>
                    <option value="D" selected>1 Day</option>
                    <option value="W">1 Week</option>
                </select>
            </div>
            <div class="control-group">
                <label for="exchange-selector"><i class="fas fa-building"></i> Exchange</label>
                <select id="exchange-selector">
                    <option value="BINANCE" selected>Binance</option>
                    <option value="COINBASE">Coinbase</option>
                    <option value="KRAKEN">Kraken</option>
                    <option value="FTX">FTX</option>
                </select>
            </div>
            <div class="control-group">
                <label for="chart-type"><i class="fas fa-chart-bar"></i> Chart Type</label>
                <select id="chart-type">
                    <option value="1" selected>Candles</option>
                    <option value="2">Hollow Candles</option>
                    <option value="3">Heikin Ashi</option>
                    <option value="4">Line</option>
                    <option value="5">Area</option>
                    <option value="6">Bars</option>
                </select>
            </div>
        </div>

        <div class="chart-container">
            <div id="trading-chart-container">
                <div class="chart-loading">
                    <div class="loading-spinner"></div>
                    <span>Loading chart data...</span>
                </div>
            </div>
        </div>

        <div class="chart-features">
            <div class="feature-card">
                <h4><i class="fas fa-robot"></i> AI-Powered Analysis</h4>
                <p>Our machine learning models analyze chart patterns and market conditions to identify potential trading opportunities and key support/resistance levels.</p>
            </div>
            <div class="feature-card">
                <h4><i class="fas fa-bell"></i> Price Alerts</h4>
                <p>Set custom price alerts to be notified when a cryptocurrency reaches a specific price level or when a technical indicator signals a potential trade.</p>
            </div>
            <div class="feature-card">
                <h4><i class="fas fa-history"></i> Historical Backtesting</h4>
                <p>Test your trading strategies against historical data to see how they would have performed in past market conditions.</p>
            </div>
            <div class="feature-card">
                <h4><i class="fas fa-sync-alt"></i> Multi-Timeframe Analysis</h4>
                <p>Analyze price action across multiple timeframes simultaneously to identify stronger trading signals and confirm trends.</p>
            </div>
        </div>
    </div>

    <script src="https://s3.tradingview.com/tv.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize variables
            let widget;
            let currentSymbol = 'BINANCE:BTCUSD';
            let currentInterval = 'D';
            let currentStyle = '1';

            // Function to create or update the TradingView widget
            function createWidget() {
                // Clear the container
                document.getElementById('trading-chart-container').innerHTML = '';

                // Create new widget
                widget = new TradingView.widget({
                    "autosize": true,
                    "symbol": currentSymbol,
                    "interval": currentInterval,
                    "timezone": "Etc/UTC",
                    "theme": "light",
                    "style": currentStyle,
                    "locale": "en",
                    "toolbar_bg": "#f1f3f6",
                    "enable_publishing": false,
                    "withdateranges": true,
                    "hide_side_toolbar": false,
                    "allow_symbol_change": true,
                    "studies": [
                        "MASimple@tv-basicstudies",
                        "RSI@tv-basicstudies",
                        "MACD@tv-basicstudies"
                    ],
                    "container_id": "trading-chart-container"
                });
            }

            // Initialize the chart
            createWidget();

            // Handle coin selection change
            document.getElementById('coin-selector').addEventListener('change', function() {
                const exchange = document.getElementById('exchange-selector').value;
                currentSymbol = `${exchange}:${this.value}`;
                createWidget();
            });

            // Handle timeframe selection change
            document.getElementById('timeframe-selector').addEventListener('change', function() {
                currentInterval = this.value;
                createWidget();
            });

            // Handle exchange selection change
            document.getElementById('exchange-selector').addEventListener('change', function() {
                const coin = document.getElementById('coin-selector').value;
                currentSymbol = `${this.value}:${coin}`;
                createWidget();
            });

            // Handle chart type selection change
            document.getElementById('chart-type').addEventListener('change', function() {
                currentStyle = this.value;
                createWidget();
            });
        });
    </script>
</body>
</html>
