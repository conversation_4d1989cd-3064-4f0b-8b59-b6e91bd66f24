@echo off
title MCP Trading Platform - Simple Real Data Start
color 0A

echo.
echo ========================================
echo   🟢 MCP TRADING PLATFORM - REAL DATA
echo ========================================
echo   Using existing setup with real APIs
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org
    pause
    exit /b 1
)

REM Use existing virtual environment if available
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Using existing virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo 🔧 No virtual environment found, using system Python...
)

REM Set environment variable for real data
set USE_REAL_DATA=true
set ENABLE_REAL_TIME_DATA=true

echo 🔑 Configuring real API keys...

REM Set API keys as environment variables (from your existing config)
set COINGECKO_API_KEY=CG-Ag17QEEjKxkZAo4yFr66pE9L
set CRYPTOCOMPARE_API_KEY=****************************************************************
set COINMARKETCAP_API_KEY=26858902-d753-4225-8fc2-f5af98144a9d
set BINANCE_API_KEY=GhP7XdXbyxfMyCGXellMwNlEqYDCVYyPulh2FXpR9t10dEGaNlXoUgy7IcDG4wEr
set BINANCE_SECRET_KEY=KKPV67R2Ml0ccEfLWSCsYnjZAwhG8kmJZjSKABrdz9J3YDeVToMPvVQalXWV33Gq
set NLP_CLOUD_API_KEY=a95f288fdcaee11608cdf4de5fe5f46f657da731

echo ✅ Real API keys configured!

REM Create logs directory if it doesn't exist
if not exist "backend\logs" mkdir "backend\logs"

echo.
echo 🔴 STARTING WITH REAL DATA...
echo ========================================

REM Check if servers are already running
netstat -ano | findstr :8004 > nul
if %errorlevel% equ 0 (
    echo ⚠️  Backend server already running on port 8004
    goto :frontend_check
)

REM Try to start the unified server first
echo 🖥️  Starting backend with real data...
if exist "backend\main.py" (
    start "MCP Real Data Backend" cmd /k "cd backend && set USE_REAL_DATA=true && python main.py"
    timeout /t 3 > nul
    
    REM Check if unified server started
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8004/health' -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" > nul 2>&1
    if not errorlevel 1 (
        echo ✅ Unified backend started with real data!
        goto :start_frontend
    )
)

REM Fallback to original server
echo 🔄 Trying original server...
if exist "backend\price_server.py" (
    start "MCP Real Data Server" cmd /k "cd backend && set USE_REAL_DATA=true && python price_server.py"
    timeout /t 3 > nul
    echo ✅ Original server started with real data!
) else (
    echo ❌ No backend server found
    pause
    exit /b 1
)

:start_frontend
:frontend_check
REM Check if frontend is already running
netstat -ano | findstr :8085 > nul
if %errorlevel% equ 0 (
    echo ⚠️  Frontend already running on port 8085
    goto :open_browser
)

REM Start the frontend server
echo 🌐 Starting frontend server...
start "MCP Frontend Server" cmd /k "cd frontend\public && python -m http.server 8085"
timeout /t 2 > nul

:open_browser
REM Wait for everything to initialize
echo ⏳ Initializing real data connections...
timeout /t 3 > nul

REM Open the browser
echo 🌍 Opening browser to dashboard...
if exist "frontend\public\dashboard.html" (
    start http://localhost:8085/dashboard.html
) else (
    start http://localhost:8085/index.html
)

echo.
echo ========================================
echo ✅ REAL DATA MODE IS RUNNING!
echo ========================================
echo.
echo 🔴 LIVE DATA SOURCES ACTIVE:
echo    • CoinGecko: Live prices & market data
echo    • CryptoCompare: Real-time crypto data  
echo    • CoinMarketCap: Market cap data
echo    • Binance: Live exchange data
echo    • NLP Cloud: Real sentiment analysis
echo.
echo 🔗 QUICK ACCESS:
echo    Dashboard: http://localhost:8085/dashboard.html
echo    API Health: http://localhost:8004/health
echo    Live BTC Price: http://localhost:8004/api/cc/price/BTC
echo    Real Signals: http://localhost:8085/signals.html
echo.
echo 👤 LOGIN CREDENTIALS:
echo    Username: user / Password: password
echo    Username: premium / Password: premium
echo.
echo 📊 REAL DATA FEATURES:
echo    • Live cryptocurrency prices (updated every 30s)
echo    • Real-time trading signals
echo    • Actual market sentiment analysis
echo    • Live on-chain metrics
echo    • Current market cap & volume data
echo.
echo 💡 VERIFICATION:
echo    • Check prices match real market prices
echo    • Signals update with live market data
echo    • No "demo" or "fake" data labels
echo.
echo ⚠️  Press any key to STOP all servers...
pause > nul

echo.
echo 🛑 Stopping all servers...
taskkill /f /im python.exe 2>nul
echo ✅ All servers stopped.
echo 🔴 Real data session ended.
echo.
pause
