"""
Test script for the governance data collector.

This script tests the governance data collector by running it directly
and printing the collected data and generated signals.
"""

import asyncio
import logging
from datetime import datetime

from governance_collector import GovernanceCollector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class MockMCPClient:
    """Mock MCP client for testing."""
    
    async def add_context(self, context_item):
        """Mock method to add context to MCP server."""
        print(f"Would send to MCP: {context_item}")
        return True

async def main():
    """Main test function."""
    # Create a mock MCP client
    mock_client = MockMCPClient()
    
    # Create the governance collector
    collector = GovernanceCollector(mock_client, {
        'lookback_hours': 24,
        'snapshot_api_url': 'https://hub.snapshot.org/graphql',
    })
    
    # Collect governance data
    print("\n=== Collecting Governance Data ===\n")
    governance_data = await collector.collect_data()
    
    # Print summary of collected data
    print(f"\nCollected {len(governance_data)} governance proposals")
    
    if governance_data:
        # Print details of the first proposal
        first_proposal = governance_data[0]
        if hasattr(first_proposal, 'title'):
            print(f"\nExample proposal: {first_proposal.title}")
            print(f"From space: {first_proposal.space_name}")
            print(f"State: {first_proposal.state}")
            print(f"Created at: {first_proposal.created_at}")
        else:
            print(f"\nExample proposal: {first_proposal}")
    
    # Generate signals
    print("\n=== Generating Signals ===\n")
    signals = collector.signal_generator.generate_signals(governance_data)
    
    # Print generated signals
    print(f"\nGenerated {len(signals)} signals")
    
    if signals:
        # Print details of the first signal
        first_signal = signals[0]
        print(f"\nExample signal: {first_signal['signal_type']}")
        print(f"Description: {first_signal['description']}")
        print(f"Strength: {first_signal['strength']}")
        print(f"Assets affected: {first_signal['assets_affected']}")
        print(f"Source: {first_signal['source']}")

if __name__ == "__main__":
    asyncio.run(main())
