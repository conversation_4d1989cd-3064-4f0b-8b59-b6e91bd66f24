"""
Governance Signal Processor

This module processes governance signals from the MCP server and converts them
to trading signals that can be displayed in the frontend.
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from signal_generator import Signal, SignalType, TimeFrame, AssetType, ContextQuery

logger = logging.getLogger(__name__)

class GovernanceSignalProcessor:
    """Processes governance signals from MCP context items."""
    
    def __init__(self, signal_generator):
        """Initialize with a reference to the signal generator."""
        self.signal_generator = signal_generator
        logger.info("Governance signal processor initialized")
    
    async def fetch_governance_signals(self) -> List[Dict[str, Any]]:
        """Fetch governance signals from the MCP server."""
        # Query for governance signals from the last 24 hours
        query = ContextQuery(
            types=["governance_signal"],
            time_range={
                "start": datetime.utcnow() - timedelta(hours=24)
            },
            limit=20
        )
        
        try:
            context_items = await self.signal_generator.fetch_context(query)
            logger.info(f"Fetched {len(context_items)} governance signals from MCP server")
            return context_items
        except Exception as e:
            logger.error(f"Error fetching governance signals: {e}")
            return []
    
    def _map_signal_type(self, governance_signal_type: str) -> SignalType:
        """Map governance signal types to trading signal types."""
        # Map governance signal types to trading signal types
        if governance_signal_type in ["high_impact_proposal", "contentious_proposal"]:
            return SignalType.HOLD  # These are "watch" signals
        
        # For other types, use a simple mapping based on sentiment
        return SignalType.BUY  # Default to BUY for positive governance activity
    
    def _calculate_confidence(self, strength: float) -> float:
        """Calculate confidence based on signal strength."""
        # Governance signals already have a strength value between 0 and 1
        # We can use this directly as confidence, or adjust as needed
        return min(0.9, strength + 0.1)  # Boost slightly but cap at 0.9
    
    def _get_asset_details(self, asset_symbol: str) -> Dict[str, Any]:
        """Get asset details for a given symbol."""
        # This is a simplified implementation
        # In a real system, you would look up asset details from a database
        
        # Default values for unknown assets
        asset_details = {
            "id": asset_symbol.lower(),
            "name": asset_symbol,
            "price": 0.0
        }
        
        # Known assets
        known_assets = {
            "BTC": {"id": "bitcoin", "name": "Bitcoin", "price": 60000.0},
            "ETH": {"id": "ethereum", "name": "Ethereum", "price": 3000.0},
            "UNI": {"id": "uniswap", "name": "Uniswap", "price": 5.0},
            "AAVE": {"id": "aave", "name": "Aave", "price": 80.0},
            "COMP": {"id": "compound", "name": "Compound", "price": 40.0},
            "MKR": {"id": "maker", "name": "Maker", "price": 1200.0},
            "SUSHI": {"id": "sushi", "name": "SushiSwap", "price": 1.0},
            "CRV": {"id": "curve-dao-token", "name": "Curve DAO", "price": 0.5},
            "SNX": {"id": "synthetix-network-token", "name": "Synthetix", "price": 2.5},
            "BAL": {"id": "balancer", "name": "Balancer", "price": 4.0},
            "YFI": {"id": "yearn-finance", "name": "Yearn.finance", "price": 7000.0},
        }
        
        if asset_symbol in known_assets:
            asset_details = known_assets[asset_symbol]
        
        return asset_details
    
    def convert_to_trading_signals(self, governance_signals: List[Dict[str, Any]]) -> List[Signal]:
        """Convert governance signals to trading signals."""
        trading_signals = []
        
        for gov_signal in governance_signals:
            # Extract content
            content = gov_signal["content"]
            assets_affected = content["assets_affected"]
            
            # Skip signals with no affected assets
            if not assets_affected:
                continue
            
            # Create a trading signal for each affected asset
            for asset_symbol in assets_affected:
                # Get asset details
                asset_details = self._get_asset_details(asset_symbol)
                
                # Map governance signal type to trading signal type
                signal_type = self._map_signal_type(content["signal_type"])
                
                # Calculate confidence
                confidence = self._calculate_confidence(content["strength"])
                
                # Create trading signal
                trading_signal = Signal(
                    id=f"gov_{gov_signal['id']}_{asset_symbol}",
                    asset_id=asset_details["id"],
                    asset_symbol=asset_symbol,
                    asset_name=asset_details["name"],
                    asset_type=AssetType.CRYPTO,  # Assuming all governance signals are for crypto
                    signal_type=signal_type,
                    time_frame=TimeFrame.DAY_1,  # Governance signals typically have longer timeframes
                    confidence=confidence,
                    price=asset_details["price"],
                    timestamp=datetime.fromisoformat(gov_signal["timestamp"].replace("Z", "+00:00")),
                    expiration=datetime.utcnow() + timedelta(days=1),  # Governance signals last longer
                    indicators={
                        "governance_signal_type": content["signal_type"],
                        "governance_signal_strength": content["strength"],
                        "proposal_id": content["proposal_id"],
                        "description": content["description"]
                    },
                    context_ids=[gov_signal["id"]]
                )
                
                trading_signals.append(trading_signal)
                logger.info(f"Converted governance signal to {signal_type} trading signal for {asset_symbol}")
        
        return trading_signals
    
    async def get_governance_trading_signals(self) -> List[Signal]:
        """Get trading signals derived from governance signals."""
        # Fetch governance signals from MCP
        governance_signals = await self.fetch_governance_signals()
        
        # Convert to trading signals
        if governance_signals:
            trading_signals = self.convert_to_trading_signals(governance_signals)
            logger.info(f"Generated {len(trading_signals)} trading signals from {len(governance_signals)} governance signals")
            return trading_signals
        
        return []
