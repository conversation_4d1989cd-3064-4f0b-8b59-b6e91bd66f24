"""
User Preferences API

This module provides API endpoints for managing user preferences.
"""

import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/preferences",
    tags=["preferences"],
    responses={404: {"description": "Not found"}},
)

# Models
class DashboardWidget(BaseModel):
    """Model for dashboard widget preferences."""
    id: str
    type: str
    title: str
    position: Dict[str, int]
    size: Dict[str, int]
    settings: Dict[str, Any]
    visible: bool = True

class SignalFilter(BaseModel):
    """Model for signal filter preferences."""
    signal_type: str
    enabled: bool = True
    min_strength: float = 0.0
    max_strength: float = 1.0
    assets: List[str] = []

class AssetPreference(BaseModel):
    """Model for asset preferences."""
    symbol: str
    name: str
    favorite: bool = False
    alerts: List[Dict[str, Any]] = []
    notes: str = ""

class UserPreferences(BaseModel):
    """Model for user preferences."""
    user_id: str
    theme: str = "light"
    dashboard_widgets: List[DashboardWidget] = []
    signal_filters: List[SignalFilter] = []
    assets: List[AssetPreference] = []
    refresh_intervals: Dict[str, int] = {
        "prices": 30,
        "trading_signals": 60,
        "governance": 1800,
        "sentiment": 300
    }
    notifications: Dict[str, bool] = {
        "email": True,
        "browser": True,
        "mobile": False
    }

# In-memory storage for user preferences (replace with database in production)
user_preferences_db = {}

# Default preferences
DEFAULT_WIDGETS = [
    DashboardWidget(
        id="price_chart",
        type="chart",
        title="Price Chart",
        position={"x": 0, "y": 0},
        size={"w": 8, "h": 4},
        settings={"timeframe": "1d", "indicators": ["sma", "volume"]}
    ),
    DashboardWidget(
        id="trading_signals",
        type="table",
        title="Trading Signals",
        position={"x": 0, "y": 4},
        size={"w": 4, "h": 4},
        settings={"limit": 10, "sort": "timestamp"}
    ),
    DashboardWidget(
        id="governance_signals",
        type="table",
        title="Governance Signals",
        position={"x": 4, "y": 4},
        size={"w": 4, "h": 4},
        settings={"limit": 10, "sort": "timestamp"}
    ),
    DashboardWidget(
        id="sentiment_overview",
        type="gauge",
        title="Market Sentiment",
        position={"x": 8, "y": 0},
        size={"w": 4, "h": 4},
        settings={"assets": ["BTC", "ETH"]}
    )
]

DEFAULT_SIGNAL_FILTERS = [
    SignalFilter(
        signal_type="buy",
        enabled=True,
        min_strength=0.5,
        max_strength=1.0,
        assets=[]
    ),
    SignalFilter(
        signal_type="sell",
        enabled=True,
        min_strength=0.5,
        max_strength=1.0,
        assets=[]
    ),
    SignalFilter(
        signal_type="high_impact_proposal",
        enabled=True,
        min_strength=0.7,
        max_strength=1.0,
        assets=[]
    ),
    SignalFilter(
        signal_type="sentiment_shift",
        enabled=True,
        min_strength=0.6,
        max_strength=1.0,
        assets=[]
    )
]

DEFAULT_ASSETS = [
    AssetPreference(
        symbol="BTC",
        name="Bitcoin",
        favorite=True,
        alerts=[
            {"type": "price", "condition": "above", "value": 50000, "enabled": True},
            {"type": "price", "condition": "below", "value": 40000, "enabled": True}
        ]
    ),
    AssetPreference(
        symbol="ETH",
        name="Ethereum",
        favorite=True,
        alerts=[
            {"type": "price", "condition": "above", "value": 3000, "enabled": True},
            {"type": "price", "condition": "below", "value": 2000, "enabled": True}
        ]
    )
]

def get_default_preferences(user_id: str) -> UserPreferences:
    """Get default preferences for a new user."""
    return UserPreferences(
        user_id=user_id,
        dashboard_widgets=DEFAULT_WIDGETS,
        signal_filters=DEFAULT_SIGNAL_FILTERS,
        assets=DEFAULT_ASSETS
    )

# API endpoints
@router.get("/{user_id}", response_model=UserPreferences)
async def get_preferences(user_id: str):
    """Get user preferences."""
    if user_id not in user_preferences_db:
        # Create default preferences for new users
        user_preferences_db[user_id] = get_default_preferences(user_id)
    
    return user_preferences_db[user_id]

@router.put("/{user_id}", response_model=UserPreferences)
async def update_preferences(user_id: str, preferences: UserPreferences):
    """Update user preferences."""
    if preferences.user_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User ID in path must match user ID in preferences"
        )
    
    user_preferences_db[user_id] = preferences
    logger.info(f"Updated preferences for user {user_id}")
    return preferences

@router.patch("/{user_id}/widgets", response_model=List[DashboardWidget])
async def update_widgets(user_id: str, widgets: List[DashboardWidget]):
    """Update dashboard widgets."""
    if user_id not in user_preferences_db:
        user_preferences_db[user_id] = get_default_preferences(user_id)
    
    user_preferences_db[user_id].dashboard_widgets = widgets
    logger.info(f"Updated widgets for user {user_id}")
    return widgets

@router.patch("/{user_id}/signal_filters", response_model=List[SignalFilter])
async def update_signal_filters(user_id: str, filters: List[SignalFilter]):
    """Update signal filters."""
    if user_id not in user_preferences_db:
        user_preferences_db[user_id] = get_default_preferences(user_id)
    
    user_preferences_db[user_id].signal_filters = filters
    logger.info(f"Updated signal filters for user {user_id}")
    return filters

@router.patch("/{user_id}/assets", response_model=List[AssetPreference])
async def update_assets(user_id: str, assets: List[AssetPreference]):
    """Update asset preferences."""
    if user_id not in user_preferences_db:
        user_preferences_db[user_id] = get_default_preferences(user_id)
    
    user_preferences_db[user_id].assets = assets
    logger.info(f"Updated asset preferences for user {user_id}")
    return assets

@router.patch("/{user_id}/refresh_intervals", response_model=Dict[str, int])
async def update_refresh_intervals(user_id: str, intervals: Dict[str, int]):
    """Update refresh intervals."""
    if user_id not in user_preferences_db:
        user_preferences_db[user_id] = get_default_preferences(user_id)
    
    user_preferences_db[user_id].refresh_intervals = intervals
    logger.info(f"Updated refresh intervals for user {user_id}")
    return intervals

@router.patch("/{user_id}/notifications", response_model=Dict[str, bool])
async def update_notifications(user_id: str, notifications: Dict[str, bool]):
    """Update notification preferences."""
    if user_id not in user_preferences_db:
        user_preferences_db[user_id] = get_default_preferences(user_id)
    
    user_preferences_db[user_id].notifications = notifications
    logger.info(f"Updated notification preferences for user {user_id}")
    return notifications

@router.delete("/{user_id}")
async def reset_preferences(user_id: str):
    """Reset user preferences to defaults."""
    user_preferences_db[user_id] = get_default_preferences(user_id)
    logger.info(f"Reset preferences for user {user_id}")
    return {"message": "Preferences reset to defaults"}
