"""
Paper Trading Module for Project Ruby.

This module handles paper trading functionality, allowing users to track trades
based on platform-generated signals without using real money.
"""

import os
import json
import uuid
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("paper_trading")

# Constants
DATA_DIR = os.path.join("data")
PAPER_TRADES_FILE = os.path.join(DATA_DIR, "paper_trades.json")

# Ensure data directory exists
os.makedirs(DATA_DIR, exist_ok=True)

class PaperTrading:
    """Paper Trading class to manage paper trades."""

    def __init__(self):
        """Initialize the paper trading system."""
        self.trades = self._load_trades()

    def _load_trades(self) -> List[Dict[str, Any]]:
        """Load trades from the JSON file."""
        if os.path.exists(PAPER_TRADES_FILE):
            try:
                with open(PAPER_TRADES_FILE, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading paper trades: {e}")
                return []
        return []

    def _save_trades(self) -> bool:
        """Save trades to the JSON file."""
        try:
            with open(PAPER_TRADES_FILE, 'w') as f:
                json.dump(self.trades, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving paper trades: {e}")
            return False

    def create_trade(self,
                    symbol: str,
                    trade_type: str,
                    entry_price: float,
                    amount: float,
                    usd_amount: float = 0.0,
                    leverage: float = 1.0,
                    timeframe: str = "1d",
                    confidence: float = 0.0,
                    notes: str = "") -> Dict[str, Any]:
        """
        Create a new paper trade.

        Args:
            symbol: The cryptocurrency symbol (e.g., BTC, ETH)
            trade_type: The type of trade (buy/sell)
            entry_price: The price at entry
            amount: The amount of cryptocurrency
            timeframe: The timeframe of the signal
            confidence: The confidence level of the signal
            notes: Additional notes for the trade

        Returns:
            The created trade as a dictionary
        """
        trade_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()

        trade = {
            "id": trade_id,
            "symbol": symbol.upper(),
            "trade_type": trade_type.lower(),
            "entry_price": float(entry_price),
            "amount": float(amount),
            "usd_amount": float(usd_amount),
            "leverage": float(leverage),
            "entry_timestamp": timestamp,
            "exit_price": None,
            "exit_timestamp": None,
            "profit_loss": None,
            "profit_loss_percentage": None,
            "status": "open",
            "timeframe": timeframe,
            "confidence": float(confidence),
            "notes": notes
        }

        self.trades.append(trade)
        self._save_trades()

        return trade

    def close_trade(self, trade_id: str, exit_price: float) -> Optional[Dict[str, Any]]:
        """
        Close an existing paper trade.

        Args:
            trade_id: The ID of the trade to close
            exit_price: The price at exit

        Returns:
            The updated trade as a dictionary, or None if not found
        """
        for trade in self.trades:
            if trade["id"] == trade_id and trade["status"] == "open":
                trade["exit_price"] = float(exit_price)
                trade["exit_timestamp"] = datetime.now().isoformat()
                trade["status"] = "closed"

                # Calculate profit/loss
                entry_price = trade["entry_price"]
                amount = trade["amount"]
                leverage = trade.get("leverage", 1.0)

                if trade["trade_type"] == "buy" or trade["trade_type"] == "long":
                    # For long trades, profit = (exit_price - entry_price) * amount * leverage
                    profit_loss = (exit_price - entry_price) * amount * leverage
                    profit_loss_percentage = (exit_price / entry_price - 1) * 100 * leverage
                else:
                    # For short trades, profit = (entry_price - exit_price) * amount * leverage
                    profit_loss = (entry_price - exit_price) * amount * leverage
                    profit_loss_percentage = (entry_price / exit_price - 1) * 100 * leverage

                trade["profit_loss"] = profit_loss
                trade["profit_loss_percentage"] = profit_loss_percentage

                self._save_trades()
                return trade

        return None

    def get_trade(self, trade_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific trade by ID.

        Args:
            trade_id: The ID of the trade to retrieve

        Returns:
            The trade as a dictionary, or None if not found
        """
        for trade in self.trades:
            if trade["id"] == trade_id:
                return trade

        return None

    def get_all_trades(self) -> List[Dict[str, Any]]:
        """
        Get all trades.

        Returns:
            A list of all trades
        """
        return self.trades

    def get_open_trades(self) -> List[Dict[str, Any]]:
        """
        Get all open trades.

        Returns:
            A list of open trades
        """
        return [trade for trade in self.trades if trade["status"] == "open"]

    def get_closed_trades(self) -> List[Dict[str, Any]]:
        """
        Get all closed trades.

        Returns:
            A list of closed trades
        """
        return [trade for trade in self.trades if trade["status"] == "closed"]

    def update_trade_pl(self, trade_id: str, current_price: float) -> Optional[Dict[str, Any]]:
        """
        Update the profit/loss of an open trade based on current price.

        Args:
            trade_id: The ID of the trade to update
            current_price: The current price of the cryptocurrency

        Returns:
            The updated trade as a dictionary, or None if not found
        """
        for trade in self.trades:
            if trade["id"] == trade_id and trade["status"] == "open":
                entry_price = trade["entry_price"]
                amount = trade["amount"]

                leverage = trade.get("leverage", 1.0)

                if trade["trade_type"] == "buy" or trade["trade_type"] == "long":
                    # For long trades, profit = (current_price - entry_price) * amount * leverage
                    profit_loss = (current_price - entry_price) * amount * leverage
                    profit_loss_percentage = (current_price / entry_price - 1) * 100 * leverage
                else:
                    # For short trades, profit = (entry_price - current_price) * amount * leverage
                    profit_loss = (entry_price - current_price) * amount * leverage
                    profit_loss_percentage = (entry_price / current_price - 1) * 100 * leverage

                # Update the trade with current P/L (but don't change status)
                trade["current_price"] = current_price
                trade["profit_loss"] = profit_loss
                trade["profit_loss_percentage"] = profit_loss_percentage
                trade["last_updated"] = datetime.now().isoformat()

                self._save_trades()
                return trade

        return None

    def update_all_open_trades(self, price_data: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        Update all open trades with current prices.

        Args:
            price_data: Dictionary mapping symbols to current prices

        Returns:
            List of updated trades
        """
        updated_trades = []

        for trade in self.trades:
            if trade["status"] == "open" and trade["symbol"] in price_data:
                current_price = price_data[trade["symbol"]]
                updated_trade = self.update_trade_pl(trade["id"], current_price)
                if updated_trade:
                    updated_trades.append(updated_trade)

        return updated_trades

    def delete_trade(self, trade_id: str) -> bool:
        """
        Delete a trade.

        Args:
            trade_id: The ID of the trade to delete

        Returns:
            True if the trade was deleted, False otherwise
        """
        for i, trade in enumerate(self.trades):
            if trade["id"] == trade_id:
                del self.trades[i]
                self._save_trades()
                return True

        return False

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the paper trading portfolio.

        Returns:
            A dictionary with portfolio summary information
        """
        total_invested = 0.0
        total_current_value = 0.0
        total_profit_loss = 0.0
        total_closed_profit_loss = 0.0

        open_positions = {}

        # Calculate totals for open trades
        for trade in self.trades:
            if trade["status"] == "open":
                invested = trade["entry_price"] * trade["amount"]
                total_invested += invested

                if "current_price" in trade:
                    current_value = trade["current_price"] * trade["amount"]
                    total_current_value += current_value
                    total_profit_loss += trade["profit_loss"]

                # Track open positions by symbol
                symbol = trade["symbol"]
                if symbol not in open_positions:
                    open_positions[symbol] = {
                        "amount": 0,
                        "invested": 0,
                        "current_value": 0,
                        "profit_loss": 0
                    }

                open_positions[symbol]["amount"] += trade["amount"]
                open_positions[symbol]["invested"] += invested

                if "current_price" in trade:
                    open_positions[symbol]["current_value"] += current_value
                    open_positions[symbol]["profit_loss"] += trade["profit_loss"]

            # Add closed trade profits/losses
            elif trade["status"] == "closed":
                total_closed_profit_loss += trade["profit_loss"]

        # Calculate portfolio performance
        portfolio_performance = 0.0
        if total_invested > 0:
            portfolio_performance = (total_profit_loss / total_invested) * 100

        return {
            "total_invested": total_invested,
            "total_current_value": total_current_value,
            "total_profit_loss": total_profit_loss,
            "total_closed_profit_loss": total_closed_profit_loss,
            "portfolio_performance": portfolio_performance,
            "open_positions": open_positions,
            "open_trades_count": len(self.get_open_trades()),
            "closed_trades_count": len(self.get_closed_trades()),
            "total_trades_count": len(self.trades)
        }

# Create a singleton instance
paper_trading = PaperTrading()
