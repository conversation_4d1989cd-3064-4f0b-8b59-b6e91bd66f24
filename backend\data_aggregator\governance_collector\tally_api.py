"""
API client for Tally, a governance analytics platform.

Tally provides analytics and interfaces for on-chain governance systems
like Compound, Aave, Uniswap, and other major DeFi protocols.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

import aiohttp
from .models import GovernanceProposal, VotingData

logger = logging.getLogger(__name__)

class TallyAPI:
    """
    Client for the Tally API.

    Tally is a governance analytics platform that provides data on on-chain
    governance systems for major DeFi protocols.
    """

    def __init__(self, api_key: str = "", api_url: str = "https://api.tally.xyz"):
        """
        Initialize the Tally API client.

        Args:
            api_key: API key for Tally (if required)
            api_url: The API endpoint URL
        """
        self.api_key = api_key
        self.api_url = api_url
        self.headers = {}

        if api_key:
            self.headers["Authorization"] = f"Bearer {api_key}"

        logger.info(f"Initialized Tally API client with endpoint: {api_url}")

    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make a request to the Tally API.

        Args:
            endpoint: API endpoint path
            params: Query parameters

        Returns:
            The JSON response from the API
        """
        url = f"{self.api_url}/{endpoint}"
        params = params or {}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, headers=self.headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Tally API error: {response.status} - {error_text}")
                    raise Exception(f"Tally API returned {response.status}: {error_text}")

                return await response.json()

    async def get_recent_proposals(self, hours_ago: int = 24) -> List[GovernanceProposal]:
        """
        Get recent proposals from Tally.

        This implementation uses Tally's GraphQL API to fetch recent governance proposals
        from major DeFi protocols like Compound, Aave, and Uniswap.

        Args:
            hours_ago: Look back this many hours for proposals

        Returns:
            List of GovernanceProposal objects
        """
        # Tally now offers a GraphQL API that we can use to fetch governance data
        # We'll use their public GraphQL endpoint

        try:
            # Define the GraphQL query for fetching recent proposals
            graphql_endpoint = "graphql"
            since_timestamp = int((datetime.now() - timedelta(hours=hours_ago)).timestamp())

            # GraphQL query to fetch recent proposals
            query = """
            query GetRecentProposals($since: Int!) {
              proposals(where: {createdAt_gt: $since}, orderBy: createdAt, orderDirection: desc, first: 50) {
                id
                title
                description
                createdAt
                startBlock
                endBlock
                state
                proposer {
                  id
                }
                governor {
                  id
                  name
                }
                votes {
                  id
                  voter {
                    id
                  }
                  support
                  weight
                }
              }
            }
            """

            # Make the GraphQL request
            payload = {
                "query": query,
                "variables": {"since": since_timestamp}
            }

            # Use the _make_request method to send the GraphQL query
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.api_url}/{graphql_endpoint}",
                                       json=payload,
                                       headers=self.headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Tally GraphQL API error: {response.status} - {error_text}")
                        return []

                    result = await response.json()

            # Process the response
            if "data" not in result or "proposals" not in result["data"]:
                logger.warning("Unexpected response format from Tally API")
                return []

            proposals_data = result["data"]["proposals"]
            logger.info(f"Retrieved {len(proposals_data)} proposals from Tally")

            # Convert to GovernanceProposal objects
            proposals = []
            for p in proposals_data:
                # Calculate votes
                votes_count = len(p.get("votes", []))
                votes_for = sum(v.get("weight", 0) for v in p.get("votes", []) if v.get("support") == 1)
                votes_against = sum(v.get("weight", 0) for v in p.get("votes", []) if v.get("support") == 0)

                # Create the proposal object
                proposal = GovernanceProposal(
                    id=p["id"],
                    title=p["title"],
                    description=p["description"][:500] + "..." if len(p["description"]) > 500 else p["description"],
                    created_at=datetime.fromtimestamp(int(p["createdAt"])),
                    end_time=None,  # Not directly available
                    state=p["state"].lower(),
                    space_id=p["governor"]["id"],
                    space_name=p["governor"]["name"],
                    author=p["proposer"]["id"],
                    votes_count=votes_count,
                    scores=[votes_for, votes_against],
                    scores_total=votes_for + votes_against,
                    platform="tally"
                )
                proposals.append(proposal)

            return proposals

        except Exception as e:
            logger.error(f"Error fetching proposals from Tally: {str(e)}")
            return []

    async def get_governance_stats(self, protocol_id: str) -> Dict[str, Any]:
        """
        Get governance statistics for a specific protocol.

        Args:
            protocol_id: The protocol identifier (governor contract address)

        Returns:
            Dictionary with governance statistics
        """
        try:
            # Define the GraphQL query for fetching protocol stats
            graphql_endpoint = "graphql"

            # GraphQL query to fetch protocol stats
            query = """
            query GetProtocolStats($id: ID!) {
              governor(id: $id) {
                id
                name
                proposalCount
                totalVotes
                proposals(first: 10, orderBy: createdAt, orderDirection: desc) {
                  id
                  title
                  state
                  createdAt
                  votes {
                    id
                  }
                }
                delegations(first: 100, orderBy: votingPower, orderDirection: desc) {
                  id
                  votingPower
                }
              }
            }
            """

            # Make the GraphQL request
            payload = {
                "query": query,
                "variables": {"id": protocol_id}
            }

            # Use aiohttp to send the GraphQL query
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.api_url}/{graphql_endpoint}",
                                       json=payload,
                                       headers=self.headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Tally GraphQL API error: {response.status} - {error_text}")
                        return {}

                    result = await response.json()

            # Process the response
            if "data" not in result or "governor" not in result["data"] or not result["data"]["governor"]:
                logger.warning(f"Protocol {protocol_id} not found or unexpected response format")
                return {}

            governor_data = result["data"]["governor"]

            # Calculate statistics
            recent_proposals = governor_data.get("proposals", [])
            active_proposals = [p for p in recent_proposals if p.get("state") in ["ACTIVE", "PENDING"]]
            total_delegations = len(governor_data.get("delegations", []))
            top_delegations = governor_data.get("delegations", [])[:10]
            voting_power_concentration = sum(float(d.get("votingPower", 0)) for d in top_delegations) / \
                                        (sum(float(d.get("votingPower", 0)) for d in governor_data.get("delegations", [])) or 1)

            # Compile stats
            stats = {
                "protocol_id": protocol_id,
                "name": governor_data.get("name", "Unknown"),
                "proposal_count": int(governor_data.get("proposalCount", 0)),
                "total_votes": int(governor_data.get("totalVotes", 0)),
                "active_proposals": len(active_proposals),
                "recent_proposals": len(recent_proposals),
                "total_delegations": total_delegations,
                "voting_power_concentration": voting_power_concentration,
                "last_updated": datetime.now().isoformat()
            }

            logger.info(f"Retrieved governance stats for {stats['name']} ({protocol_id})")
            return stats

        except Exception as e:
            logger.error(f"Error fetching governance stats for {protocol_id}: {str(e)}")
            return {}
