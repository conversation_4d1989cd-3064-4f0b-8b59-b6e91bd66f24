"""
Real Data API Endpoints for MCP Trading Platform

These endpoints specifically test and provide real data from live APIs.
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel

# Import the real data fetcher
from core.real_data_fetcher import create_real_data_fetcher, RealDataFetcher
from config.settings import get_settings

logger = logging.getLogger(__name__)

router = APIRouter()

class RealDataResponse(BaseModel):
    """Response model for real data endpoints."""
    success: bool
    data: Any
    source: str
    timestamp: datetime
    is_real_data: bool
    message: Optional[str] = None

class APIValidationResponse(BaseModel):
    """Response model for API validation."""
    api_keys_configured: Dict[str, bool]
    api_keys_working: Dict[str, bool]
    real_data_available: bool
    message: str

@router.get("/validate-apis", response_model=APIValidationResponse)
async def validate_api_keys():
    """Validate that API keys are configured and working."""
    try:
        settings = get_settings()
        
        # Check which API keys are configured
        configured_keys = {
            'coingecko': bool(settings.api.coingecko_api_key),
            'cryptocompare': bool(settings.api.cryptocompare_api_key),
            'coinmarketcap': bool(settings.api.coinmarketcap_api_key),
            'binance': bool(settings.api.binance_api_key)
        }
        
        # Test API keys
        working_keys = {}
        async with await create_real_data_fetcher(settings) as fetcher:
            working_keys = await fetcher.validate_api_keys()
        
        real_data_available = any(working_keys.values())
        
        message = "✅ Real data APIs are working!" if real_data_available else "⚠️ No working API keys found"
        
        return APIValidationResponse(
            api_keys_configured=configured_keys,
            api_keys_working=working_keys,
            real_data_available=real_data_available,
            message=message
        )
        
    except Exception as e:
        logger.error(f"Error validating API keys: {e}")
        raise HTTPException(status_code=500, detail=f"API validation failed: {str(e)}")

@router.get("/real-price/{symbol}", response_model=RealDataResponse)
async def get_real_price(symbol: str):
    """Get real-time price data for a specific symbol."""
    try:
        settings = get_settings()
        
        async with await create_real_data_fetcher(settings) as fetcher:
            price_data = await fetcher.fetch_real_price_data(symbol.upper())
        
        if price_data:
            return RealDataResponse(
                success=True,
                data=price_data,
                source=price_data.get('source', 'unknown'),
                timestamp=datetime.now(),
                is_real_data=True,
                message=f"Real price data for {symbol.upper()}"
            )
        else:
            return RealDataResponse(
                success=False,
                data=None,
                source="none",
                timestamp=datetime.now(),
                is_real_data=False,
                message=f"No real data available for {symbol.upper()}"
            )
            
    except Exception as e:
        logger.error(f"Error fetching real price for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch real price: {str(e)}")

@router.get("/real-prices", response_model=RealDataResponse)
async def get_real_prices(
    symbols: str = Query(..., description="Comma-separated list of symbols (e.g., BTC,ETH,SOL)")
):
    """Get real-time price data for multiple symbols."""
    try:
        settings = get_settings()
        symbol_list = [s.strip().upper() for s in symbols.split(',')]
        
        async with await create_real_data_fetcher(settings) as fetcher:
            price_data = await fetcher.fetch_multiple_prices(symbol_list)
        
        if price_data:
            return RealDataResponse(
                success=True,
                data=price_data,
                source="multiple",
                timestamp=datetime.now(),
                is_real_data=True,
                message=f"Real price data for {len(price_data)} symbols"
            )
        else:
            return RealDataResponse(
                success=False,
                data={},
                source="none",
                timestamp=datetime.now(),
                is_real_data=False,
                message="No real data available for any symbols"
            )
            
    except Exception as e:
        logger.error(f"Error fetching real prices for {symbols}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch real prices: {str(e)}")

@router.get("/data-sources", response_model=Dict[str, Any])
async def get_data_sources():
    """Get information about available data sources and their status."""
    try:
        settings = get_settings()
        
        sources_info = {
            'coingecko': {
                'name': 'CoinGecko',
                'configured': bool(settings.api.coingecko_api_key),
                'api_key_preview': f"{settings.api.coingecko_api_key[:8]}..." if settings.api.coingecko_api_key else None,
                'description': 'Comprehensive cryptocurrency data',
                'rate_limit': '50 requests/minute'
            },
            'cryptocompare': {
                'name': 'CryptoCompare',
                'configured': bool(settings.api.cryptocompare_api_key),
                'api_key_preview': f"{settings.api.cryptocompare_api_key[:8]}..." if settings.api.cryptocompare_api_key else None,
                'description': 'Real-time and historical crypto data',
                'rate_limit': '100 requests/minute'
            },
            'coinmarketcap': {
                'name': 'CoinMarketCap',
                'configured': bool(settings.api.coinmarketcap_api_key),
                'api_key_preview': f"{settings.api.coinmarketcap_api_key[:8]}..." if settings.api.coinmarketcap_api_key else None,
                'description': 'Market cap and trading data',
                'rate_limit': '333 requests/minute'
            },
            'binance': {
                'name': 'Binance',
                'configured': bool(settings.api.binance_api_key),
                'api_key_preview': f"{settings.api.binance_api_key[:8]}..." if settings.api.binance_api_key else None,
                'description': 'Live exchange data',
                'rate_limit': '1200 requests/minute'
            }
        }
        
        configured_count = sum(1 for source in sources_info.values() if source['configured'])
        
        return {
            'sources': sources_info,
            'summary': {
                'total_sources': len(sources_info),
                'configured_sources': configured_count,
                'real_data_available': configured_count > 0
            },
            'timestamp': datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Error getting data sources info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get data sources: {str(e)}")

@router.get("/test-connection/{source}")
async def test_api_connection(source: str):
    """Test connection to a specific API source."""
    try:
        settings = get_settings()
        
        async with await create_real_data_fetcher(settings) as fetcher:
            if source.lower() == 'coingecko':
                result = await fetcher.fetch_coingecko_price('BTC')
            elif source.lower() == 'cryptocompare':
                result = await fetcher.fetch_cryptocompare_price('BTC')
            elif source.lower() == 'coinmarketcap':
                result = await fetcher.fetch_coinmarketcap_price('BTC')
            elif source.lower() == 'binance':
                result = await fetcher.fetch_binance_price('BTC')
            else:
                raise HTTPException(status_code=400, detail=f"Unknown source: {source}")
        
        if result:
            return {
                'success': True,
                'source': source.lower(),
                'test_data': result,
                'message': f"✅ {source} API is working",
                'timestamp': datetime.now()
            }
        else:
            return {
                'success': False,
                'source': source.lower(),
                'test_data': None,
                'message': f"❌ {source} API test failed",
                'timestamp': datetime.now()
            }
            
    except Exception as e:
        logger.error(f"Error testing {source} connection: {e}")
        return {
            'success': False,
            'source': source.lower(),
            'test_data': None,
            'message': f"❌ {source} API error: {str(e)}",
            'timestamp': datetime.now()
        }

@router.get("/live-demo")
async def live_demo():
    """Demonstrate live data fetching from all available sources."""
    try:
        settings = get_settings()
        demo_symbols = ['BTC', 'ETH', 'SOL']
        
        results = {}
        
        async with await create_real_data_fetcher(settings) as fetcher:
            # Test each source individually
            for symbol in demo_symbols:
                symbol_results = {}
                
                # Try each API source
                if settings.api.coingecko_api_key:
                    symbol_results['coingecko'] = await fetcher.fetch_coingecko_price(symbol)
                
                if settings.api.cryptocompare_api_key:
                    symbol_results['cryptocompare'] = await fetcher.fetch_cryptocompare_price(symbol)
                
                if settings.api.coinmarketcap_api_key:
                    symbol_results['coinmarketcap'] = await fetcher.fetch_coinmarketcap_price(symbol)
                
                if settings.api.binance_api_key:
                    symbol_results['binance'] = await fetcher.fetch_binance_price(symbol)
                
                results[symbol] = symbol_results
        
        return {
            'success': True,
            'demo_data': results,
            'message': "Live data demo completed",
            'timestamp': datetime.now(),
            'note': "This shows real data from all configured API sources"
        }
        
    except Exception as e:
        logger.error(f"Error in live demo: {e}")
        raise HTTPException(status_code=500, detail=f"Live demo failed: {str(e)}")

@router.get("/health")
async def real_data_health():
    """Health check specifically for real data functionality."""
    try:
        settings = get_settings()
        
        # Quick validation
        async with await create_real_data_fetcher(settings) as fetcher:
            validation_results = await fetcher.validate_api_keys()
        
        working_apis = [api for api, working in validation_results.items() if working]
        
        return {
            'status': 'healthy' if working_apis else 'degraded',
            'real_data_available': len(working_apis) > 0,
            'working_apis': working_apis,
            'total_configured_apis': len(validation_results),
            'message': f"Real data available from {len(working_apis)} sources" if working_apis else "No real data sources available",
            'timestamp': datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Real data health check failed: {e}")
        return {
            'status': 'unhealthy',
            'real_data_available': False,
            'working_apis': [],
            'error': str(e),
            'timestamp': datetime.now()
        }
