"""
Multi-Timeframe Signal Generator for Project Ruby.

This module integrates the multi-timeframe analysis with the existing signal
generation system to provide more reliable trading signals.
"""

import logging
import asyncio
import json
import os
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from .multi_timeframe_analysis import MultiTimeframeAnalyzer
from .signal_generator import SignalGenerator, Signal, SignalType, AssetType, TimeFrame

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define output directory for signals
SIGNALS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "signals")
os.makedirs(SIGNALS_DIR, exist_ok=True)

class MultiTimeframeSignalGenerator:
    """Generates trading signals using multi-timeframe analysis."""
    
    def __init__(self):
        """Initialize the multi-timeframe signal generator."""
        self.analyzer = MultiTimeframeAnalyzer()
        self.base_generator = SignalGenerator()
        logger.info("Initialized MultiTimeframeSignalGenerator")
    
    def _map_timeframe(self, tf_str: str) -> TimeFrame:
        """
        Map timeframe string to TimeFrame enum.
        
        Args:
            tf_str: Timeframe string (e.g., 1h, 4h)
            
        Returns:
            TimeFrame enum value
        """
        mapping = {
            "5m": TimeFrame.MIN_5,
            "15m": TimeFrame.MIN_15,
            "1h": TimeFrame.HOUR_1,
            "4h": TimeFrame.HOUR_4,
            "1d": TimeFrame.DAY_1,
            "1w": TimeFrame.WEEK_1
        }
        return mapping.get(tf_str, TimeFrame.HOUR_1)
    
    def _map_signal_type(self, signal_str: str) -> SignalType:
        """
        Map signal string to SignalType enum.
        
        Args:
            signal_str: Signal string (e.g., buy, sell)
            
        Returns:
            SignalType enum value
        """
        mapping = {
            "buy": SignalType.BUY,
            "sell": SignalType.SELL,
            "neutral": SignalType.HOLD
        }
        return mapping.get(signal_str, SignalType.HOLD)
    
    def _create_signal_from_analysis(self, symbol: str, analysis: Dict[str, Any]) -> Signal:
        """
        Create a Signal object from analysis results.
        
        Args:
            symbol: Cryptocurrency symbol
            analysis: Analysis results from multi-timeframe analyzer
            
        Returns:
            Signal object
        """
        # Extract consensus signal
        consensus = analysis["consensus"]
        signal_type = self._map_signal_type(consensus["signal"])
        confidence = consensus["confidence"]
        
        # Get current price from the 1h timeframe
        price = analysis["timeframes"].get("1h", {}).get("price", 0)
        
        # Create indicators dictionary
        indicators = {
            "multi_timeframe": {
                "consensus_strength": consensus["consensus_strength"],
                "timeframes": {}
            }
        }
        
        # Add indicators for each timeframe
        for tf, tf_data in analysis["timeframes"].items():
            indicators["multi_timeframe"]["timeframes"][tf] = {
                "signal": tf_data["signal"],
                "confidence": tf_data["confidence"],
                "trend": tf_data["indicators"]["trend"]["trend"],
                "momentum": tf_data["indicators"]["momentum"]["momentum"],
                "volatility": tf_data["indicators"]["volatility"]["volatility"],
                "rsi": tf_data["indicators"]["momentum"]["rsi"]
            }
        
        # Create signal
        signal = Signal(
            id=f"mts_{symbol.lower()}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}",
            asset_id=symbol.lower(),
            asset_symbol=symbol,
            asset_name=self._get_asset_name(symbol),
            asset_type=AssetType.CRYPTO,
            signal_type=signal_type,
            time_frame=TimeFrame.HOUR_1,  # Main timeframe is 1h
            confidence=confidence,
            price=price,
            timestamp=datetime.utcnow(),
            expiration=datetime.utcnow() + timedelta(hours=4),
            indicators=indicators,
            context_ids=[],  # No context IDs for now
            sentiment=None  # No sentiment for now
        )
        
        return signal
    
    def _get_asset_name(self, symbol: str) -> str:
        """
        Get asset name from symbol.
        
        Args:
            symbol: Cryptocurrency symbol
            
        Returns:
            Asset name
        """
        mapping = {
            "BTC": "Bitcoin",
            "ETH": "Ethereum",
            "SOL": "Solana",
            "ADA": "Cardano",
            "XRP": "Ripple",
            "DOT": "Polkadot",
            "DOGE": "Dogecoin",
            "AVAX": "Avalanche",
            "MATIC": "Polygon"
        }
        return mapping.get(symbol, symbol)
    
    async def generate_signals(self, symbols: List[str] = None) -> List[Signal]:
        """
        Generate trading signals for specified symbols.
        
        Args:
            symbols: List of cryptocurrency symbols
            
        Returns:
            List of Signal objects
        """
        if symbols is None:
            symbols = ["BTC", "ETH", "SOL", "ADA"]
        
        logger.info(f"Generating multi-timeframe signals for {len(symbols)} symbols")
        
        # Analyze each symbol
        signals = []
        for symbol in symbols:
            try:
                # Analyze symbol across multiple timeframes
                analysis = await self.analyzer.analyze_multiple_timeframes(symbol)
                
                # Create signal from analysis
                signal = self._create_signal_from_analysis(symbol, analysis)
                signals.append(signal)
                
                logger.info(f"Generated {signal.signal_type} signal for {symbol} with confidence {signal.confidence:.2f}")
            except Exception as e:
                logger.error(f"Error generating signal for {symbol}: {str(e)}")
        
        # Save signals to file
        self._save_signals(signals)
        
        return signals
    
    def _save_signals(self, signals: List[Signal]) -> None:
        """
        Save signals to file.
        
        Args:
            signals: List of Signal objects
        """
        # Convert signals to dictionaries
        signal_dicts = [signal.dict() for signal in signals]
        
        # Create output file path
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        output_file = os.path.join(SIGNALS_DIR, f"multi_timeframe_signals_{timestamp}.json")
        
        # Save to file
        with open(output_file, "w") as f:
            json.dump(signal_dicts, f, indent=2, default=str)
        
        # Also save to latest file
        latest_file = os.path.join(SIGNALS_DIR, "multi_timeframe_signals_latest.json")
        with open(latest_file, "w") as f:
            json.dump(signal_dicts, f, indent=2, default=str)
        
        logger.info(f"Saved {len(signals)} signals to {output_file}")
    
    async def generate_and_combine_signals(self) -> List[Signal]:
        """
        Generate multi-timeframe signals and combine with base signals.
        
        Returns:
            List of combined Signal objects
        """
        # Generate multi-timeframe signals
        mt_signals = await self.generate_signals()
        
        # Generate base signals
        base_signals = await self.base_generator.generate_crypto_signals()
        
        # Combine signals (prioritize multi-timeframe signals)
        combined_signals = []
        
        # Add multi-timeframe signals
        combined_signals.extend(mt_signals)
        
        # Add base signals that don't overlap with multi-timeframe signals
        mt_symbols = [signal.asset_symbol for signal in mt_signals]
        for signal in base_signals:
            if signal.asset_symbol not in mt_symbols:
                combined_signals.append(signal)
        
        # Sort by timestamp (newest first)
        combined_signals.sort(key=lambda x: x.timestamp, reverse=True)
        
        logger.info(f"Combined {len(mt_signals)} multi-timeframe signals with {len(base_signals)} base signals")
        
        return combined_signals

async def main():
    """Main function for testing the multi-timeframe signal generator."""
    generator = MultiTimeframeSignalGenerator()
    
    # Generate signals
    signals = await generator.generate_signals()
    
    print(f"Generated {len(signals)} multi-timeframe signals")
    
    # Print the first signal as an example
    if signals:
        print(f"Example signal: {signals[0].json(indent=2)}")

if __name__ == "__main__":
    asyncio.run(main())
