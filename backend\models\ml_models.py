"""
Machine Learning Models for Project Ruby.

This module implements various ML models for cryptocurrency price prediction.
"""

import os
import numpy as np
import pandas as pd
import logging
import joblib
from datetime import datetime
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Conv1D, MaxPooling1D, Flatten, GRU, Bidirectional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseModel:
    """Base class for all prediction models"""
    def __init__(self, lookback, features):
        self.lookback = lookback
        self.features = features
        self.model = None
        self.scaler = MinMaxScaler()
        self.model_dir = os.path.join('backend', 'models', 'saved_models')
        os.makedirs(self.model_dir, exist_ok=True)
        
    def preprocess_data(self, data):
        """Preprocess data for model input"""
        # Select relevant features
        if isinstance(data, pd.DataFrame):
            # Check if all required features are in the dataframe
            missing_features = [f for f in self.features if f not in data.columns]
            if missing_features:
                logger.warning(f"Missing features: {missing_features}. Using available features.")
                available_features = [f for f in self.features if f in data.columns]
                if not available_features:
                    logger.error("No required features available in data")
                    return None, None
                features_data = data[available_features].values
            else:
                features_data = data[self.features].values
        else:
            # If data is not a dataframe, assume it's already in the right format
            features_data = data
            
        # Scale data
        scaled_data = self.scaler.fit_transform(features_data)
        
        # Create sequences
        X, y = [], []
        for i in range(len(scaled_data) - self.lookback):
            X.append(scaled_data[i:i + self.lookback])
            y.append(scaled_data[i + self.lookback, 0])  # Predict price (first feature)
            
        return np.array(X), np.array(y)
    
    def predict(self, data):
        """Generate prediction for data"""
        if self.model is None:
            logger.error("Model not initialized")
            return None
            
        # Preprocess for prediction
        X, _ = self.preprocess_data(data)
        if X is None:
            logger.error("Failed to preprocess data for prediction")
            return None
            
        X_latest = X[-1].reshape(1, self.lookback, len(self.features))
        
        # Make prediction
        scaled_prediction = self.model.predict(X_latest)[0][0]
        
        # Inverse transform to get actual price
        prediction_array = np.zeros((1, len(self.features)))
        prediction_array[0, 0] = scaled_prediction
        prediction = self.scaler.inverse_transform(prediction_array)[0, 0]
        
        # Calculate confidence based on recent prediction accuracy
        confidence = self.calculate_confidence(data)
        
        # Determine direction
        current_price = data['close'].iloc[-1]
        direction = 'up' if prediction > current_price else 'down'
        
        return {
            'model_type': self.__class__.__name__,
            'prediction': float(prediction),
            'direction': direction,
            'confidence': confidence,
            'features_used': self.features,
            'lookback_period': self.lookback
        }
    
    def calculate_confidence(self, data):
        """Calculate confidence based on recent prediction accuracy"""
        # In a real implementation, this would be based on backtesting
        # For this demo, we'll return a random confidence level
        return np.random.uniform(0.6, 0.9)
    
    def save_model(self, symbol):
        """Save model to disk"""
        if self.model is None:
            logger.error("Cannot save uninitialized model")
            return False
            
        try:
            model_path = os.path.join(self.model_dir, f"{symbol}_{self.__class__.__name__}.h5")
            self.model.save(model_path)
            
            # Save scaler
            scaler_path = os.path.join(self.model_dir, f"{symbol}_{self.__class__.__name__}_scaler.pkl")
            joblib.dump(self.scaler, scaler_path)
            
            logger.info(f"Saved model to {model_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving model: {str(e)}")
            return False
    
    def load_model(self, symbol):
        """Load model from disk"""
        try:
            model_path = os.path.join(self.model_dir, f"{symbol}_{self.__class__.__name__}.h5")
            scaler_path = os.path.join(self.model_dir, f"{symbol}_{self.__class__.__name__}_scaler.pkl")
            
            if os.path.exists(model_path) and os.path.exists(scaler_path):
                self.model = load_model(model_path)
                self.scaler = joblib.load(scaler_path)
                logger.info(f"Loaded model from {model_path}")
                return True
            else:
                logger.warning(f"Model files not found for {symbol}_{self.__class__.__name__}")
                return False
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False


class LSTMModel(BaseModel):
    """Long Short-Term Memory model for time series prediction"""
    def __init__(self, lookback=60, features=['close', 'volume', 'rsi']):
        super().__init__(lookback, features)
        self.build_model()
        
    def build_model(self):
        """Build LSTM model architecture"""
        try:
            self.model = Sequential([
                LSTM(units=50, return_sequences=True, input_shape=(self.lookback, len(self.features))),
                Dropout(0.2),
                LSTM(units=50, return_sequences=False),
                Dropout(0.2),
                Dense(units=1)
            ])
            self.model.compile(optimizer='adam', loss='mean_squared_error')
            logger.info("Built LSTM model")
        except Exception as e:
            logger.error(f"Error building LSTM model: {str(e)}")
    
    def calculate_confidence(self, data):
        """LSTM models tend to be good at capturing long-term trends"""
        # In a real implementation, this would be based on backtesting
        # For this demo, we'll return a confidence level based on the symbol
        symbol = data.index.name if data.index.name else "UNKNOWN"
        
        if symbol in ['BTC', 'ETH']:
            return np.random.uniform(0.75, 0.9)  # Higher confidence for major coins
        else:
            return np.random.uniform(0.6, 0.8)


class CNNModel(BaseModel):
    """Convolutional Neural Network model for time series prediction"""
    def __init__(self, lookback=30, features=['close', 'volume', 'macd']):
        super().__init__(lookback, features)
        self.build_model()
        
    def build_model(self):
        """Build CNN model architecture"""
        try:
            self.model = Sequential([
                Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=(self.lookback, len(self.features))),
                MaxPooling1D(pool_size=2),
                Conv1D(filters=128, kernel_size=3, activation='relu'),
                MaxPooling1D(pool_size=2),
                Flatten(),
                Dense(units=50, activation='relu'),
                Dense(units=1)
            ])
            self.model.compile(optimizer='adam', loss='mean_squared_error')
            logger.info("Built CNN model")
        except Exception as e:
            logger.error(f"Error building CNN model: {str(e)}")
    
    def calculate_confidence(self, data):
        """CNNs tend to be good at capturing patterns"""
        # In a real implementation, this would be based on backtesting
        # For this demo, we'll return a confidence level based on the symbol
        symbol = data.index.name if data.index.name else "UNKNOWN"
        
        if symbol in ['SOL', 'ADA']:
            return np.random.uniform(0.7, 0.85)  # Higher confidence for certain coins
        else:
            return np.random.uniform(0.65, 0.8)


class GRUModel(BaseModel):
    """Gated Recurrent Unit model for time series prediction"""
    def __init__(self, lookback=14, features=['close', 'volume', 'bollinger']):
        super().__init__(lookback, features)
        self.build_model()
        
    def build_model(self):
        """Build GRU model architecture"""
        try:
            self.model = Sequential([
                Bidirectional(GRU(units=50, return_sequences=True, input_shape=(self.lookback, len(self.features)))),
                Dropout(0.2),
                GRU(units=50, return_sequences=False),
                Dropout(0.2),
                Dense(units=1)
            ])
            self.model.compile(optimizer='adam', loss='mean_squared_error')
            logger.info("Built GRU model")
        except Exception as e:
            logger.error(f"Error building GRU model: {str(e)}")
    
    def calculate_confidence(self, data):
        """GRUs tend to be good at capturing recent changes"""
        # In a real implementation, this would be based on backtesting
        # For this demo, we'll return a confidence level based on the symbol
        symbol = data.index.name if data.index.name else "UNKNOWN"
        
        if symbol in ['ETH', 'ADA']:
            return np.random.uniform(0.7, 0.85)  # Higher confidence for certain coins
        else:
            return np.random.uniform(0.65, 0.8)


class TransformerModel(BaseModel):
    """Simple Transformer-inspired model for time series prediction"""
    def __init__(self, lookback=20, features=['close', 'volume', 'rsi', 'macd']):
        super().__init__(lookback, features)
        self.build_model()
        
    def build_model(self):
        """Build Transformer-inspired model architecture"""
        try:
            # For simplicity, we'll use a CNN-LSTM hybrid as a stand-in for a transformer
            self.model = Sequential([
                Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=(self.lookback, len(self.features))),
                MaxPooling1D(pool_size=2),
                LSTM(units=50, return_sequences=False),
                Dropout(0.2),
                Dense(units=20, activation='relu'),
                Dense(units=1)
            ])
            self.model.compile(optimizer='adam', loss='mean_squared_error')
            logger.info("Built Transformer-inspired model")
        except Exception as e:
            logger.error(f"Error building Transformer model: {str(e)}")
    
    def calculate_confidence(self, data):
        """Transformers tend to be good at capturing complex relationships"""
        # In a real implementation, this would be based on backtesting
        # For this demo, we'll return a confidence level based on the symbol
        symbol = data.index.name if data.index.name else "UNKNOWN"
        
        if symbol in ['BTC']:
            return np.random.uniform(0.75, 0.9)  # Higher confidence for BTC
        elif symbol in ['SOL']:
            return np.random.uniform(0.7, 0.85)  # Higher confidence for SOL
        else:
            return np.random.uniform(0.65, 0.8)


# For testing
if __name__ == "__main__":
    # Create dummy data
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
    data = pd.DataFrame({
        'close': np.random.normal(100, 10, 100),
        'volume': np.random.normal(1000000, 100000, 100),
        'rsi': np.random.normal(50, 10, 100),
        'macd': np.random.normal(0, 1, 100),
        'bollinger': np.random.normal(0, 1, 100)
    }, index=dates)
    
    # Test LSTM model
    lstm_model = LSTMModel()
    lstm_prediction = lstm_model.predict(data)
    print("LSTM Prediction:", lstm_prediction)
    
    # Test CNN model
    cnn_model = CNNModel()
    cnn_prediction = cnn_model.predict(data)
    print("CNN Prediction:", cnn_prediction)
    
    # Test GRU model
    gru_model = GRUModel()
    gru_prediction = gru_model.predict(data)
    print("GRU Prediction:", gru_prediction)
    
    # Test Transformer model
    transformer_model = TransformerModel()
    transformer_prediction = transformer_model.predict(data)
    print("Transformer Prediction:", transformer_prediction)
