"""
Reddit API Client for Sentiment Analysis

This module fetches posts and comments from Reddit for sentiment analysis.
"""

import logging
import base64
import httpx
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
REDDIT_API_URL = "https://oauth.reddit.com"
REDDIT_AUTH_URL = "https://www.reddit.com/api/v1/access_token"
DEFAULT_SUBREDDITS = ["CryptoCurrency", "Bitcoin", "Ethereum", "CryptoMarkets", "Binance"]
REQUEST_TIMEOUT = 30  # seconds
USER_AGENT = "python:mcp-trading-platform:v1.0 (by /u/mcp_trading_bot)"

class RedditAPIClient:
    """Client for fetching data from Reddit API."""
    
    def __init__(self, client_id: Optional[str] = None, client_secret: Optional[str] = None):
        """
        Initialize the Reddit API client.
        
        Args:
            client_id: Reddit API client ID
            client_secret: Reddit API client secret
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.token_expiry = datetime.now()
        logger.info("Reddit API client initialized")
    
    async def authenticate(self) -> bool:
        """
        Authenticate with the Reddit API.
        
        Returns:
            True if authentication was successful, False otherwise
        """
        if not self.client_id or not self.client_secret:
            logger.warning("No Reddit API credentials provided")
            return False
            
        # Check if token is still valid
        if self.access_token and self.token_expiry > datetime.now():
            return True
            
        try:
            # Prepare authentication request
            auth = base64.b64encode(f"{self.client_id}:{self.client_secret}".encode()).decode()
            headers = {
                "User-Agent": USER_AGENT,
                "Authorization": f"Basic {auth}"
            }
            data = {
                "grant_type": "client_credentials"
            }
            
            # Make authentication request
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.post(
                    REDDIT_AUTH_URL,
                    headers=headers,
                    data=data
                )
                response.raise_for_status()
                auth_data = response.json()
                
                # Store token and expiry
                self.access_token = auth_data["access_token"]
                self.token_expiry = datetime.now() + timedelta(seconds=auth_data["expires_in"] - 60)  # Subtract 60 seconds for safety
                
                logger.info("Reddit API authentication successful")
                return True
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error authenticating with Reddit API: {e}")
            return False
        except httpx.RequestError as e:
            logger.error(f"Request error authenticating with Reddit API: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error authenticating with Reddit API: {e}")
            return False
    
    async def fetch_subreddit_posts(
        self, 
        subreddit: str, 
        time_filter: str = "day", 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch posts from a subreddit.
        
        Args:
            subreddit: Name of the subreddit
            time_filter: Time filter (hour, day, week, month, year, all)
            limit: Maximum number of posts to fetch
            
        Returns:
            List of posts
        """
        if not await self.authenticate():
            logger.warning("Using sample data due to authentication failure")
            return self._get_sample_posts(subreddit)
        
        try:
            # Prepare request
            url = f"{REDDIT_API_URL}/r/{subreddit}/top"
            headers = {
                "User-Agent": USER_AGENT,
                "Authorization": f"Bearer {self.access_token}"
            }
            params = {
                "t": time_filter,
                "limit": limit
            }
            
            # Make request
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(
                    url,
                    headers=headers,
                    params=params
                )
                response.raise_for_status()
                data = response.json()
                
                # Extract posts
                posts = []
                for child in data["data"]["children"]:
                    post = child["data"]
                    posts.append({
                        "id": post["id"],
                        "title": post["title"],
                        "selftext": post.get("selftext", ""),
                        "score": post["score"],
                        "upvote_ratio": post.get("upvote_ratio", 0),
                        "num_comments": post["num_comments"],
                        "created_utc": post["created_utc"],
                        "url": post["url"],
                        "permalink": post["permalink"],
                        "subreddit": post["subreddit"],
                        "author": post["author"]
                    })
                
                logger.info(f"Fetched {len(posts)} posts from r/{subreddit}")
                return posts
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching posts from r/{subreddit}: {e}")
            if e.response.status_code == 429:
                logger.warning("Rate limit exceeded for Reddit API")
            return self._get_sample_posts(subreddit)
        except httpx.RequestError as e:
            logger.error(f"Request error fetching posts from r/{subreddit}: {e}")
            return self._get_sample_posts(subreddit)
        except Exception as e:
            logger.error(f"Unexpected error fetching posts from r/{subreddit}: {e}")
            return self._get_sample_posts(subreddit)
    
    async def fetch_post_comments(self, post_id: str, subreddit: str) -> List[Dict[str, Any]]:
        """
        Fetch comments for a post.
        
        Args:
            post_id: ID of the post
            subreddit: Name of the subreddit
            
        Returns:
            List of comments
        """
        if not await self.authenticate():
            logger.warning("Using sample data due to authentication failure")
            return self._get_sample_comments()
        
        try:
            # Prepare request
            url = f"{REDDIT_API_URL}/r/{subreddit}/comments/{post_id}"
            headers = {
                "User-Agent": USER_AGENT,
                "Authorization": f"Bearer {self.access_token}"
            }
            
            # Make request
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(
                    url,
                    headers=headers
                )
                response.raise_for_status()
                data = response.json()
                
                # Extract comments
                comments = []
                if len(data) > 1:  # First element is the post, second is comments
                    self._extract_comments(data[1]["data"]["children"], comments)
                
                logger.info(f"Fetched {len(comments)} comments for post {post_id}")
                return comments
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching comments for post {post_id}: {e}")
            return self._get_sample_comments()
        except httpx.RequestError as e:
            logger.error(f"Request error fetching comments for post {post_id}: {e}")
            return self._get_sample_comments()
        except Exception as e:
            logger.error(f"Unexpected error fetching comments for post {post_id}: {e}")
            return self._get_sample_comments()
    
    def _extract_comments(self, children: List[Dict[str, Any]], result: List[Dict[str, Any]], depth: int = 0):
        """
        Recursively extract comments from the Reddit API response.
        
        Args:
            children: List of comment children
            result: List to store extracted comments
            depth: Current depth in the comment tree
        """
        for child in children:
            if child["kind"] == "t1":  # Comment
                comment = child["data"]
                result.append({
                    "id": comment["id"],
                    "body": comment["body"],
                    "score": comment["score"],
                    "created_utc": comment["created_utc"],
                    "author": comment.get("author", "[deleted]"),
                    "depth": depth
                })
                
                # Process replies
                if "replies" in comment and comment["replies"] and "data" in comment["replies"]:
                    self._extract_comments(comment["replies"]["data"]["children"], result, depth + 1)
    
    async def search_posts(
        self, 
        query: str, 
        subreddit: str = None, 
        time_filter: str = "week", 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Search for posts matching a query.
        
        Args:
            query: Search query
            subreddit: Name of the subreddit (optional)
            time_filter: Time filter (hour, day, week, month, year, all)
            limit: Maximum number of posts to fetch
            
        Returns:
            List of posts
        """
        if not await self.authenticate():
            logger.warning("Using sample data due to authentication failure")
            return self._get_sample_posts(subreddit or "CryptoCurrency")
        
        try:
            # Prepare request
            if subreddit:
                url = f"{REDDIT_API_URL}/r/{subreddit}/search"
            else:
                url = f"{REDDIT_API_URL}/search"
                
            headers = {
                "User-Agent": USER_AGENT,
                "Authorization": f"Bearer {self.access_token}"
            }
            params = {
                "q": query,
                "t": time_filter,
                "limit": limit,
                "sort": "relevance"
            }
            
            # Make request
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                response = await client.get(
                    url,
                    headers=headers,
                    params=params
                )
                response.raise_for_status()
                data = response.json()
                
                # Extract posts
                posts = []
                for child in data["data"]["children"]:
                    post = child["data"]
                    posts.append({
                        "id": post["id"],
                        "title": post["title"],
                        "selftext": post.get("selftext", ""),
                        "score": post["score"],
                        "upvote_ratio": post.get("upvote_ratio", 0),
                        "num_comments": post["num_comments"],
                        "created_utc": post["created_utc"],
                        "url": post["url"],
                        "permalink": post["permalink"],
                        "subreddit": post["subreddit"],
                        "author": post["author"]
                    })
                
                logger.info(f"Found {len(posts)} posts matching query '{query}'")
                return posts
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error searching for posts: {e}")
            return self._get_sample_posts(subreddit or "CryptoCurrency")
        except httpx.RequestError as e:
            logger.error(f"Request error searching for posts: {e}")
            return self._get_sample_posts(subreddit or "CryptoCurrency")
        except Exception as e:
            logger.error(f"Unexpected error searching for posts: {e}")
            return self._get_sample_posts(subreddit or "CryptoCurrency")
    
    def _get_sample_posts(self, subreddit: str) -> List[Dict[str, Any]]:
        """
        Get sample posts for testing.
        
        Args:
            subreddit: Name of the subreddit
            
        Returns:
            List of sample posts
        """
        logger.info(f"Using sample posts for r/{subreddit}")
        
        now = datetime.now().timestamp()
        
        # Sample posts
        sample_posts = [
            {
                "id": "sample1",
                "title": f"Bitcoin price analysis: BTC could reach new ATH soon",
                "selftext": "Looking at the recent price action and market sentiment, Bitcoin seems poised for a significant move upward. The institutional adoption continues to grow, and the technical indicators are showing bullish signals.",
                "score": 1250,
                "upvote_ratio": 0.92,
                "num_comments": 342,
                "created_utc": now - 3600,  # 1 hour ago
                "url": f"https://www.reddit.com/r/{subreddit}/comments/sample1",
                "permalink": f"/r/{subreddit}/comments/sample1",
                "subreddit": subreddit,
                "author": "crypto_analyst"
            },
            {
                "id": "sample2",
                "title": f"Ethereum's merge is a game changer for the entire crypto ecosystem",
                "selftext": "The successful transition to proof-of-stake has significant implications for Ethereum's energy consumption, issuance rate, and overall network security. This could lead to increased adoption and higher valuations in the long term.",
                "score": 876,
                "upvote_ratio": 0.88,
                "num_comments": 215,
                "created_utc": now - 7200,  # 2 hours ago
                "url": f"https://www.reddit.com/r/{subreddit}/comments/sample2",
                "permalink": f"/r/{subreddit}/comments/sample2",
                "subreddit": subreddit,
                "author": "eth_enthusiast"
            },
            {
                "id": "sample3",
                "title": f"Market sentiment analysis: Fear & Greed Index at 'Extreme Greed'",
                "selftext": "The crypto Fear & Greed Index has reached 'Extreme Greed' levels, which historically has been a contrarian indicator. It might be a good time to take some profits or at least be cautious with new investments.",
                "score": 543,
                "upvote_ratio": 0.76,
                "num_comments": 189,
                "created_utc": now - 10800,  # 3 hours ago
                "url": f"https://www.reddit.com/r/{subreddit}/comments/sample3",
                "permalink": f"/r/{subreddit}/comments/sample3",
                "subreddit": subreddit,
                "author": "market_watcher"
            }
        ]
        
        return sample_posts
    
    def _get_sample_comments(self) -> List[Dict[str, Any]]:
        """
        Get sample comments for testing.
        
        Returns:
            List of sample comments
        """
        logger.info("Using sample comments")
        
        now = datetime.now().timestamp()
        
        # Sample comments
        sample_comments = [
            {
                "id": "comment1",
                "body": "I agree with this analysis. The market fundamentals are strong, and we're seeing increased institutional adoption.",
                "score": 45,
                "created_utc": now - 1800,  # 30 minutes ago
                "author": "crypto_bull",
                "depth": 0
            },
            {
                "id": "comment2",
                "body": "I'm not so sure. The macro economic conditions are still uncertain, and we might see more downside before a sustainable rally.",
                "score": 32,
                "created_utc": now - 2700,  # 45 minutes ago
                "author": "cautious_investor",
                "depth": 0
            },
            {
                "id": "comment3",
                "body": "Technical analysis suggests a potential breakout above the resistance level at $60k.",
                "score": 28,
                "created_utc": now - 3600,  # 1 hour ago
                "author": "chart_analyst",
                "depth": 1
            }
        ]
        
        return sample_comments

async def main():
    """Main function for testing the Reddit API client."""
    # Create client with sample credentials
    client = RedditAPIClient(
        client_id="your_client_id",
        client_secret="your_client_secret"
    )
    
    # Fetch posts from a subreddit
    posts = await client.fetch_subreddit_posts("CryptoCurrency")
    print(f"Fetched {len(posts)} posts from r/CryptoCurrency")
    
    if posts:
        # Print the first post as an example
        print(f"\nExample post: {posts[0]['title']}")
        print(f"Score: {posts[0]['score']}")
        print(f"Comments: {posts[0]['num_comments']}")
        
        # Fetch comments for the first post
        comments = await client.fetch_post_comments(posts[0]['id'], "CryptoCurrency")
        print(f"\nFetched {len(comments)} comments")
        
        if comments:
            # Print the first comment as an example
            print(f"\nExample comment: {comments[0]['body'][:100]}...")
    
    # Search for posts
    search_results = await client.search_posts("bitcoin price")
    print(f"\nFound {len(search_results)} posts matching 'bitcoin price'")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
