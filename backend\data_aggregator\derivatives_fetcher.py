"""
Derivatives Data Fetcher

This module fetches data from cryptocurrency derivatives markets (futures and options)
to provide insights into market sentiment and potential price movements.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

import httpx

from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# API endpoints
BINANCE_API_URL = "https://fapi.binance.com"
DERIBIT_API_URL = "https://www.deribit.com/api/v2"
BYBIT_API_URL = "https://api.bybit.com"

# Default assets to track
DEFAULT_ASSETS = ["BTC", "ETH", "SOL", "BNB", "XRP"]

class DerivativesData(BaseModel):
    """Model for derivatives market data."""
    asset_symbol: str
    contract_type: str  # "futures", "options", "perpetual"
    exchange: str
    expiry: Optional[datetime] = None
    price: float
    volume: float
    open_interest: float
    funding_rate: Optional[float] = None
    implied_volatility: Optional[float] = None
    timestamp: datetime
    additional_data: Optional[Dict[str, Any]] = None

class DerivativesFetcher:
    """Fetches data from cryptocurrency derivatives markets."""

    def __init__(self, api_keys: Dict[str, Dict[str, str]] = None):
        """
        Initialize the derivatives data fetcher.
        
        Args:
            api_keys: Dictionary of API keys for different exchanges
        """
        self.api_keys = api_keys or {}
        self.tracked_assets = DEFAULT_ASSETS
        logger.info("Derivatives data fetcher initialized")

    async def fetch_binance_futures(self, symbol: str = "BTCUSDT") -> Optional[DerivativesData]:
        """
        Fetch futures data from Binance.
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            DerivativesData object with futures data
        """
        try:
            # Fetch mark price and funding rate
            url = f"{BINANCE_API_URL}/fapi/v1/premiumIndex"
            params = {"symbol": symbol}
            headers = {}
            
            if "binance" in self.api_keys:
                headers["X-MBX-APIKEY"] = self.api_keys["binance"]["api_key"]
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                mark_price_data = response.json()
                
                # Fetch open interest
                url = f"{BINANCE_API_URL}/fapi/v1/openInterest"
                params = {"symbol": symbol}
                
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                open_interest_data = response.json()
                
                # Fetch 24h volume
                url = f"{BINANCE_API_URL}/fapi/v1/ticker/24hr"
                params = {"symbol": symbol}
                
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                volume_data = response.json()
                
                # Create derivatives data object
                return DerivativesData(
                    asset_symbol=symbol.replace("USDT", ""),
                    contract_type="perpetual",
                    exchange="binance",
                    price=float(mark_price_data["markPrice"]),
                    volume=float(volume_data["volume"]),
                    open_interest=float(open_interest_data["openInterest"]),
                    funding_rate=float(mark_price_data["lastFundingRate"]),
                    timestamp=datetime.now(timezone.utc),
                    additional_data={
                        "index_price": float(mark_price_data["indexPrice"]),
                        "next_funding_time": datetime.fromtimestamp(
                            mark_price_data["nextFundingTime"] / 1000, 
                            tz=timezone.utc
                        ),
                        "price_change_percent": float(volume_data["priceChangePercent"])
                    }
                )
        
        except Exception as e:
            logger.error(f"Error fetching Binance futures data for {symbol}: {str(e)}")
            return None

    async def fetch_deribit_options(self, symbol: str = "BTC") -> List[DerivativesData]:
        """
        Fetch options data from Deribit.
        
        Args:
            symbol: Asset symbol
            
        Returns:
            List of DerivativesData objects with options data
        """
        try:
            # Fetch options summary
            url = f"{DERIBIT_API_URL}/public/get_book_summary_by_currency"
            params = {
                "currency": symbol,
                "kind": "option"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                options_data = []
                if "result" in data:
                    # Group by expiration date
                    expirations = {}
                    for option in data["result"]:
                        expiry = datetime.fromtimestamp(
                            option["instrument_name"].split("-")[1], 
                            tz=timezone.utc
                        )
                        expiry_str = expiry.strftime("%Y-%m-%d")
                        
                        if expiry_str not in expirations:
                            expirations[expiry_str] = {
                                "calls": [],
                                "puts": [],
                                "expiry": expiry
                            }
                        
                        option_type = "call" if "C" in option["instrument_name"] else "put"
                        if option_type == "call":
                            expirations[expiry_str]["calls"].append(option)
                        else:
                            expirations[expiry_str]["puts"].append(option)
                    
                    # Create derivatives data for each expiration
                    for expiry_str, data in expirations.items():
                        # Calculate total volume and open interest
                        call_volume = sum(float(option["volume"] or 0) for option in data["calls"])
                        put_volume = sum(float(option["volume"] or 0) for option in data["puts"])
                        call_oi = sum(float(option["open_interest"] or 0) for option in data["calls"])
                        put_oi = sum(float(option["open_interest"] or 0) for option in data["puts"])
                        
                        # Calculate volume-weighted average IV
                        call_iv = sum(float(option["mark_iv"] or 0) * float(option["volume"] or 1) for option in data["calls"])
                        put_iv = sum(float(option["mark_iv"] or 0) * float(option["volume"] or 1) for option in data["puts"])
                        total_volume = call_volume + put_volume
                        
                        if total_volume > 0:
                            avg_iv = (call_iv + put_iv) / total_volume
                        else:
                            # Simple average if no volume
                            call_count = len(data["calls"])
                            put_count = len(data["puts"])
                            total_count = call_count + put_count
                            
                            if total_count > 0:
                                call_avg_iv = sum(float(option["mark_iv"] or 0) for option in data["calls"]) / call_count if call_count > 0 else 0
                                put_avg_iv = sum(float(option["mark_iv"] or 0) for option in data["puts"]) / put_count if put_count > 0 else 0
                                avg_iv = (call_avg_iv * call_count + put_avg_iv * put_count) / total_count
                            else:
                                avg_iv = 0
                        
                        # Create derivatives data object
                        options_data.append(DerivativesData(
                            asset_symbol=symbol,
                            contract_type="options",
                            exchange="deribit",
                            expiry=data["expiry"],
                            price=0,  # Not applicable for options summary
                            volume=call_volume + put_volume,
                            open_interest=call_oi + put_oi,
                            implied_volatility=avg_iv,
                            timestamp=datetime.now(timezone.utc),
                            additional_data={
                                "call_volume": call_volume,
                                "put_volume": put_volume,
                                "call_open_interest": call_oi,
                                "put_open_interest": put_oi,
                                "put_call_ratio": put_volume / call_volume if call_volume > 0 else 0,
                                "put_call_oi_ratio": put_oi / call_oi if call_oi > 0 else 0
                            }
                        ))
                
                return options_data
        
        except Exception as e:
            logger.error(f"Error fetching Deribit options data for {symbol}: {str(e)}")
            return []

    async def fetch_bybit_futures(self, symbol: str = "BTCUSD") -> Optional[DerivativesData]:
        """
        Fetch futures data from Bybit.
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            DerivativesData object with futures data
        """
        try:
            # Fetch ticker data
            url = f"{BYBIT_API_URL}/v2/public/tickers"
            params = {"symbol": symbol}
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "result" in data and len(data["result"]) > 0:
                    ticker = data["result"][0]
                    
                    # Fetch funding rate
                    url = f"{BYBIT_API_URL}/v2/public/funding/prev-funding-rate"
                    params = {"symbol": symbol}
                    
                    response = await client.get(url, params=params)
                    response.raise_for_status()
                    funding_data = response.json()
                    
                    funding_rate = 0
                    if "result" in funding_data and funding_data["result"]:
                        funding_rate = float(funding_data["result"]["funding_rate"])
                    
                    # Create derivatives data object
                    return DerivativesData(
                        asset_symbol=symbol.replace("USD", ""),
                        contract_type="perpetual",
                        exchange="bybit",
                        price=float(ticker["last_price"]),
                        volume=float(ticker["volume_24h"]),
                        open_interest=float(ticker["open_interest"]),
                        funding_rate=funding_rate,
                        timestamp=datetime.now(timezone.utc),
                        additional_data={
                            "index_price": float(ticker["index_price"]),
                            "price_change_percent": float(ticker["price_24h_pcnt"]) * 100,
                            "bid_price": float(ticker["bid_price"]),
                            "ask_price": float(ticker["ask_price"])
                        }
                    )
                
                return None
        
        except Exception as e:
            logger.error(f"Error fetching Bybit futures data for {symbol}: {str(e)}")
            return None

    async def fetch_all_derivatives(self) -> List[DerivativesData]:
        """
        Fetch all derivatives data for tracked assets.
        
        Returns:
            List of DerivativesData objects
        """
        all_data = []
        
        # Fetch Binance futures data
        for asset in self.tracked_assets:
            symbol = f"{asset}USDT"
            futures_data = await self.fetch_binance_futures(symbol)
            if futures_data:
                all_data.append(futures_data)
        
        # Fetch Deribit options data
        for asset in ["BTC", "ETH"]:  # Deribit only supports BTC and ETH options
            if asset in self.tracked_assets:
                options_data = await self.fetch_deribit_options(asset)
                all_data.extend(options_data)
        
        # Fetch Bybit futures data
        for asset in self.tracked_assets:
            symbol = f"{asset}USD"
            futures_data = await self.fetch_bybit_futures(symbol)
            if futures_data:
                all_data.append(futures_data)
        
        logger.info(f"Fetched {len(all_data)} derivatives data points")
        return all_data

    def to_context_items(self, derivatives_data: List[DerivativesData]) -> List[Dict[str, Any]]:
        """
        Convert derivatives data to context items for MCP.
        
        Args:
            derivatives_data: List of DerivativesData objects
            
        Returns:
            List of context items
        """
        context_items = []
        
        for data in derivatives_data:
            context_item = {
                "id": f"derivatives_{data.asset_symbol}_{data.contract_type}_{data.exchange}_{uuid.uuid4()}",
                "type": "derivatives_data",
                "content": {
                    "asset_symbol": data.asset_symbol,
                    "contract_type": data.contract_type,
                    "exchange": data.exchange,
                    "price": data.price,
                    "volume": data.volume,
                    "open_interest": data.open_interest
                },
                "timestamp": data.timestamp.isoformat(),
                "source": data.exchange,
                "confidence": 1.0
            }
            
            if data.funding_rate is not None:
                context_item["content"]["funding_rate"] = data.funding_rate
            
            if data.implied_volatility is not None:
                context_item["content"]["implied_volatility"] = data.implied_volatility
            
            if data.expiry is not None:
                context_item["content"]["expiry"] = data.expiry.isoformat()
            
            if data.additional_data:
                for key, value in data.additional_data.items():
                    if isinstance(value, datetime):
                        context_item["content"][key] = value.isoformat()
                    else:
                        context_item["content"][key] = value
            
            context_items.append(context_item)
        
        return context_items

    def analyze_derivatives_sentiment(self, derivatives_data: List[DerivativesData]) -> Dict[str, Any]:
        """
        Analyze derivatives data to determine market sentiment.
        
        Args:
            derivatives_data: List of DerivativesData objects
            
        Returns:
            Dictionary with sentiment analysis results
        """
        # Group data by asset
        asset_data = {}
        for data in derivatives_data:
            if data.asset_symbol not in asset_data:
                asset_data[data.asset_symbol] = []
            asset_data[data.asset_symbol].append(data)
        
        sentiment_results = {}
        
        for asset, data_list in asset_data.items():
            # Initialize sentiment metrics
            sentiment = {
                "funding_rate_sentiment": 0,
                "open_interest_sentiment": 0,
                "options_sentiment": 0,
                "overall_sentiment": 0,
                "confidence": 0
            }
            
            # Analyze funding rates (positive funding rate = bearish, negative = bullish)
            funding_rates = [d.funding_rate for d in data_list if d.funding_rate is not None]
            if funding_rates:
                avg_funding_rate = sum(funding_rates) / len(funding_rates)
                sentiment["funding_rate_sentiment"] = -avg_funding_rate * 10  # Invert and scale
            
            # Analyze open interest changes (need historical data for this)
            # For now, we'll just use the raw value
            open_interests = [d.open_interest for d in data_list]
            if open_interests:
                total_oi = sum(open_interests)
                sentiment["open_interest_sentiment"] = 0  # Neutral without historical comparison
            
            # Analyze options data (put/call ratio)
            options_data = [d for d in data_list if d.contract_type == "options"]
            if options_data:
                # Average put/call ratio across all expirations
                put_call_ratios = [
                    d.additional_data.get("put_call_ratio", 1.0) 
                    for d in options_data 
                    if d.additional_data and "put_call_ratio" in d.additional_data
                ]
                
                if put_call_ratios:
                    avg_put_call_ratio = sum(put_call_ratios) / len(put_call_ratios)
                    # Put/call ratio > 1 is bearish, < 1 is bullish
                    sentiment["options_sentiment"] = (1 - avg_put_call_ratio) * 2  # Scale to [-1, 1]
            
            # Calculate overall sentiment
            weights = {
                "funding_rate_sentiment": 0.3,
                "open_interest_sentiment": 0.2,
                "options_sentiment": 0.5
            }
            
            valid_sentiments = {
                k: v for k, v in sentiment.items() 
                if k in weights and v != 0
            }
            
            if valid_sentiments:
                total_weight = sum(weights[k] for k in valid_sentiments.keys())
                if total_weight > 0:
                    sentiment["overall_sentiment"] = sum(
                        sentiment[k] * weights[k] for k in valid_sentiments.keys()
                    ) / total_weight
                    
                    # Calculate confidence based on available data points
                    sentiment["confidence"] = min(1.0, len(valid_sentiments) / len(weights))
            
            sentiment_results[asset] = sentiment
        
        return sentiment_results

async def main():
    """Main function for testing the derivatives data fetcher."""
    # Sample API keys (replace with actual keys in production)
    api_keys = {
        "binance": {
            "api_key": "YOUR_BINANCE_API_KEY",
            "api_secret": "YOUR_BINANCE_API_SECRET"
        },
        "deribit": {
            "api_key": "YOUR_DERIBIT_API_KEY",
            "api_secret": "YOUR_DERIBIT_API_SECRET"
        },
        "bybit": {
            "api_key": "YOUR_BYBIT_API_KEY",
            "api_secret": "YOUR_BYBIT_API_SECRET"
        }
    }
    
    fetcher = DerivativesFetcher(api_keys)
    
    # Fetch all derivatives data
    derivatives_data = await fetcher.fetch_all_derivatives()
    
    # Convert to context items
    context_items = fetcher.to_context_items(derivatives_data)
    
    # Analyze sentiment
    sentiment = fetcher.analyze_derivatives_sentiment(derivatives_data)
    
    # Print results
    print(f"Fetched {len(derivatives_data)} derivatives data points")
    print(f"Generated {len(context_items)} context items")
    
    if sentiment:
        print("\nDerivatives Market Sentiment:")
        for asset, data in sentiment.items():
            print(f"\n{asset}:")
            for key, value in data.items():
                print(f"  {key}: {value:.4f}")

if __name__ == "__main__":
    asyncio.run(main())
