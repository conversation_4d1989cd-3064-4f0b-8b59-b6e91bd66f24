"""
Simple FastAPI server to provide cryptocurrency price data and trading signals
"""

import json
import random
import math
import numpy as np
from datetime import datetime, timedelta
import os
import sys
import requests
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import importlib.util

# Import API keys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from config.api_keys import CRYPTOCOMPARE_API_KEY
except ImportError:
    CRYPTOCOMPARE_API_KEY = "****************************************************************"

# Create FastAPI app
app = FastAPI(title="Cryptocurrency Price API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Import consensus endpoints if available
try:
    # Check if consensus_engine.py exists
    consensus_engine_path = os.path.join(os.path.dirname(__file__), 'models', 'consensus_engine.py')
    if os.path.exists(consensus_engine_path):
        # Import consensus endpoints
        try:
            from backend.api.consensus_endpoints import router as consensus_router
            # Include consensus router
            app.include_router(consensus_router, prefix="/api")
            print("Consensus API endpoints registered successfully")
        except ImportError:
            # Try relative import
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from api.consensus_endpoints import router as consensus_router
            # Include consensus router
            app.include_router(consensus_router, prefix="/api")
            print("Consensus API endpoints registered successfully (using relative import)")
    else:
        print("Consensus engine not found, consensus endpoints not registered")
except Exception as e:
    print(f"Error registering consensus endpoints: {str(e)}")

# CryptoCompare API configuration
CC_API_KEY = CRYPTOCOMPARE_API_KEY
CC_BASE_URL = "https://min-api.cryptocompare.com"

# Cache for price data
price_cache = {}
historical_cache = {}
indicators_cache = {}

# Models for trading signals
class TradingSignal(BaseModel):
    id: str
    asset_symbol: str
    asset_name: str
    signal_type: str  # 'buy', 'sell', 'hold'
    time_frame: str
    confidence: float
    price: float
    timestamp: str
    indicators: Dict[str, Any] = {}

class TechnicalIndicator(BaseModel):
    name: str
    value: float
    interpretation: str

# Cache expiration (in seconds)
PRICE_CACHE_EXPIRY = 60  # 1 minute
HISTORICAL_CACHE_EXPIRY = 3600  # 1 hour
INDICATORS_CACHE_EXPIRY = 300  # 5 minutes

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Cryptocurrency Price API is running"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/cc/price/{symbol}")
async def get_price(symbol: str):
    """
    Get current price for a cryptocurrency from CryptoCompare
    """
    symbol = symbol.upper()

    # Check cache
    if symbol in price_cache and (datetime.now() - price_cache[symbol]["timestamp"]).total_seconds() < PRICE_CACHE_EXPIRY:
        return price_cache[symbol]["data"]

    try:
        # Fetch from CryptoCompare API
        url = f"{CC_BASE_URL}/data/price"
        params = {
            "fsym": symbol,
            "tsyms": "USD"
        }
        headers = {
            "authorization": f"Apikey {CC_API_KEY}"
        }

        response = requests.get(url, params=params, headers=headers)

        if response.status_code == 200:
            data = response.json()
            if "USD" in data:
                result = {
                    "symbol": symbol,
                    "price": data["USD"],
                    "timestamp": datetime.now().isoformat()
                }

                # Update cache
                price_cache[symbol] = {
                    "data": result,
                    "timestamp": datetime.now()
                }

                return result
            else:
                raise HTTPException(status_code=404, detail=f"Price data for {symbol} not found")
        else:
            raise HTTPException(status_code=response.status_code, detail=f"CryptoCompare API error: {response.text}")

    except Exception as e:
        # Fallback to hardcoded values if API fails
        fallback_prices = {
            "BTC": 93747.66,
            "ETH": 1781.41,
            "SOL": 153.92,
            "ADA": 0.45,
            "DOT": 5.23
        }

        if symbol in fallback_prices:
            # Add some randomness to make it look like real-time data
            price = fallback_prices[symbol] * (1 + random.uniform(-0.005, 0.005))
            result = {
                "symbol": symbol,
                "price": price,
                "timestamp": datetime.now().isoformat(),
                "source": "fallback"
            }

            # Update cache
            price_cache[symbol] = {
                "data": result,
                "timestamp": datetime.now()
            }

            return result
        else:
            raise HTTPException(status_code=500, detail=f"Failed to get price for {symbol}: {str(e)}")

@app.get("/api/cc/historical/{symbol}")
async def get_historical(symbol: str, timeframe: str = "1d", limit: int = 30):
    """
    Get historical price data for a cryptocurrency from CryptoCompare
    """
    symbol = symbol.upper()
    cache_key = f"{symbol}_{timeframe}_{limit}"

    # Check cache
    if cache_key in historical_cache and (datetime.now() - historical_cache[cache_key]["timestamp"]).total_seconds() < HISTORICAL_CACHE_EXPIRY:
        return historical_cache[cache_key]["data"]

    try:
        # Map timeframe to CryptoCompare format
        cc_timeframe = {
            "1h": "hour",
            "1d": "day",
            "1w": "week"
        }.get(timeframe, "day")

        # Fetch from CryptoCompare API
        url = f"{CC_BASE_URL}/data/v2/histo{cc_timeframe}"
        params = {
            "fsym": symbol,
            "tsym": "USD",
            "limit": limit
        }
        headers = {
            "authorization": f"Apikey {CC_API_KEY}"
        }

        response = requests.get(url, params=params, headers=headers)

        if response.status_code == 200:
            data = response.json()
            if data["Response"] == "Success" and "Data" in data:
                result = {
                    "symbol": symbol,
                    "timeframe": timeframe,
                    "data": data["Data"]["Data"],
                    "timestamp": datetime.now().isoformat()
                }

                # Update cache
                historical_cache[cache_key] = {
                    "data": result,
                    "timestamp": datetime.now()
                }

                return result
            else:
                raise HTTPException(status_code=404, detail=f"Historical data for {symbol} not found")
        else:
            raise HTTPException(status_code=response.status_code, detail=f"CryptoCompare API error: {response.text}")

    except Exception as e:
        # Generate fallback historical data
        now = datetime.now()
        fallback_prices = {
            "BTC": 93747.66,
            "ETH": 1781.41,
            "SOL": 153.92,
            "ADA": 0.45,
            "DOT": 5.23
        }

        if symbol in fallback_prices:
            base_price = fallback_prices[symbol]
            data = []

            for i in range(limit):
                # Generate a date in the past
                date = now - timedelta(days=limit-i)

                # Generate a price with some randomness to simulate historical movement
                # More variance for older dates
                variance_factor = (limit - i) / limit * 0.2  # Up to 20% variance for oldest date
                price_variance = random.uniform(-variance_factor, variance_factor)
                price = base_price * (1 + price_variance)

                # Add some randomness to volume
                volume = random.uniform(0.7, 1.3) * base_price * 100

                data.append({
                    "time": int(date.timestamp()),
                    "high": price * 1.02,
                    "low": price * 0.98,
                    "open": price * 0.99,
                    "close": price,
                    "volumefrom": volume,
                    "volumeto": volume * price
                })

            result = {
                "symbol": symbol,
                "timeframe": timeframe,
                "data": data,
                "timestamp": datetime.now().isoformat(),
                "source": "fallback"
            }

            # Update cache
            historical_cache[cache_key] = {
                "data": result,
                "timestamp": datetime.now()
            }

            return result
        else:
            raise HTTPException(status_code=500, detail=f"Failed to get historical data for {symbol}: {str(e)}")

@app.get("/api/cc/indicators/{symbol}")
async def get_indicators(symbol: str):
    """
    Get technical indicators for a cryptocurrency
    """
    symbol = symbol.upper()

    # Check cache
    if symbol in indicators_cache and (datetime.now() - indicators_cache[symbol]["timestamp"]).total_seconds() < INDICATORS_CACHE_EXPIRY:
        return indicators_cache[symbol]["data"]

    try:
        # This would normally fetch from an API, but we'll generate mock data
        indicators = {
            "rsi": random.uniform(30, 70),
            "macd": random.uniform(-10, 10),
            "ema_short": random.uniform(0.8, 1.2),
            "ema_long": random.uniform(0.8, 1.2),
            "bollinger_upper": random.uniform(1.05, 1.2),
            "bollinger_lower": random.uniform(0.8, 0.95),
            "volume_change": random.uniform(-0.1, 0.1)
        }

        result = {
            "symbol": symbol,
            "indicators": indicators,
            "timestamp": datetime.now().isoformat()
        }

        # Update cache
        indicators_cache[symbol] = {
            "data": result,
            "timestamp": datetime.now()
        }

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get indicators for {symbol}: {str(e)}")

@app.get("/api/market/chart/{symbol}")
async def get_market_chart(symbol: str, days: int = 30):
    """
    Get market chart data for a cryptocurrency (CoinGecko format)
    """
    symbol = symbol.upper()
    cache_key = f"{symbol}_chart_{days}"

    # Check cache
    if cache_key in historical_cache and (datetime.now() - historical_cache[cache_key]["timestamp"]).total_seconds() < HISTORICAL_CACHE_EXPIRY:
        return historical_cache[cache_key]["data"]

    try:
        # This would normally fetch from CoinGecko API, but we'll generate mock data
        fallback_prices = {
            "BTC": 93747.66,
            "ETH": 1781.41,
            "SOL": 153.92,
            "ADA": 0.45,
            "DOT": 5.23
        }

        if symbol in fallback_prices:
            base_price = fallback_prices[symbol]
            now = datetime.now()

            # Generate price data
            prices = []
            market_caps = []
            total_volumes = []

            for i in range(days):
                # Generate a date in the past
                date = now - timedelta(days=days-i)
                timestamp = int(date.timestamp() * 1000)

                # Generate a price with some randomness to simulate historical movement
                # More variance for older dates
                variance_factor = (days - i) / days * 0.2  # Up to 20% variance for oldest date
                price_variance = random.uniform(-variance_factor, variance_factor)
                price = base_price * (1 + price_variance)

                # Add some randomness to market cap and volume
                market_cap = price * random.uniform(18000000, 19000000)  # Approximate supply
                volume = price * random.uniform(100000, 500000)

                prices.append([timestamp, price])
                market_caps.append([timestamp, market_cap])
                total_volumes.append([timestamp, volume])

            result = {
                "prices": prices,
                "market_caps": market_caps,
                "total_volumes": total_volumes
            }

            # Update cache
            historical_cache[cache_key] = {
                "data": result,
                "timestamp": datetime.now()
            }

            return result
        else:
            raise HTTPException(status_code=404, detail=f"Chart data for {symbol} not found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get chart data for {symbol}: {str(e)}")

@app.get("/signals", response_model=List[TradingSignal])
async def get_trading_signals(timeframe: str = "1h"):
    """
    Generate real-time trading signals based on technical analysis of market data
    """
    # Supported cryptocurrencies
    cryptocurrencies = [
        {"symbol": "BTC", "name": "Bitcoin"},
        {"symbol": "ETH", "name": "Ethereum"},
        {"symbol": "SOL", "name": "Solana"},
        {"symbol": "ADA", "name": "Cardano"},
        {"symbol": "DOT", "name": "Polkadot"}
    ]

    signals = []

    for crypto in cryptocurrencies:
        symbol = crypto["symbol"]

        # Get current price
        try:
            price_data = await get_price(symbol)
            current_price = price_data["price"]
        except Exception as e:
            print(f"Error getting price for {symbol}: {str(e)}")
            continue

        # Get historical data for technical analysis
        try:
            historical_data = await get_historical(symbol, timeframe=timeframe, limit=30)
            prices = [item["close"] for item in historical_data["data"]]
        except Exception as e:
            print(f"Error getting historical data for {symbol}: {str(e)}")
            continue

        # Calculate technical indicators
        indicators = {}

        # RSI (Relative Strength Index)
        try:
            rsi = calculate_rsi(prices)
            indicators["rsi"] = rsi

            # RSI interpretation
            if rsi > 70:
                rsi_interpretation = "overbought"
            elif rsi < 30:
                rsi_interpretation = "oversold"
            elif rsi > 50:
                rsi_interpretation = "bullish"
            else:
                rsi_interpretation = "bearish"

            indicators["rsi_interpretation"] = rsi_interpretation
        except Exception as e:
            print(f"Error calculating RSI for {symbol}: {str(e)}")

        # Moving Averages
        try:
            if len(prices) >= 20:
                sma_5 = sum(prices[-5:]) / 5
                sma_10 = sum(prices[-10:]) / 10
                sma_20 = sum(prices[-20:]) / 20

                indicators["sma_5"] = sma_5
                indicators["sma_10"] = sma_10
                indicators["sma_20"] = sma_20

                # Moving average interpretation
                if sma_5 > sma_10 > sma_20:
                    ma_trend = "strong_bullish"
                elif sma_5 > sma_10:
                    ma_trend = "bullish"
                elif sma_5 < sma_10 < sma_20:
                    ma_trend = "strong_bearish"
                elif sma_5 < sma_10:
                    ma_trend = "bearish"
                else:
                    ma_trend = "neutral"

                indicators["ma_trend"] = ma_trend
        except Exception as e:
            print(f"Error calculating moving averages for {symbol}: {str(e)}")

        # MACD (Moving Average Convergence Divergence)
        try:
            if len(prices) >= 26:
                ema_12 = calculate_ema(prices, 12)
                ema_26 = calculate_ema(prices, 26)
                macd_line = ema_12 - ema_26

                # Calculate signal line (9-day EMA of MACD line)
                if len(prices) >= 35:  # Need at least 26 + 9 days
                    macd_history = []
                    for i in range(9):
                        idx = len(prices) - 9 + i - 26
                        if idx >= 0:
                            ema_12_hist = calculate_ema(prices[:len(prices)-9+i], 12)
                            ema_26_hist = calculate_ema(prices[:len(prices)-9+i], 26)
                            macd_history.append(ema_12_hist - ema_26_hist)

                    signal_line = calculate_ema(macd_history, 9) if macd_history else macd_line
                    macd_histogram = macd_line - signal_line

                    indicators["macd_line"] = macd_line
                    indicators["macd_signal"] = signal_line
                    indicators["macd_histogram"] = macd_histogram

                    # MACD interpretation
                    if macd_line > signal_line and macd_histogram > 0:
                        macd_trend = "bullish"
                    elif macd_line < signal_line and macd_histogram < 0:
                        macd_trend = "bearish"
                    elif macd_line > signal_line and macd_histogram < 0:
                        macd_trend = "bullish_weakening"
                    elif macd_line < signal_line and macd_histogram > 0:
                        macd_trend = "bearish_weakening"
                    else:
                        macd_trend = "neutral"

                    indicators["macd_trend"] = macd_trend
        except Exception as e:
            print(f"Error calculating MACD for {symbol}: {str(e)}")

        # Determine signal type based on indicators
        signal_type = "hold"  # Default
        confidence = 0.5  # Default

        # Simple signal logic based on indicators
        if "rsi_interpretation" in indicators and "ma_trend" in indicators:
            rsi_interp = indicators["rsi_interpretation"]
            ma_trend = indicators["ma_trend"]

            # Strong buy signals
            if (rsi_interp == "oversold" and ma_trend in ["bullish", "strong_bullish"]) or \
               (rsi_interp == "bullish" and ma_trend == "strong_bullish"):
                signal_type = "buy"
                confidence = 0.85

            # Moderate buy signals
            elif (rsi_interp == "bullish" and ma_trend == "bullish") or \
                 (rsi_interp == "oversold" and ma_trend == "neutral"):
                signal_type = "buy"
                confidence = 0.7

            # Strong sell signals
            elif (rsi_interp == "overbought" and ma_trend in ["bearish", "strong_bearish"]) or \
                 (rsi_interp == "bearish" and ma_trend == "strong_bearish"):
                signal_type = "sell"
                confidence = 0.85

            # Moderate sell signals
            elif (rsi_interp == "bearish" and ma_trend == "bearish") or \
                 (rsi_interp == "overbought" and ma_trend == "neutral"):
                signal_type = "sell"
                confidence = 0.7

            # Hold signals
            else:
                signal_type = "hold"
                confidence = 0.6

        # Add MACD confirmation to increase confidence
        if "macd_trend" in indicators:
            macd_trend = indicators["macd_trend"]

            if signal_type == "buy" and macd_trend in ["bullish"]:
                confidence = min(0.95, confidence + 0.1)
            elif signal_type == "sell" and macd_trend in ["bearish"]:
                confidence = min(0.95, confidence + 0.1)
            elif signal_type == "buy" and macd_trend in ["bearish", "bearish_weakening"]:
                confidence = max(0.55, confidence - 0.15)
            elif signal_type == "sell" and macd_trend in ["bullish", "bullish_weakening"]:
                confidence = max(0.55, confidence - 0.15)

        # Create the trading signal
        signal = TradingSignal(
            id=f"signal-{symbol.lower()}-{datetime.now().strftime('%Y%m%d%H%M')}",
            asset_symbol=symbol,
            asset_name=crypto["name"],
            signal_type=signal_type,
            time_frame=timeframe,
            confidence=confidence,
            price=current_price,
            timestamp=datetime.now().isoformat(),
            indicators=indicators
        )

        signals.append(signal)

    return signals

def calculate_rsi(prices, period=14):
    """Calculate the Relative Strength Index (RSI)"""
    if len(prices) < period + 1:
        # Not enough data
        return 50  # Return neutral RSI

    # Calculate price changes
    deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]

    # Separate gains and losses
    gains = [delta if delta > 0 else 0 for delta in deltas]
    losses = [-delta if delta < 0 else 0 for delta in deltas]

    # Calculate average gain and loss over the period
    avg_gain = sum(gains[-period:]) / period
    avg_loss = sum(losses[-period:]) / period

    if avg_loss == 0:
        return 100  # No losses, RSI is 100

    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return rsi

def calculate_ema(prices, period):
    """Calculate Exponential Moving Average (EMA)"""
    if len(prices) < period:
        return sum(prices) / len(prices)  # Simple average if not enough data

    # Start with SMA for the first EMA value
    ema = sum(prices[:period]) / period

    # Multiplier for weighting the EMA
    multiplier = 2 / (period + 1)

    # Calculate EMA for the remaining prices
    for price in prices[period:]:
        ema = (price - ema) * multiplier + ema

    return ema

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)
