@echo off
echo Starting MCP Trading Platform Charts...

echo Starting backend server...
start cmd /k "cd backend && python server.py"

echo Starting frontend server...
start cmd /k "cd frontend && python -m http.server 8080"

echo.
echo MCP Trading Platform is starting...
echo.
echo Waiting for servers to initialize...
timeout /t 3 /nobreak > nul

echo.
echo Opening Charts page...
start http://localhost:8080/public/charts.html

echo.
echo MCP Trading Platform Charts is now running!
echo.
echo Press any key to exit this launcher (servers will continue running)...
pause > nul
