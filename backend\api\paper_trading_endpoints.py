"""
API endpoints for paper trading functionality.
"""

import logging
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from paper_trading import paper_trading
from data_aggregator.cryptocompare_client import CryptoCompareClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("paper_trading_api")

# Create router
router = APIRouter(
    prefix="/api/paper-trading",
    tags=["paper-trading"],
    responses={404: {"description": "Not found"}},
)

# Initialize CryptoCompare client for price data
cc_client = CryptoCompareClient()

# Models
class TradeCreate(BaseModel):
    symbol: str
    trade_type: str
    entry_price: float
    amount: float
    usd_amount: float
    leverage: float = 1.0
    timeframe: str = "1d"
    confidence: float = 0.0
    notes: str = ""

class TradeClose(BaseModel):
    exit_price: float

class TradeResponse(BaseModel):
    id: str
    symbol: str
    trade_type: str
    entry_price: float
    amount: float
    usd_amount: Optional[float] = None
    leverage: float = 1.0
    entry_timestamp: str
    exit_price: Optional[float] = None
    exit_timestamp: Optional[str] = None
    profit_loss: Optional[float] = None
    profit_loss_percentage: Optional[float] = None
    status: str
    timeframe: str
    confidence: float
    notes: str
    current_price: Optional[float] = None
    last_updated: Optional[str] = None

class PortfolioSummary(BaseModel):
    total_invested: float
    total_current_value: float
    total_profit_loss: float
    total_closed_profit_loss: float
    portfolio_performance: float
    open_positions: Dict
    open_trades_count: int
    closed_trades_count: int
    total_trades_count: int

@router.post("/trades", response_model=TradeResponse)
async def create_trade(trade: TradeCreate):
    """Create a new paper trade."""
    try:
        # Create the trade
        new_trade = paper_trading.create_trade(
            symbol=trade.symbol,
            trade_type=trade.trade_type,
            entry_price=trade.entry_price,
            amount=trade.amount,
            usd_amount=trade.usd_amount,
            leverage=trade.leverage,
            timeframe=trade.timeframe,
            confidence=trade.confidence,
            notes=trade.notes
        )

        # Update with current price
        try:
            price_data = cc_client.get_price(trade.symbol)
            if price_data and trade.symbol in price_data and "USD" in price_data[trade.symbol]:
                current_price = price_data[trade.symbol]["USD"]["PRICE"]
                paper_trading.update_trade_pl(new_trade["id"], current_price)
                new_trade = paper_trading.get_trade(new_trade["id"])
        except Exception as e:
            logger.error(f"Error updating trade with current price: {e}")

        return new_trade
    except Exception as e:
        logger.error(f"Error creating trade: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating trade: {str(e)}")

@router.get("/trades", response_model=List[TradeResponse])
async def get_trades(status: Optional[str] = None):
    """Get all trades, optionally filtered by status."""
    try:
        if status == "open":
            trades = paper_trading.get_open_trades()
        elif status == "closed":
            trades = paper_trading.get_closed_trades()
        else:
            trades = paper_trading.get_all_trades()

        # Update open trades with current prices
        if status != "closed":
            try:
                # Get current prices for all symbols in open trades
                symbols = list(set([trade["symbol"] for trade in trades if trade["status"] == "open"]))
                price_data = {}

                for symbol in symbols:
                    price_info = cc_client.get_price(symbol)
                    if price_info and symbol in price_info and "USD" in price_info[symbol]:
                        price_data[symbol] = price_info[symbol]["USD"]["PRICE"]

                # Update all open trades
                paper_trading.update_all_open_trades(price_data)

                # Refresh trades list after updates
                if status == "open":
                    trades = paper_trading.get_open_trades()
                else:
                    trades = paper_trading.get_all_trades()
            except Exception as e:
                logger.error(f"Error updating trades with current prices: {e}")

        return trades
    except Exception as e:
        logger.error(f"Error getting trades: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting trades: {str(e)}")

@router.get("/trades/{trade_id}", response_model=TradeResponse)
async def get_trade(trade_id: str):
    """Get a specific trade by ID."""
    try:
        trade = paper_trading.get_trade(trade_id)
        if not trade:
            raise HTTPException(status_code=404, detail=f"Trade with ID {trade_id} not found")

        # Update with current price if trade is open
        if trade["status"] == "open":
            try:
                price_data = cc_client.get_price(trade["symbol"])
                if price_data and trade["symbol"] in price_data and "USD" in price_data[trade["symbol"]]:
                    current_price = price_data[trade["symbol"]]["USD"]["PRICE"]
                    paper_trading.update_trade_pl(trade_id, current_price)
                    trade = paper_trading.get_trade(trade_id)
            except Exception as e:
                logger.error(f"Error updating trade with current price: {e}")

        return trade
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting trade: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting trade: {str(e)}")

@router.post("/trades/{trade_id}/close", response_model=TradeResponse)
async def close_trade(trade_id: str, close_data: TradeClose):
    """Close an existing paper trade."""
    try:
        trade = paper_trading.close_trade(trade_id, close_data.exit_price)
        if not trade:
            raise HTTPException(status_code=404, detail=f"Open trade with ID {trade_id} not found")

        return trade
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error closing trade: {e}")
        raise HTTPException(status_code=500, detail=f"Error closing trade: {str(e)}")

@router.delete("/trades/{trade_id}")
async def delete_trade(trade_id: str):
    """Delete a trade."""
    try:
        success = paper_trading.delete_trade(trade_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"Trade with ID {trade_id} not found")

        return {"status": "success", "message": f"Trade {trade_id} deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting trade: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting trade: {str(e)}")

@router.get("/portfolio", response_model=PortfolioSummary)
async def get_portfolio_summary():
    """Get a summary of the paper trading portfolio."""
    try:
        # Update all open trades with current prices
        open_trades = paper_trading.get_open_trades()
        symbols = list(set([trade["symbol"] for trade in open_trades]))
        price_data = {}

        for symbol in symbols:
            try:
                price_info = cc_client.get_price(symbol)
                if price_info and symbol in price_info and "USD" in price_info[symbol]:
                    price_data[symbol] = price_info[symbol]["USD"]["PRICE"]
            except Exception as e:
                logger.error(f"Error getting price for {symbol}: {e}")

        paper_trading.update_all_open_trades(price_data)

        # Get portfolio summary
        summary = paper_trading.get_portfolio_summary()
        return summary
    except Exception as e:
        logger.error(f"Error getting portfolio summary: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting portfolio summary: {str(e)}")

@router.post("/update-prices")
async def update_prices():
    """Update all open trades with current prices."""
    try:
        # Get all open trades
        open_trades = paper_trading.get_open_trades()
        symbols = list(set([trade["symbol"] for trade in open_trades]))
        price_data = {}

        for symbol in symbols:
            try:
                price_info = cc_client.get_price(symbol)
                if price_info and symbol in price_info and "USD" in price_info[symbol]:
                    price_data[symbol] = price_info[symbol]["USD"]["PRICE"]
            except Exception as e:
                logger.error(f"Error getting price for {symbol}: {e}")

        updated_trades = paper_trading.update_all_open_trades(price_data)

        return {
            "status": "success",
            "message": f"Updated {len(updated_trades)} trades with current prices",
            "updated_trades": len(updated_trades)
        }
    except Exception as e:
        logger.error(f"Error updating prices: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating prices: {str(e)}")
