"""
CryptoCompare API Endpoints

This module provides API endpoints for accessing CryptoCompare data.
"""

import os
import json
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from datetime import datetime

# Import CryptoCompare client
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from data_aggregator.cryptocompare_client import CryptoCompareClient, SUPPORTED_COINS, TIMEFRAMES

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('cc_api')

# Create router
router = APIRouter(
    prefix="/api/cc",
    tags=["cryptocompare"],
    responses={404: {"description": "Not found"}},
)

# Initialize CryptoCompare client
cc_client = CryptoCompareClient()

# Data paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')

# Models
class PriceData(BaseModel):
    symbol: str
    price: float
    market_cap: float
    volume_24h: float
    change_24h: float
    high_24h: float
    low_24h: float
    last_updated: str

class HistoricalDataPoint(BaseModel):
    time: int
    open: float
    high: float
    low: float
    close: float
    volume_from: float
    volume_to: float

class Exchange(BaseModel):
    exchange: str
    price: float
    volume_24h: float
    volume_percentage: float
    last_updated: str

class NewsItem(BaseModel):
    id: str
    title: str
    url: str
    image_url: str
    body: str
    tags: str
    categories: str
    published_on: int
    source: str

class TechnicalIndicator(BaseModel):
    time: int
    value: float

class RSIData(BaseModel):
    data: List[TechnicalIndicator]
    interpretation: str

class MACDData(BaseModel):
    data: List[Dict[str, Any]]
    interpretation: str

class BollingerBandsData(BaseModel):
    data: List[Dict[str, Any]]
    interpretation: str

class TechnicalIndicators(BaseModel):
    rsi: Optional[RSIData]
    macd: Optional[MACDData]
    bollinger: Optional[BollingerBandsData]

@router.get("/coins")
async def get_supported_coins():
    """Get a list of supported coins."""
    return {"coins": SUPPORTED_COINS}

@router.get("/timeframes")
async def get_supported_timeframes():
    """Get a list of supported timeframes."""
    return {"timeframes": list(TIMEFRAMES.keys())}

@router.get("/price/{coin}")
async def get_coin_price(coin: str):
    """
    Get current price data for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    try:
        # Check if coin is supported
        if coin.upper() not in SUPPORTED_COINS:
            raise HTTPException(status_code=404, detail=f"Coin not supported: {coin}")
        
        # Get price data from CryptoCompare
        price_data = cc_client.get_price(coin.upper())
        
        if not price_data or coin.upper() not in price_data or "USD" not in price_data[coin.upper()]:
            # Try to get from saved data
            saved_data = cc_client.get_latest_market_data(coin.upper())
            if "price" in saved_data and "USD" in saved_data["price"]:
                price_data = {coin.upper(): saved_data["price"]}
            else:
                raise HTTPException(status_code=404, detail=f"Price data not found for {coin}")
        
        # Extract price data
        coin_data = price_data[coin.upper()]["USD"]
        
        # Format response
        return {
            "symbol": coin.upper(),
            "price": coin_data["PRICE"],
            "market_cap": coin_data.get("MKTCAP", 0),
            "volume_24h": coin_data.get("VOLUME24HOUR", 0),
            "change_24h": coin_data.get("CHANGEPCT24HOUR", 0),
            "high_24h": coin_data.get("HIGH24HOUR", 0),
            "low_24h": coin_data.get("LOW24HOUR", 0),
            "last_updated": datetime.fromtimestamp(coin_data.get("LASTUPDATE", 0)).isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting price for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/historical/{coin}")
async def get_coin_historical_data(
    coin: str,
    timeframe: str = Query("1d", description="Timeframe (1m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w)"),
    limit: int = Query(30, description="Number of data points to return")
):
    """
    Get historical OHLCV data for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
        timeframe: Timeframe (1m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w)
        limit: Number of data points to return
    """
    try:
        # Check if coin is supported
        if coin.upper() not in SUPPORTED_COINS:
            raise HTTPException(status_code=404, detail=f"Coin not supported: {coin}")
        
        # Check if timeframe is supported
        if timeframe not in TIMEFRAMES:
            raise HTTPException(status_code=400, detail=f"Timeframe not supported: {timeframe}")
        
        # Get historical data from CryptoCompare
        historical_data = cc_client.get_historical_data(coin.upper(), timeframe, limit)
        
        if not historical_data:
            # Try to get from saved data
            saved_data = cc_client.get_latest_market_data(coin.upper())
            if "history" in saved_data:
                historical_data = saved_data["history"]
            else:
                raise HTTPException(status_code=404, detail=f"Historical data not found for {coin}")
        
        # Format response
        return {
            "symbol": coin.upper(),
            "timeframe": timeframe,
            "data": historical_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting historical data for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/exchanges/{coin}")
async def get_coin_exchanges(
    coin: str,
    limit: int = Query(10, description="Number of exchanges to return")
):
    """
    Get top exchanges for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
        limit: Number of exchanges to return
    """
    try:
        # Check if coin is supported
        if coin.upper() not in SUPPORTED_COINS:
            raise HTTPException(status_code=404, detail=f"Coin not supported: {coin}")
        
        # Get exchanges from CryptoCompare
        exchanges = cc_client.get_top_exchanges(coin.upper(), limit)
        
        if not exchanges:
            # Try to get from saved data
            saved_data = cc_client.get_latest_market_data(coin.upper())
            if "exchanges" in saved_data:
                exchanges = saved_data["exchanges"]
            else:
                raise HTTPException(status_code=404, detail=f"Exchange data not found for {coin}")
        
        # Format response
        return {
            "symbol": coin.upper(),
            "exchanges": exchanges
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting exchanges for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/news")
async def get_crypto_news(
    limit: int = Query(10, description="Number of news items to return"),
    categories: Optional[str] = Query(None, description="Comma-separated list of news categories")
):
    """
    Get cryptocurrency news.
    
    Args:
        limit: Number of news items to return
        categories: Comma-separated list of news categories
    """
    try:
        # Parse categories
        category_list = None
        if categories:
            category_list = categories.split(",")
        
        # Get news from CryptoCompare
        news = cc_client.get_news(category_list, limit)
        
        if not news:
            # Try to get from saved data
            news = cc_client.get_latest_news()
            if not news:
                raise HTTPException(status_code=404, detail="News not found")
        
        # Format response
        return {
            "news": news[:limit]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting news: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/indicators/{coin}")
async def get_coin_technical_indicators(
    coin: str,
    timeframe: str = Query("1d", description="Timeframe (1m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w)")
):
    """
    Get technical indicators for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
        timeframe: Timeframe (1m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w)
    """
    try:
        # Check if coin is supported
        if coin.upper() not in SUPPORTED_COINS:
            raise HTTPException(status_code=404, detail=f"Coin not supported: {coin}")
        
        # Check if timeframe is supported
        if timeframe not in TIMEFRAMES:
            raise HTTPException(status_code=400, detail=f"Timeframe not supported: {timeframe}")
        
        # Get technical indicators from CryptoCompare
        indicators = cc_client.get_technical_indicators(coin.upper(), timeframe)
        
        if not indicators:
            # Try to get from saved data
            saved_data = cc_client.get_latest_market_data(coin.upper())
            if "indicators" in saved_data:
                indicators = saved_data["indicators"]
            else:
                raise HTTPException(status_code=404, detail=f"Technical indicators not found for {coin}")
        
        # Add interpretations
        result = {}
        
        # RSI interpretation
        if "rsi" in indicators and indicators["rsi"]:
            latest_rsi = indicators["rsi"][-1]["value"]
            if latest_rsi > 70:
                interpretation = "Overbought - potential sell signal"
            elif latest_rsi < 30:
                interpretation = "Oversold - potential buy signal"
            else:
                interpretation = "Neutral"
            
            result["rsi"] = {
                "data": indicators["rsi"],
                "interpretation": interpretation
            }
        
        # MACD interpretation
        if "macd" in indicators and indicators["macd"]:
            latest_macd = indicators["macd"][-1]
            if latest_macd["macd"] > latest_macd["signal"]:
                if latest_macd["histogram"] > 0 and latest_macd["histogram"] > indicators["macd"][-2]["histogram"]:
                    interpretation = "Strong bullish signal - MACD above signal line and increasing"
                else:
                    interpretation = "Bullish signal - MACD above signal line"
            else:
                if latest_macd["histogram"] < 0 and latest_macd["histogram"] < indicators["macd"][-2]["histogram"]:
                    interpretation = "Strong bearish signal - MACD below signal line and decreasing"
                else:
                    interpretation = "Bearish signal - MACD below signal line"
            
            result["macd"] = {
                "data": indicators["macd"],
                "interpretation": interpretation
            }
        
        # Bollinger Bands interpretation
        if "bollinger" in indicators and indicators["bollinger"]:
            latest_bb = indicators["bollinger"][-1]
            latest_price = cc_client.get_price(coin.upper())
            
            if latest_price and coin.upper() in latest_price and "USD" in latest_price[coin.upper()]:
                current_price = latest_price[coin.upper()]["USD"]["PRICE"]
                
                if current_price > latest_bb["upper"]:
                    interpretation = "Price above upper band - potential overbought condition"
                elif current_price < latest_bb["lower"]:
                    interpretation = "Price below lower band - potential oversold condition"
                else:
                    # Calculate percentage position within bands
                    band_width = latest_bb["upper"] - latest_bb["lower"]
                    position = (current_price - latest_bb["lower"]) / band_width
                    
                    if position > 0.8:
                        interpretation = "Price near upper band - potential resistance"
                    elif position < 0.2:
                        interpretation = "Price near lower band - potential support"
                    else:
                        interpretation = "Price within bands - neutral"
            else:
                interpretation = "Unable to determine current position within bands"
            
            result["bollinger"] = {
                "data": indicators["bollinger"],
                "interpretation": interpretation
            }
        
        # Format response
        return {
            "symbol": coin.upper(),
            "timeframe": timeframe,
            "indicators": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting technical indicators for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/refresh")
async def refresh_cc_data():
    """Refresh CryptoCompare data for all coins."""
    try:
        # Save market data for all supported coins
        results = cc_client.save_market_data()
        
        # Count successful saves
        success_count = sum(1 for files in results.values() if files)
        
        return {
            "status": "success" if success_count > 0 else "partial_success",
            "message": f"Refreshed CryptoCompare data for {success_count} coins",
            "details": {coin: bool(files) for coin, files in results.items()}
        }
    except Exception as e:
        logger.error(f"Error refreshing CryptoCompare data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
