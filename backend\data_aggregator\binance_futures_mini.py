"""
Binance Futures Data Fetcher (Mini Version)

This module fetches futures market data from the Binance API.
"""

import logging
import hmac
import hashlib
import time
import uuid
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Binance API credentials
BINANCE_API_KEY = "GhP7XdXbyxfMyCGXellMwNlEqYDCVYyPulh2FXpR9t10dEGaNlXoUgy7IcDG4wEr"
BINANCE_API_SECRET = "KKPV67R2Ml0ccEfLWSCsYnjZAwhG8kmJZjSKABrdz9J3YDeVToMPvVQalXWV33Gq"

class BinanceFuturesDataFetcher:
    """Fetches futures market data from the Binance API."""

    def __init__(self):
        """Initialize the Binance futures data fetcher."""
        self.api_key = BINANCE_API_KEY
        self.api_secret = BINANCE_API_SECRET
        self.base_url = "https://fapi.binance.com"
        logger.info("Binance futures data fetcher initialized")

    async def _make_request(self, endpoint: str, params: Dict = None, signed: bool = False) -> Dict:
        """
        Make a request to the Binance API.
        
        Args:
            endpoint: API endpoint
            params: Request parameters
            signed: Whether the request needs to be signed
            
        Returns:
            API response
        """
        url = f"{self.base_url}{endpoint}"
        headers = {"X-MBX-APIKEY": self.api_key}
        params = params or {}
        
        if signed:
            # Add timestamp and signature
            params["timestamp"] = int(time.time() * 1000)
            query_string = "&".join([f"{key}={params[key]}" for key in params])
            signature = hmac.new(
                self.api_secret.encode("utf-8"),
                query_string.encode("utf-8"),
                hashlib.sha256
            ).hexdigest()
            params["signature"] = signature
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(url, params=params, headers=headers)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Error making request to {endpoint}: {str(e)}")
            return {}

    async def fetch_funding_rate(self, symbol: str = None) -> List[Dict[str, Any]]:
        """
        Fetch funding rate for a specific symbol or all symbols.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
            
        Returns:
            List of funding rate data
        """
        endpoint = "/fapi/v1/fundingRate"
        params = {}
        if symbol:
            params["symbol"] = symbol
        
        try:
            data = await self._make_request(endpoint, params)
            
            if not data:
                return []
            
            funding_rates = []
            for item in data:
                funding_rates.append({
                    "symbol": item["symbol"],
                    "funding_rate": float(item["fundingRate"]),
                    "funding_time": datetime.fromtimestamp(item["fundingTime"] / 1000, tz=timezone.utc).isoformat(),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "binance"
                })
            
            logger.info(f"Fetched {len(funding_rates)} funding rates")
            return funding_rates
        
        except Exception as e:
            logger.error(f"Error fetching funding rate: {str(e)}")
            return []

    async def fetch_open_interest(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Fetch open interest for a specific symbol.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
            
        Returns:
            Open interest data
        """
        endpoint = "/fapi/v1/openInterest"
        params = {"symbol": symbol}
        
        try:
            data = await self._make_request(endpoint, params)
            
            if not data or "openInterest" not in data:
                return None
            
            return {
                "symbol": symbol,
                "open_interest": float(data["openInterest"]),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "binance"
            }
        
        except Exception as e:
            logger.error(f"Error fetching open interest for {symbol}: {str(e)}")
            return None

    async def fetch_mark_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Fetch mark price for a specific symbol.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
            
        Returns:
            Mark price data
        """
        endpoint = "/fapi/v1/premiumIndex"
        params = {"symbol": symbol}
        
        try:
            data = await self._make_request(endpoint, params)
            
            if not data or "markPrice" not in data:
                return None
            
            return {
                "symbol": symbol,
                "mark_price": float(data["markPrice"]),
                "index_price": float(data["indexPrice"]),
                "estimated_settlement_price": float(data.get("estimatedSettlePrice", 0)),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "binance"
            }
        
        except Exception as e:
            logger.error(f"Error fetching mark price for {symbol}: {str(e)}")
            return None

    async def fetch_futures_data(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch comprehensive futures data for a specific symbol.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSDT")
            
        Returns:
            Dictionary with futures market data
        """
        try:
            # Fetch funding rate
            funding_rates = await self.fetch_funding_rate(symbol)
            funding_rate = funding_rates[0] if funding_rates else None
            
            # Fetch open interest
            open_interest = await self.fetch_open_interest(symbol)
            
            # Fetch mark price
            mark_price = await self.fetch_mark_price(symbol)
            
            # Combine all data
            futures_data = {
                "symbol": symbol,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "binance"
            }
            
            if funding_rate:
                futures_data["funding_rate"] = funding_rate["funding_rate"]
                futures_data["funding_time"] = funding_rate["funding_time"]
            
            if open_interest:
                futures_data["open_interest"] = open_interest["open_interest"]
            
            if mark_price:
                futures_data["mark_price"] = mark_price["mark_price"]
                futures_data["index_price"] = mark_price["index_price"]
            
            logger.info(f"Fetched comprehensive futures data for {symbol}")
            return futures_data
        
        except Exception as e:
            logger.error(f"Error fetching futures data for {symbol}: {str(e)}")
            return {"symbol": symbol, "error": str(e)}

    async def fetch_all_futures_data(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Fetch futures data for multiple symbols.
        
        Args:
            symbols: List of trading pair symbols
            
        Returns:
            List of futures market data
        """
        tasks = [self.fetch_futures_data(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks)
        
        # Filter out any results with errors
        valid_results = [result for result in results if "error" not in result]
        
        logger.info(f"Fetched futures data for {len(valid_results)} symbols")
        return valid_results

    def analyze_futures_sentiment(self, futures_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Analyze futures market sentiment based on funding rates and open interest.
        
        Args:
            futures_data: List of futures market data
            
        Returns:
            Dictionary with sentiment analysis for each symbol
        """
        sentiment = {}
        
        for data in futures_data:
            symbol = data["symbol"]
            symbol_base = symbol.replace("USDT", "")
            
            # Initialize sentiment
            sentiment[symbol_base] = {
                "overall_sentiment": "neutral",
                "funding_rate_sentiment": "neutral",
                "open_interest_sentiment": "neutral",
                "confidence": 0.5
            }
            
            # Analyze funding rate
            if "funding_rate" in data:
                funding_rate = data["funding_rate"]
                
                if funding_rate > 0.001:
                    sentiment[symbol_base]["funding_rate_sentiment"] = "bearish"
                elif funding_rate > 0.0001:
                    sentiment[symbol_base]["funding_rate_sentiment"] = "slightly_bearish"
                elif funding_rate < -0.001:
                    sentiment[symbol_base]["funding_rate_sentiment"] = "bullish"
                elif funding_rate < -0.0001:
                    sentiment[symbol_base]["funding_rate_sentiment"] = "slightly_bullish"
            
            # Determine overall sentiment
            if sentiment[symbol_base]["funding_rate_sentiment"] == "bullish":
                sentiment[symbol_base]["overall_sentiment"] = "bullish"
                sentiment[symbol_base]["confidence"] = 0.7
            elif sentiment[symbol_base]["funding_rate_sentiment"] == "bearish":
                sentiment[symbol_base]["overall_sentiment"] = "bearish"
                sentiment[symbol_base]["confidence"] = 0.7
            elif sentiment[symbol_base]["funding_rate_sentiment"] == "slightly_bullish":
                sentiment[symbol_base]["overall_sentiment"] = "slightly_bullish"
                sentiment[symbol_base]["confidence"] = 0.6
            elif sentiment[symbol_base]["funding_rate_sentiment"] == "slightly_bearish":
                sentiment[symbol_base]["overall_sentiment"] = "slightly_bearish"
                sentiment[symbol_base]["confidence"] = 0.6
        
        return sentiment

    def to_context_items(self, futures_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Convert futures data to context items for MCP.
        
        Args:
            futures_data: List of futures market data
            
        Returns:
            List of context items
        """
        context_items = []
        
        for data in futures_data:
            context_item = {
                "id": f"futures_{data['symbol']}_{uuid.uuid4()}",
                "type": "futures_data",
                "content": data,
                "timestamp": data.get("timestamp", datetime.now(timezone.utc).isoformat()),
                "source": data.get("source", "binance"),
                "confidence": 1.0
            }
            
            context_items.append(context_item)
        
        return context_items

async def main():
    """Main function for testing the Binance futures data fetcher."""
    fetcher = BinanceFuturesDataFetcher()
    
    # Fetch futures data for BTC and ETH
    symbols = ["BTCUSDT", "ETHUSDT"]
    futures_data = await fetcher.fetch_all_futures_data(symbols)
    
    # Analyze sentiment
    sentiment = fetcher.analyze_futures_sentiment(futures_data)
    
    # Convert to context items
    context_items = fetcher.to_context_items(futures_data)
    
    # Print results
    print(f"Fetched futures data for {len(futures_data)} symbols")
    
    for data in futures_data:
        symbol = data["symbol"]
        print(f"\n{symbol} Futures Data:")
        for key, value in data.items():
            if key != "symbol":
                print(f"  {key}: {value}")
    
    print("\nSentiment Analysis:")
    for symbol, analysis in sentiment.items():
        print(f"\n{symbol}:")
        for key, value in analysis.items():
            print(f"  {key}: {value}")
    
    print(f"\nGenerated {len(context_items)} context items")

if __name__ == "__main__":
    asyncio.run(main())
