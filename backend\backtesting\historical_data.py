"""
Historical Data Storage

This module provides functionality for storing and retrieving historical data
for backtesting and analysis.
"""

import logging
import json
import os
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
DEFAULT_DATA_DIR = "data/historical"
PRICE_DATA_DIR = "prices"
SIGNAL_DATA_DIR = "signals"
SENTIMENT_DATA_DIR = "sentiment"
GOVERNANCE_DATA_DIR = "governance"

class HistoricalDataStorage:
    """Storage for historical data used in backtesting and analysis."""
    
    def __init__(self, base_dir: str = DEFAULT_DATA_DIR):
        """
        Initialize the historical data storage.
        
        Args:
            base_dir: Base directory for storing historical data
        """
        self.base_dir = base_dir
        
        # Create directories if they don't exist
        os.makedirs(os.path.join(base_dir, PRICE_DATA_DIR), exist_ok=True)
        os.makedirs(os.path.join(base_dir, SIGNAL_DATA_DIR), exist_ok=True)
        os.makedirs(os.path.join(base_dir, SENTIMENT_DATA_DIR), exist_ok=True)
        os.makedirs(os.path.join(base_dir, GOVERNANCE_DATA_DIR), exist_ok=True)
        
        logger.info(f"Initialized historical data storage in {base_dir}")
    
    def store_price_data(self, symbol: str, timeframe: str, data: List[Dict[str, Any]]):
        """
        Store historical price data.
        
        Args:
            symbol: Asset symbol (e.g., "BTC")
            timeframe: Timeframe (e.g., "1d", "1h")
            data: List of price data points
        """
        # Create directory for this symbol if it doesn't exist
        symbol_dir = os.path.join(self.base_dir, PRICE_DATA_DIR, symbol)
        os.makedirs(symbol_dir, exist_ok=True)
        
        # Create filename based on timeframe
        filename = f"{timeframe}.json"
        filepath = os.path.join(symbol_dir, filename)
        
        # Write data to file
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Stored {len(data)} price data points for {symbol} ({timeframe})")
    
    def load_price_data(self, symbol: str, timeframe: str) -> List[Dict[str, Any]]:
        """
        Load historical price data.
        
        Args:
            symbol: Asset symbol (e.g., "BTC")
            timeframe: Timeframe (e.g., "1d", "1h")
            
        Returns:
            List of price data points
        """
        filepath = os.path.join(self.base_dir, PRICE_DATA_DIR, symbol, f"{timeframe}.json")
        
        try:
            with open(filepath, "r") as f:
                data = json.load(f)
            
            logger.info(f"Loaded {len(data)} price data points for {symbol} ({timeframe})")
            return data
        except FileNotFoundError:
            logger.warning(f"No historical price data found for {symbol} ({timeframe})")
            return []
        except json.JSONDecodeError:
            logger.error(f"Error decoding price data for {symbol} ({timeframe})")
            return []
    
    def store_signal_data(self, signal_type: str, data: List[Dict[str, Any]]):
        """
        Store historical signal data.
        
        Args:
            signal_type: Type of signal (e.g., "trading", "governance")
            data: List of signal data points
        """
        # Create filename based on date
        date_str = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        filename = f"{signal_type}_{date_str}.json"
        filepath = os.path.join(self.base_dir, SIGNAL_DATA_DIR, filename)
        
        # Write data to file
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Stored {len(data)} {signal_type} signal data points")
    
    def load_signal_data(self, signal_type: str, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Load historical signal data.
        
        Args:
            signal_type: Type of signal (e.g., "trading", "governance")
            start_date: Start date for filtering data
            end_date: End date for filtering data
            
        Returns:
            List of signal data points
        """
        signal_dir = os.path.join(self.base_dir, SIGNAL_DATA_DIR)
        
        # Get all signal files of the specified type
        all_files = [f for f in os.listdir(signal_dir) if f.startswith(f"{signal_type}_") and f.endswith(".json")]
        
        # Filter files by date if specified
        if start_date or end_date:
            filtered_files = []
            for filename in all_files:
                try:
                    date_str = filename.split("_")[1].split(".")[0]
                    file_date = datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                    
                    if start_date and file_date < start_date:
                        continue
                    if end_date and file_date > end_date:
                        continue
                    
                    filtered_files.append(filename)
                except (IndexError, ValueError):
                    continue
            
            all_files = filtered_files
        
        # Load and combine data from all files
        all_data = []
        for filename in all_files:
            filepath = os.path.join(signal_dir, filename)
            try:
                with open(filepath, "r") as f:
                    data = json.load(f)
                all_data.extend(data)
            except (json.JSONDecodeError, FileNotFoundError):
                logger.error(f"Error loading signal data from {filename}")
        
        logger.info(f"Loaded {len(all_data)} {signal_type} signal data points")
        return all_data
    
    def store_sentiment_data(self, source: str, data: List[Dict[str, Any]]):
        """
        Store historical sentiment data.
        
        Args:
            source: Sentiment data source (e.g., "news", "social")
            data: List of sentiment data points
        """
        # Create directory for this source if it doesn't exist
        source_dir = os.path.join(self.base_dir, SENTIMENT_DATA_DIR, source)
        os.makedirs(source_dir, exist_ok=True)
        
        # Create filename based on date
        date_str = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        filename = f"{date_str}.json"
        filepath = os.path.join(source_dir, filename)
        
        # Write data to file
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Stored {len(data)} sentiment data points from {source}")
    
    def load_sentiment_data(self, source: str, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Load historical sentiment data.
        
        Args:
            source: Sentiment data source (e.g., "news", "social")
            start_date: Start date for filtering data
            end_date: End date for filtering data
            
        Returns:
            List of sentiment data points
        """
        source_dir = os.path.join(self.base_dir, SENTIMENT_DATA_DIR, source)
        
        try:
            # Get all sentiment files
            all_files = [f for f in os.listdir(source_dir) if f.endswith(".json")]
            
            # Filter files by date if specified
            if start_date or end_date:
                filtered_files = []
                for filename in all_files:
                    try:
                        date_str = filename.split(".")[0]
                        file_date = datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                        
                        if start_date and file_date < start_date:
                            continue
                        if end_date and file_date > end_date:
                            continue
                        
                        filtered_files.append(filename)
                    except (IndexError, ValueError):
                        continue
                
                all_files = filtered_files
            
            # Load and combine data from all files
            all_data = []
            for filename in all_files:
                filepath = os.path.join(source_dir, filename)
                try:
                    with open(filepath, "r") as f:
                        data = json.load(f)
                    all_data.extend(data)
                except (json.JSONDecodeError, FileNotFoundError):
                    logger.error(f"Error loading sentiment data from {filename}")
            
            logger.info(f"Loaded {len(all_data)} sentiment data points from {source}")
            return all_data
        except FileNotFoundError:
            logger.warning(f"No historical sentiment data found for {source}")
            return []
    
    def store_governance_data(self, protocol: str, data: List[Dict[str, Any]]):
        """
        Store historical governance data.
        
        Args:
            protocol: Protocol name (e.g., "uniswap", "aave")
            data: List of governance data points
        """
        # Create directory for this protocol if it doesn't exist
        protocol_dir = os.path.join(self.base_dir, GOVERNANCE_DATA_DIR, protocol)
        os.makedirs(protocol_dir, exist_ok=True)
        
        # Create filename based on date
        date_str = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        filename = f"{date_str}.json"
        filepath = os.path.join(protocol_dir, filename)
        
        # Write data to file
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Stored {len(data)} governance data points for {protocol}")
    
    def load_governance_data(self, protocol: str, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Load historical governance data.
        
        Args:
            protocol: Protocol name (e.g., "uniswap", "aave")
            start_date: Start date for filtering data
            end_date: End date for filtering data
            
        Returns:
            List of governance data points
        """
        protocol_dir = os.path.join(self.base_dir, GOVERNANCE_DATA_DIR, protocol)
        
        try:
            # Get all governance files
            all_files = [f for f in os.listdir(protocol_dir) if f.endswith(".json")]
            
            # Filter files by date if specified
            if start_date or end_date:
                filtered_files = []
                for filename in all_files:
                    try:
                        date_str = filename.split(".")[0]
                        file_date = datetime.strptime(date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                        
                        if start_date and file_date < start_date:
                            continue
                        if end_date and file_date > end_date:
                            continue
                        
                        filtered_files.append(filename)
                    except (IndexError, ValueError):
                        continue
                
                all_files = filtered_files
            
            # Load and combine data from all files
            all_data = []
            for filename in all_files:
                filepath = os.path.join(protocol_dir, filename)
                try:
                    with open(filepath, "r") as f:
                        data = json.load(f)
                    all_data.extend(data)
                except (json.JSONDecodeError, FileNotFoundError):
                    logger.error(f"Error loading governance data from {filename}")
            
            logger.info(f"Loaded {len(all_data)} governance data points for {protocol}")
            return all_data
        except FileNotFoundError:
            logger.warning(f"No historical governance data found for {protocol}")
            return []
    
    def get_available_symbols(self) -> List[str]:
        """
        Get list of available symbols with historical price data.
        
        Returns:
            List of symbol names
        """
        price_dir = os.path.join(self.base_dir, PRICE_DATA_DIR)
        try:
            symbols = [d for d in os.listdir(price_dir) if os.path.isdir(os.path.join(price_dir, d))]
            return symbols
        except FileNotFoundError:
            return []
    
    def get_available_timeframes(self, symbol: str) -> List[str]:
        """
        Get list of available timeframes for a symbol.
        
        Args:
            symbol: Asset symbol
            
        Returns:
            List of timeframe names
        """
        symbol_dir = os.path.join(self.base_dir, PRICE_DATA_DIR, symbol)
        try:
            timeframes = [f.split(".")[0] for f in os.listdir(symbol_dir) if f.endswith(".json")]
            return timeframes
        except FileNotFoundError:
            return []
    
    def get_available_protocols(self) -> List[str]:
        """
        Get list of available protocols with historical governance data.
        
        Returns:
            List of protocol names
        """
        governance_dir = os.path.join(self.base_dir, GOVERNANCE_DATA_DIR)
        try:
            protocols = [d for d in os.listdir(governance_dir) if os.path.isdir(os.path.join(governance_dir, d))]
            return protocols
        except FileNotFoundError:
            return []
    
    def get_available_sentiment_sources(self) -> List[str]:
        """
        Get list of available sentiment data sources.
        
        Returns:
            List of source names
        """
        sentiment_dir = os.path.join(self.base_dir, SENTIMENT_DATA_DIR)
        try:
            sources = [d for d in os.listdir(sentiment_dir) if os.path.isdir(os.path.join(sentiment_dir, d))]
            return sources
        except FileNotFoundError:
            return []
    
    def convert_price_data_to_dataframe(self, symbol: str, timeframe: str) -> pd.DataFrame:
        """
        Convert price data to pandas DataFrame.
        
        Args:
            symbol: Asset symbol
            timeframe: Timeframe
            
        Returns:
            DataFrame with price data
        """
        data = self.load_price_data(symbol, timeframe)
        if not data:
            return pd.DataFrame()
        
        df = pd.DataFrame(data)
        
        # Convert timestamp to datetime
        if "timestamp" in df.columns:
            df["timestamp"] = pd.to_datetime(df["timestamp"])
            df.set_index("timestamp", inplace=True)
        
        return df

def main():
    """Main function for testing the historical data storage."""
    storage = HistoricalDataStorage()
    
    # Sample price data
    price_data = [
        {
            "timestamp": "2023-01-01T00:00:00Z",
            "open": 16500.0,
            "high": 16800.0,
            "low": 16400.0,
            "close": 16700.0,
            "volume": 10000.0
        },
        {
            "timestamp": "2023-01-02T00:00:00Z",
            "open": 16700.0,
            "high": 17000.0,
            "low": 16600.0,
            "close": 16900.0,
            "volume": 12000.0
        },
        {
            "timestamp": "2023-01-03T00:00:00Z",
            "open": 16900.0,
            "high": 17200.0,
            "low": 16800.0,
            "close": 17100.0,
            "volume": 15000.0
        }
    ]
    
    # Store sample price data
    storage.store_price_data("BTC", "1d", price_data)
    
    # Load and display price data
    loaded_data = storage.load_price_data("BTC", "1d")
    print(f"Loaded {len(loaded_data)} price data points for BTC (1d)")
    
    # Convert to DataFrame
    df = storage.convert_price_data_to_dataframe("BTC", "1d")
    print("\nPrice DataFrame:")
    print(df)
    
    # Display available symbols and timeframes
    symbols = storage.get_available_symbols()
    print(f"\nAvailable symbols: {symbols}")
    
    for symbol in symbols:
        timeframes = storage.get_available_timeframes(symbol)
        print(f"Available timeframes for {symbol}: {timeframes}")

if __name__ == "__main__":
    main()
