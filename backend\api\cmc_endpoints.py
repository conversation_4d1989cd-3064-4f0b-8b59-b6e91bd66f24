"""
CoinMarketCap API Endpoints

This module provides API endpoints for accessing CoinMarketCap data.
"""

import os
import json
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from datetime import datetime

# Import CoinMarketCap client
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from data_aggregator.coinmarketcap_client import CoinMarketCapClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('cmc_api')

# Create router
router = APIRouter(
    prefix="/api/cmc",
    tags=["coinmarketcap"],
    responses={404: {"description": "Not found"}},
)

# Initialize CoinMarketCap client
cmc_client = CoinMarketCapClient()

# Data paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')

# Models
class Quote(BaseModel):
    price: float
    volume_24h: float
    volume_change_24h: float
    percent_change_1h: float
    percent_change_24h: float
    percent_change_7d: float
    market_cap: float
    market_cap_dominance: float
    fully_diluted_market_cap: float
    last_updated: str

class CoinData(BaseModel):
    id: int
    name: str
    symbol: str
    slug: str
    cmc_rank: int
    num_market_pairs: int
    circulating_supply: float
    total_supply: float
    max_supply: Optional[float]
    last_updated: str
    date_added: str
    tags: List[str]
    quote: Dict[str, Quote]

class MarketPair(BaseModel):
    exchange: Dict[str, Any]
    market_pair: str
    market_pair_base: Dict[str, Any]
    market_pair_quote: Dict[str, Any]
    quote: Dict[str, Any]

class GlobalMetrics(BaseModel):
    btc_dominance: float
    eth_dominance: float
    active_cryptocurrencies: int
    total_cryptocurrencies: int
    active_market_pairs: int
    active_exchanges: int
    total_exchanges: int
    last_updated: str
    quote: Dict[str, Any]

@router.get("/coins")
async def get_supported_coins():
    """Get a list of supported coins."""
    try:
        # Update coin mapping to get the latest coins
        coin_mapping = cmc_client.update_coin_mapping()
        
        # Return the list of supported coins
        return {"coins": list(coin_mapping.keys())}
    except Exception as e:
        logger.error(f"Error getting supported coins: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/quotes/{coin}")
async def get_coin_quotes(coin: str):
    """
    Get latest quotes for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    try:
        # Check if coin is supported
        coin_id = cmc_client.get_coin_id(coin.upper())
        if not coin_id:
            raise HTTPException(status_code=404, detail=f"Coin not found: {coin}")
        
        # Get quotes from CoinMarketCap
        quotes = cmc_client.get_quotes([coin.upper()])
        
        if not quotes or str(coin_id) not in quotes:
            # Try to get from saved data
            saved_data = cmc_client.get_latest_market_data(coin.upper())
            if "quotes" in saved_data:
                return saved_data["quotes"]
            else:
                raise HTTPException(status_code=404, detail=f"Quotes not found for {coin}")
        
        # Return the quotes data
        return quotes[str(coin_id)]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting quotes for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/market-pairs/{coin}")
async def get_coin_market_pairs(
    coin: str,
    limit: int = Query(100, description="Number of market pairs to return")
):
    """
    Get market pairs for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
        limit: Number of market pairs to return
    """
    try:
        # Check if coin is supported
        coin_id = cmc_client.get_coin_id(coin.upper())
        if not coin_id:
            raise HTTPException(status_code=404, detail=f"Coin not found: {coin}")
        
        # Get market pairs from CoinMarketCap
        market_pairs = cmc_client.get_market_pairs(coin.upper(), limit)
        
        if not market_pairs:
            # Try to get from saved data
            saved_data = cmc_client.get_latest_market_data(coin.upper())
            if "market_pairs" in saved_data:
                return saved_data["market_pairs"]
            else:
                raise HTTPException(status_code=404, detail=f"Market pairs not found for {coin}")
        
        # Return the market pairs data
        return market_pairs
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting market pairs for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/global-metrics")
async def get_global_metrics():
    """Get global cryptocurrency market metrics."""
    try:
        # Get global metrics from CoinMarketCap
        global_metrics = cmc_client.get_global_metrics()
        
        if not global_metrics:
            # Try to get from saved data
            saved_metrics = cmc_client.get_latest_global_metrics()
            if saved_metrics:
                return saved_metrics
            else:
                raise HTTPException(status_code=404, detail="Global metrics not found")
        
        # Return the global metrics data
        return global_metrics
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting global metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/price/{coin}")
async def get_coin_price(coin: str):
    """
    Get current price data for a specific coin.
    
    Args:
        coin: Cryptocurrency symbol (BTC, ETH, SOL, etc.)
    """
    try:
        # Get quotes for the coin
        quotes_response = await get_coin_quotes(coin)
        
        if not quotes_response or "quote" not in quotes_response or "USD" not in quotes_response["quote"]:
            raise HTTPException(status_code=404, detail=f"Price data not found for {coin}")
        
        # Extract price data
        quote = quotes_response["quote"]["USD"]
        
        # Format response
        return {
            "symbol": quotes_response["symbol"],
            "name": quotes_response["name"],
            "price_usd": quote["price"],
            "market_cap_usd": quote["market_cap"],
            "volume_24h_usd": quote["volume_24h"],
            "percent_change_1h": quote["percent_change_1h"],
            "percent_change_24h": quote["percent_change_24h"],
            "percent_change_7d": quote["percent_change_7d"],
            "last_updated": quote["last_updated"]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting price for {coin}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/refresh")
async def refresh_cmc_data():
    """Refresh CoinMarketCap data for all coins."""
    try:
        # Save market data for BTC, ETH, and SOL
        results = cmc_client.save_market_data(["BTC", "ETH", "SOL"])
        
        # Count successful saves
        success_count = sum(1 for files in results.values() if files)
        
        return {
            "status": "success" if success_count > 0 else "partial_success",
            "message": f"Refreshed CoinMarketCap data for {success_count} coins",
            "details": {coin: bool(files) for coin, files in results.items()}
        }
    except Exception as e:
        logger.error(f"Error refreshing CoinMarketCap data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
