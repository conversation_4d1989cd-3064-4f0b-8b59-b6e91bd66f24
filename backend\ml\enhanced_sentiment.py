"""
Enhanced Sentiment Analysis

This module implements advanced sentiment analysis for cryptocurrency news and social media.
"""

import logging
import re
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple

import numpy as np
from transformers import pipeline, AutoModelForSequenceClassification, AutoTokenizer
from nltk.tokenize import sent_tokenize
import nltk

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Download NLTK resources if needed
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

class EnhancedSentimentAnalyzer:
    """Advanced sentiment analysis for cryptocurrency news and social media."""
    
    def __init__(self, model_name: str = "finiteautomata/bertweet-base-sentiment-analysis"):
        """
        Initialize the sentiment analyzer.
        
        Args:
            model_name: Name of the pre-trained model to use
        """
        self.model_name = model_name
        self.sentiment_analyzer = None
        self.initialize_model()
        
        # Entity recognition for cryptocurrencies
        self.crypto_keywords = {
            "bitcoin": ["bitcoin", "btc", "xbt", "satoshi", "bitcoins"],
            "ethereum": ["ethereum", "eth", "ether", "vitalik", "buterin"],
            "solana": ["solana", "sol"],
            "binance": ["binance", "bnb", "binance coin"],
            "ripple": ["ripple", "xrp"],
            "cardano": ["cardano", "ada"],
            "dogecoin": ["dogecoin", "doge"],
            "polkadot": ["polkadot", "dot"],
            "chainlink": ["chainlink", "link"],
            "litecoin": ["litecoin", "ltc"]
        }
        
        # Sentiment modifiers
        self.bullish_terms = [
            "bullish", "buy", "long", "moon", "rally", "surge", "soar", "gain", "rise", "uptrend",
            "breakout", "support", "accumulate", "hodl", "hold", "diamond hands", "undervalued"
        ]
        
        self.bearish_terms = [
            "bearish", "sell", "short", "dump", "crash", "plunge", "drop", "fall", "downtrend",
            "breakdown", "resistance", "distribute", "overvalued", "bubble", "correction"
        ]
        
        logger.info(f"Enhanced sentiment analyzer initialized with model {model_name}")
    
    def initialize_model(self):
        """Initialize the sentiment analysis model."""
        try:
            self.sentiment_analyzer = pipeline(
                "sentiment-analysis",
                model=self.model_name,
                tokenizer=self.model_name
            )
            logger.info("Sentiment analysis model loaded successfully")
        except Exception as e:
            logger.error(f"Error loading sentiment analysis model: {str(e)}")
            # Fallback to a simpler model
            try:
                self.sentiment_analyzer = pipeline("sentiment-analysis")
                logger.info("Fallback sentiment analysis model loaded")
            except Exception as e:
                logger.error(f"Error loading fallback model: {str(e)}")
                raise
    
    def analyze_text(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of a text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        if not text or not self.sentiment_analyzer:
            return {
                "sentiment": "neutral",
                "score": 0.5,
                "entities": [],
                "confidence": 0.0
            }
        
        try:
            # Clean text
            cleaned_text = self._clean_text(text)
            
            # Split into sentences for more granular analysis
            sentences = sent_tokenize(cleaned_text)
            
            # Analyze each sentence
            results = []
            for sentence in sentences:
                if len(sentence.split()) < 3:  # Skip very short sentences
                    continue
                
                # Get sentiment from model
                sentiment_result = self.sentiment_analyzer(sentence)
                
                # Extract label and score
                label = sentiment_result[0]["label"].lower()
                score = sentiment_result[0]["score"]
                
                # Map to standard sentiment
                if label in ["positive", "pos"]:
                    sentiment = "positive"
                    normalized_score = score
                elif label in ["negative", "neg"]:
                    sentiment = "negative"
                    normalized_score = score
                else:
                    sentiment = "neutral"
                    normalized_score = 0.5
                
                # Adjust sentiment based on financial terms
                sentiment, normalized_score = self._adjust_sentiment(sentence, sentiment, normalized_score)
                
                results.append({
                    "sentence": sentence,
                    "sentiment": sentiment,
                    "score": normalized_score
                })
            
            # Extract mentioned entities
            entities = self._extract_entities(cleaned_text)
            
            # Calculate overall sentiment
            if results:
                # Weight longer sentences more heavily
                weights = [len(r["sentence"].split()) for r in results]
                total_weight = sum(weights)
                
                if total_weight > 0:
                    # Convert sentiment to numeric (-1 to 1)
                    sentiment_values = []
                    for r, w in zip(results, weights):
                        if r["sentiment"] == "positive":
                            sentiment_values.append(r["score"] * w)
                        elif r["sentiment"] == "negative":
                            sentiment_values.append(-r["score"] * w)
                        else:
                            sentiment_values.append(0)
                    
                    weighted_sentiment = sum(sentiment_values) / total_weight
                    
                    # Convert back to label and score
                    if weighted_sentiment > 0.1:
                        overall_sentiment = "positive"
                        overall_score = weighted_sentiment
                    elif weighted_sentiment < -0.1:
                        overall_sentiment = "negative"
                        overall_score = -weighted_sentiment
                    else:
                        overall_sentiment = "neutral"
                        overall_score = 0.5
                    
                    # Calculate confidence based on consistency
                    sentiment_std = np.std([
                        1 if r["sentiment"] == "positive" else (-1 if r["sentiment"] == "negative" else 0)
                        for r in results
                    ])
                    confidence = 1.0 - min(1.0, sentiment_std)
                else:
                    overall_sentiment = "neutral"
                    overall_score = 0.5
                    confidence = 0.0
            else:
                overall_sentiment = "neutral"
                overall_score = 0.5
                confidence = 0.0
            
            return {
                "sentiment": overall_sentiment,
                "score": overall_score,
                "entities": entities,
                "confidence": confidence,
                "sentence_analysis": results[:5]  # Include top 5 sentences for reference
            }
        
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")
            return {
                "sentiment": "neutral",
                "score": 0.5,
                "entities": [],
                "confidence": 0.0
            }
    
    def _clean_text(self, text: str) -> str:
        """Clean text for analysis."""
        # Convert to lowercase
        text = text.lower()
        
        # Remove URLs
        text = re.sub(r'https?://\S+|www\.\S+', '', text)
        
        # Remove HTML tags
        text = re.sub(r'<.*?>', '', text)
        
        # Remove special characters but keep sentence structure
        text = re.sub(r'[^\w\s.,!?]', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _adjust_sentiment(self, text: str, sentiment: str, score: float) -> Tuple[str, float]:
        """
        Adjust sentiment based on financial and crypto-specific terms.
        
        Args:
            text: Text to analyze
            sentiment: Initial sentiment
            score: Initial sentiment score
            
        Returns:
            Tuple of (adjusted_sentiment, adjusted_score)
        """
        text = text.lower()
        
        # Count bullish and bearish terms
        bullish_count = sum(1 for term in self.bullish_terms if term in text)
        bearish_count = sum(1 for term in self.bearish_terms if term in text)
        
        # Adjust sentiment based on financial terms
        if bullish_count > bearish_count:
            # Boost positive sentiment or reduce negative sentiment
            if sentiment == "positive":
                score = min(1.0, score + 0.1 * bullish_count)
            elif sentiment == "negative":
                score = max(0.0, score - 0.1 * bullish_count)
                if score < 0.4:  # If score is significantly reduced, change sentiment
                    sentiment = "neutral"
            else:  # neutral
                if bullish_count >= 2:  # Multiple bullish terms can change neutral to positive
                    sentiment = "positive"
                    score = 0.5 + 0.1 * bullish_count
        
        elif bearish_count > bullish_count:
            # Boost negative sentiment or reduce positive sentiment
            if sentiment == "negative":
                score = min(1.0, score + 0.1 * bearish_count)
            elif sentiment == "positive":
                score = max(0.0, score - 0.1 * bearish_count)
                if score < 0.4:  # If score is significantly reduced, change sentiment
                    sentiment = "neutral"
            else:  # neutral
                if bearish_count >= 2:  # Multiple bearish terms can change neutral to negative
                    sentiment = "negative"
                    score = 0.5 + 0.1 * bearish_count
        
        return sentiment, score
    
    def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract cryptocurrency entities from text.
        
        Args:
            text: Text to analyze
            
        Returns:
            List of dictionaries with entity information
        """
        text = text.lower()
        entities = []
        
        for crypto, keywords in self.crypto_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    # Check if it's a standalone word
                    pattern = r'\b' + re.escape(keyword) + r'\b'
                    if re.search(pattern, text):
                        entities.append({
                            "entity": crypto,
                            "keyword": keyword
                        })
                        break  # Found one keyword for this crypto, no need to check others
        
        return entities
    
    def analyze_multiple_texts(self, texts: List[str]) -> Dict[str, Any]:
        """
        Analyze sentiment of multiple texts and aggregate results.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            Dictionary with aggregated sentiment analysis results
        """
        if not texts:
            return {
                "overall_sentiment": "neutral",
                "sentiment_score": 0.5,
                "entity_sentiments": {},
                "confidence": 0.0
            }
        
        # Analyze each text
        results = [self.analyze_text(text) for text in texts]
        
        # Extract all entities
        all_entities = set()
        for result in results:
            for entity in result["entities"]:
                all_entities.add(entity["entity"])
        
        # Calculate sentiment for each entity
        entity_sentiments = {}
        for entity in all_entities:
            entity_results = []
            
            for result in results:
                if any(e["entity"] == entity for e in result["entities"]):
                    if result["sentiment"] == "positive":
                        entity_results.append(result["score"])
                    elif result["sentiment"] == "negative":
                        entity_results.append(-result["score"])
                    else:
                        entity_results.append(0)
            
            if entity_results:
                avg_sentiment = sum(entity_results) / len(entity_results)
                
                if avg_sentiment > 0.1:
                    entity_sentiments[entity] = {
                        "sentiment": "positive",
                        "score": avg_sentiment,
                        "mentions": len(entity_results)
                    }
                elif avg_sentiment < -0.1:
                    entity_sentiments[entity] = {
                        "sentiment": "negative",
                        "score": -avg_sentiment,
                        "mentions": len(entity_results)
                    }
                else:
                    entity_sentiments[entity] = {
                        "sentiment": "neutral",
                        "score": 0.5,
                        "mentions": len(entity_results)
                    }
        
        # Calculate overall sentiment
        sentiment_values = []
        for result in results:
            if result["sentiment"] == "positive":
                sentiment_values.append(result["score"])
            elif result["sentiment"] == "negative":
                sentiment_values.append(-result["score"])
            else:
                sentiment_values.append(0)
        
        if sentiment_values:
            avg_sentiment = sum(sentiment_values) / len(sentiment_values)
            
            if avg_sentiment > 0.1:
                overall_sentiment = "positive"
                sentiment_score = avg_sentiment
            elif avg_sentiment < -0.1:
                overall_sentiment = "negative"
                sentiment_score = -avg_sentiment
            else:
                overall_sentiment = "neutral"
                sentiment_score = 0.5
            
            # Calculate confidence based on consistency
            sentiment_std = np.std(sentiment_values)
            confidence = 1.0 - min(1.0, sentiment_std)
        else:
            overall_sentiment = "neutral"
            sentiment_score = 0.5
            confidence = 0.0
        
        return {
            "overall_sentiment": overall_sentiment,
            "sentiment_score": sentiment_score,
            "entity_sentiments": entity_sentiments,
            "confidence": confidence,
            "texts_analyzed": len(texts)
        }
    
    def to_context_items(self, sentiment_results: Dict[str, Any], source: str) -> List[Dict[str, Any]]:
        """
        Convert sentiment analysis results to context items for MCP.
        
        Args:
            sentiment_results: Dictionary with sentiment analysis results
            source: Source of the analyzed texts (e.g., "twitter", "reddit", "news")
            
        Returns:
            List of context items
        """
        context_items = []
        
        # Overall sentiment context item
        context_items.append({
            "id": f"sentiment_{source}_overall_{uuid.uuid4()}",
            "type": "sentiment_analysis",
            "content": {
                "sentiment": sentiment_results["overall_sentiment"],
                "score": sentiment_results["sentiment_score"],
                "source": source,
                "texts_analyzed": sentiment_results["texts_analyzed"]
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": source,
            "confidence": sentiment_results["confidence"]
        })
        
        # Entity sentiment context items
        for entity, entity_sentiment in sentiment_results["entity_sentiments"].items():
            context_items.append({
                "id": f"sentiment_{source}_{entity}_{uuid.uuid4()}",
                "type": "entity_sentiment",
                "content": {
                    "entity": entity,
                    "sentiment": entity_sentiment["sentiment"],
                    "score": entity_sentiment["score"],
                    "mentions": entity_sentiment["mentions"],
                    "source": source
                },
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": source,
                "confidence": sentiment_results["confidence"]
            })
        
        return context_items

def main():
    """Main function for testing the enhanced sentiment analyzer."""
    # Sample texts
    texts = [
        "Bitcoin is showing strong bullish momentum as institutional adoption increases. The recent breakout above $60,000 suggests further upside potential.",
        "Ethereum's transition to proof-of-stake could face delays, raising concerns about the network's ability to scale. Some developers are worried about potential security issues.",
        "Solana's network performance has improved significantly after recent upgrades. Transaction speeds have increased while fees remain low, attracting more developers to the ecosystem.",
        "Cryptocurrency markets are experiencing increased volatility as regulatory uncertainty persists. Traders should exercise caution in the current environment."
    ]
    
    # Initialize sentiment analyzer
    analyzer = EnhancedSentimentAnalyzer()
    
    # Analyze individual texts
    print("\n===== INDIVIDUAL TEXT ANALYSIS =====")
    for i, text in enumerate(texts):
        result = analyzer.analyze_text(text)
        print(f"\nText {i+1}:")
        print(f"Sentiment: {result['sentiment']}")
        print(f"Score: {result['score']:.4f}")
        print(f"Confidence: {result['confidence']:.4f}")
        print(f"Entities: {', '.join(e['entity'] for e in result['entities'])}")
    
    # Analyze multiple texts
    print("\n===== AGGREGATED ANALYSIS =====")
    aggregated_result = analyzer.analyze_multiple_texts(texts)
    print(f"Overall Sentiment: {aggregated_result['overall_sentiment']}")
    print(f"Sentiment Score: {aggregated_result['sentiment_score']:.4f}")
    print(f"Confidence: {aggregated_result['confidence']:.4f}")
    print("\nEntity Sentiments:")
    for entity, sentiment in aggregated_result["entity_sentiments"].items():
        print(f"  {entity}: {sentiment['sentiment']} ({sentiment['score']:.4f}), {sentiment['mentions']} mentions")
    
    # Convert to context items
    context_items = analyzer.to_context_items(aggregated_result, "news")
    print(f"\nGenerated {len(context_items)} context items")

if __name__ == "__main__":
    main()
