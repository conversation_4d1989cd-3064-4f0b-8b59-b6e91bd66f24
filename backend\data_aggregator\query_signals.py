"""
Query the MCP server for governance signals.

This script queries the MCP server for governance signals and prints them.
"""

import requests
import json

def main():
    """Query the MCP server for governance signals."""
    print("Querying MCP server for governance signals...")

    # First, let's check if the MCP server is running
    try:
        health_response = requests.get("http://127.0.0.1:8000/health")
        print(f"MCP server health check: {health_response.status_code}")
        if health_response.status_code != 200:
            print("MCP server may not be running properly.")
    except Exception as e:
        print(f"Error connecting to MCP server: {e}")
        print("Make sure the MCP server is running.")
        return

    # Try to list all available endpoints
    try:
        root_response = requests.get("http://127.0.0.1:8000/")
        print(f"\nMCP server root response: {root_response.status_code}")
        if root_response.status_code == 200:
            print("Available endpoints:")
            print(root_response.text)
    except Exception as e:
        print(f"Error getting root endpoint: {e}")

    # Try to get all context items
    try:
        context_response = requests.get("http://127.0.0.1:8000/context")
        print(f"\nContext endpoint response: {context_response.status_code}")
        if context_response.status_code == 200:
            items = context_response.json()
            governance_signals = [item for item in items if item.get('type') == 'governance_signal']

            print(f"\nFound {len(governance_signals)} governance signals out of {len(items)} total items:")

            # Print each governance signal
            for i, signal in enumerate(governance_signals, 1):
                print(f"\n--- Signal {i} ---")
                print(f"Type: {signal['type']}")
                print(f"Source: {signal['source']}")
                print(f"Timestamp: {signal['timestamp']}")
                print(f"Content: {json.dumps(signal['content'], indent=2)}")
        else:
            print(f"Error: {context_response.status_code} - {context_response.text}")

    except Exception as e:
        print(f"Error querying context items: {e}")

if __name__ == "__main__":
    main()
