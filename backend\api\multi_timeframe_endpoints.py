"""
API endpoints for multi-timeframe analysis and signals.
"""

import os
import json
import logging
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException
import asyncio

from backend.signal_engine.multi_timeframe_signal_generator import MultiTimeframeSignalGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define router
router = APIRouter()

# Define signals directory
SIGNALS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "signals")
os.makedirs(SIGNALS_DIR, exist_ok=True)

# Initialize signal generator
signal_generator = MultiTimeframeSignalGenerator()

@router.get("/multi-timeframe/signals", response_model=List[Dict[str, Any]])
async def get_multi_timeframe_signals():
    """
    Get multi-timeframe trading signals.
    
    Returns:
        List of trading signals
    """
    try:
        # Check if we have recent signals
        latest_file = os.path.join(SIGNALS_DIR, "multi_timeframe_signals_latest.json")
        
        if os.path.exists(latest_file):
            # Check if file is recent (less than 1 hour old)
            file_age = os.path.getmtime(latest_file)
            import time
            if time.time() - file_age < 3600:
                # Use existing signals
                with open(latest_file, "r") as f:
                    signals = json.load(f)
                logger.info(f"Returning {len(signals)} cached multi-timeframe signals")
                return signals
        
        # Generate new signals
        signals = await signal_generator.generate_signals()
        signal_dicts = [signal.dict() for signal in signals]
        
        logger.info(f"Generated {len(signals)} new multi-timeframe signals")
        return signal_dicts
    
    except Exception as e:
        logger.error(f"Error getting multi-timeframe signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting signals: {str(e)}")

@router.get("/multi-timeframe/analysis/{symbol}")
async def get_multi_timeframe_analysis(symbol: str):
    """
    Get detailed multi-timeframe analysis for a specific symbol.
    
    Args:
        symbol: Cryptocurrency symbol (e.g., BTC)
        
    Returns:
        Detailed analysis results
    """
    try:
        # Analyze symbol
        analysis = await signal_generator.analyzer.analyze_multiple_timeframes(symbol.upper())
        
        logger.info(f"Generated multi-timeframe analysis for {symbol}")
        return analysis
    
    except Exception as e:
        logger.error(f"Error getting multi-timeframe analysis for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting analysis: {str(e)}")

@router.get("/multi-timeframe/combined-signals", response_model=List[Dict[str, Any]])
async def get_combined_signals():
    """
    Get combined signals from multi-timeframe and base generators.
    
    Returns:
        List of combined trading signals
    """
    try:
        # Generate combined signals
        signals = await signal_generator.generate_and_combine_signals()
        signal_dicts = [signal.dict() for signal in signals]
        
        logger.info(f"Generated {len(signals)} combined signals")
        return signal_dicts
    
    except Exception as e:
        logger.error(f"Error getting combined signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting signals: {str(e)}")
