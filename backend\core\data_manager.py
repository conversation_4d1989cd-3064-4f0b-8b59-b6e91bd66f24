"""
Data Manager for MCP Trading Platform

This module consolidates all data fetching and management functionality
to eliminate duplication and improve efficiency.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

# Import data fetchers
from data_aggregator.coingecko_client import CoinGeckoFetcher
from data_aggregator.cryptocompare_client import CryptoCompareFetcher
from data_aggregator.coinmarketcap_client import CoinMarketCapFetcher
from data_aggregator.binance_fetcher import BinanceFetcher
from data_aggregator.onchain_data_fetcher import OnChainDataFetcher
from data_aggregator.sentiment_analyzer import SentimentAnalyzer
from data_aggregator.economic_indicators import EconomicIndicatorsFetcher

logger = logging.getLogger(__name__)

class DataSource(str, Enum):
    """Enumeration of available data sources."""
    COINGECKO = "coingecko"
    CRYPTOCOMPARE = "cryptocompare"
    COINMARKETCAP = "coinmarketcap"
    BINANCE = "binance"
    ONCHAIN = "onchain"
    SENTIMENT = "sentiment"
    ECONOMIC = "economic"

@dataclass
class DataFetchResult:
    """Result of a data fetch operation."""
    source: DataSource
    data_type: str
    data: Any
    timestamp: datetime
    success: bool
    error: Optional[str] = None

class DataManager:
    """Centralized data manager for the trading platform."""
    
    def __init__(self, settings):
        self.settings = settings
        self.fetchers: Dict[DataSource, Any] = {}
        self.last_fetch_times: Dict[str, datetime] = {}
        self.fetch_intervals: Dict[str, int] = {
            "prices": 60,  # 1 minute
            "onchain": 300,  # 5 minutes
            "sentiment": 900,  # 15 minutes
            "economic": 3600,  # 1 hour
        }
        
    async def initialize(self):
        """Initialize all data fetchers."""
        logger.info("Initializing data manager...")
        
        # Initialize fetchers based on available API keys
        if self.settings.api.coingecko_api_key:
            self.fetchers[DataSource.COINGECKO] = CoinGeckoFetcher(
                api_key=self.settings.api.coingecko_api_key
            )
            
        if self.settings.api.cryptocompare_api_key:
            self.fetchers[DataSource.CRYPTOCOMPARE] = CryptoCompareFetcher(
                api_key=self.settings.api.cryptocompare_api_key
            )
            
        if self.settings.api.coinmarketcap_api_key:
            self.fetchers[DataSource.COINMARKETCAP] = CoinMarketCapFetcher(
                api_key=self.settings.api.coinmarketcap_api_key
            )
            
        if self.settings.api.binance_api_key:
            self.fetchers[DataSource.BINANCE] = BinanceFetcher(
                api_key=self.settings.api.binance_api_key,
                secret_key=self.settings.api.binance_secret_key
            )
            
        # Initialize on-chain data fetcher
        self.fetchers[DataSource.ONCHAIN] = OnChainDataFetcher({
            "coingecko": self.settings.api.coingecko_api_key,
            "etherscan": None,  # Add when available
            "bscscan": None,   # Add when available
        })
        
        # Initialize sentiment analyzer
        self.fetchers[DataSource.SENTIMENT] = SentimentAnalyzer({
            "newsapi": self.settings.api.newsapi_key,
            "twitter": None,  # Add when available
            "reddit": None,   # Add when available
        })
        
        # Initialize economic indicators fetcher
        if self.settings.api.alpha_vantage_api_key:
            self.fetchers[DataSource.ECONOMIC] = EconomicIndicatorsFetcher(
                api_key=self.settings.api.alpha_vantage_api_key
            )
        
        logger.info(f"Initialized {len(self.fetchers)} data fetchers")
    
    async def cleanup(self):
        """Clean up data manager resources."""
        logger.info("Cleaning up data manager...")
        # Close any open connections
        for fetcher in self.fetchers.values():
            if hasattr(fetcher, 'close'):
                await fetcher.close()
    
    async def fetch_price_data(self, symbols: List[str], source: Optional[DataSource] = None) -> List[DataFetchResult]:
        """Fetch price data for specified symbols."""
        results = []
        
        # Determine which sources to use
        sources_to_try = [source] if source else [
            DataSource.COINGECKO,
            DataSource.CRYPTOCOMPARE,
            DataSource.COINMARKETCAP
        ]
        
        for src in sources_to_try:
            if src not in self.fetchers:
                continue
                
            try:
                fetcher = self.fetchers[src]
                
                if src == DataSource.COINGECKO:
                    data = await fetcher.fetch_prices(symbols)
                elif src == DataSource.CRYPTOCOMPARE:
                    data = await fetcher.fetch_multiple_prices(symbols)
                elif src == DataSource.COINMARKETCAP:
                    data = await fetcher.fetch_quotes(symbols)
                else:
                    continue
                
                results.append(DataFetchResult(
                    source=src,
                    data_type="prices",
                    data=data,
                    timestamp=datetime.now(),
                    success=True
                ))
                
                # If we got data from one source, we can stop
                if data:
                    break
                    
            except Exception as e:
                logger.error(f"Error fetching prices from {src}: {e}")
                results.append(DataFetchResult(
                    source=src,
                    data_type="prices",
                    data=None,
                    timestamp=datetime.now(),
                    success=False,
                    error=str(e)
                ))
        
        return results
    
    async def fetch_onchain_data(self, symbols: List[str]) -> List[DataFetchResult]:
        """Fetch on-chain data for specified symbols."""
        results = []
        
        if DataSource.ONCHAIN not in self.fetchers:
            return results
        
        try:
            fetcher = self.fetchers[DataSource.ONCHAIN]
            
            for symbol in symbols:
                # Convert symbol to asset ID (e.g., BTC -> bitcoin)
                asset_id = self._symbol_to_asset_id(symbol)
                
                # Fetch various on-chain metrics
                metrics = await fetcher.fetch_all_metrics([asset_id])
                
                results.append(DataFetchResult(
                    source=DataSource.ONCHAIN,
                    data_type=f"onchain_{symbol}",
                    data=metrics,
                    timestamp=datetime.now(),
                    success=True
                ))
                
        except Exception as e:
            logger.error(f"Error fetching on-chain data: {e}")
            results.append(DataFetchResult(
                source=DataSource.ONCHAIN,
                data_type="onchain",
                data=None,
                timestamp=datetime.now(),
                success=False,
                error=str(e)
            ))
        
        return results
    
    async def fetch_sentiment_data(self, symbols: List[str]) -> List[DataFetchResult]:
        """Fetch sentiment data for specified symbols."""
        results = []
        
        if DataSource.SENTIMENT not in self.fetchers:
            return results
        
        try:
            fetcher = self.fetchers[DataSource.SENTIMENT]
            
            # Fetch news sentiment
            news_sentiment = await fetcher.analyze_news_sentiment(symbols)
            
            results.append(DataFetchResult(
                source=DataSource.SENTIMENT,
                data_type="news_sentiment",
                data=news_sentiment,
                timestamp=datetime.now(),
                success=True
            ))
            
        except Exception as e:
            logger.error(f"Error fetching sentiment data: {e}")
            results.append(DataFetchResult(
                source=DataSource.SENTIMENT,
                data_type="sentiment",
                data=None,
                timestamp=datetime.now(),
                success=False,
                error=str(e)
            ))
        
        return results
    
    async def fetch_economic_data(self) -> List[DataFetchResult]:
        """Fetch economic indicators data."""
        results = []
        
        if DataSource.ECONOMIC not in self.fetchers:
            return results
        
        try:
            fetcher = self.fetchers[DataSource.ECONOMIC]
            
            # Fetch key economic indicators
            indicators = await fetcher.fetch_all_indicators()
            
            results.append(DataFetchResult(
                source=DataSource.ECONOMIC,
                data_type="economic_indicators",
                data=indicators,
                timestamp=datetime.now(),
                success=True
            ))
            
        except Exception as e:
            logger.error(f"Error fetching economic data: {e}")
            results.append(DataFetchResult(
                source=DataSource.ECONOMIC,
                data_type="economic",
                data=None,
                timestamp=datetime.now(),
                success=False,
                error=str(e)
            ))
        
        return results
    
    async def fetch_all_data(self, symbols: List[str]) -> Dict[str, List[DataFetchResult]]:
        """Fetch all available data for specified symbols."""
        logger.info(f"Fetching all data for symbols: {symbols}")
        
        # Run all fetch operations concurrently
        tasks = [
            self.fetch_price_data(symbols),
            self.fetch_onchain_data(symbols),
            self.fetch_sentiment_data(symbols),
            self.fetch_economic_data()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Organize results by data type
        organized_results = {
            "prices": results[0] if not isinstance(results[0], Exception) else [],
            "onchain": results[1] if not isinstance(results[1], Exception) else [],
            "sentiment": results[2] if not isinstance(results[2], Exception) else [],
            "economic": results[3] if not isinstance(results[3], Exception) else []
        }
        
        return organized_results
    
    def _symbol_to_asset_id(self, symbol: str) -> str:
        """Convert symbol to asset ID for API calls."""
        symbol_mapping = {
            "BTC": "bitcoin",
            "ETH": "ethereum",
            "SOL": "solana",
            "ADA": "cardano",
            "DOT": "polkadot",
            "LINK": "chainlink",
            "UNI": "uniswap",
            "AAVE": "aave"
        }
        return symbol_mapping.get(symbol.upper(), symbol.lower())
    
    def should_fetch(self, data_type: str) -> bool:
        """Check if data should be fetched based on intervals."""
        if data_type not in self.last_fetch_times:
            return True
        
        last_fetch = self.last_fetch_times[data_type]
        interval = self.fetch_intervals.get(data_type, 300)  # Default 5 minutes
        
        return (datetime.now() - last_fetch).total_seconds() >= interval
    
    def update_fetch_time(self, data_type: str):
        """Update the last fetch time for a data type."""
        self.last_fetch_times[data_type] = datetime.now()
