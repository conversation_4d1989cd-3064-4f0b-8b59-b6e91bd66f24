"""
Multi-Timeframe Analysis Engine for Project Ruby.

This module provides functionality to analyze cryptocurrency price data across
multiple timeframes and generate consensus signals that are more reliable than
single timeframe analysis.
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import aiohttp
import asyncio
import json
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define timeframes
TIMEFRAMES = ["5m", "15m", "1h", "4h", "1d", "1w"]

class MultiTimeframeAnalyzer:
    """Analyzes cryptocurrency data across multiple timeframes."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the multi-timeframe analyzer.
        
        Args:
            api_key: API key for data provider (optional)
        """
        self.api_key = api_key or os.environ.get("CRYPTOCOMPARE_API_KEY", "****************************************************************")
        logger.info("Initialized MultiTimeframeAnalyzer")
        
    async def fetch_historical_data(self, symbol: str, timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Fetch historical price data for a specific symbol and timeframe.
        
        Args:
            symbol: Cryptocurrency symbol (e.g., BTC)
            timeframe: Timeframe (e.g., 1h, 4h, 1d)
            limit: Number of data points to fetch
            
        Returns:
            List of price data points
        """
        # Map timeframe to CryptoCompare format
        tf_mapping = {
            "5m": "minute", "15m": "minute", "30m": "minute",
            "1h": "hour", "4h": "hour", 
            "1d": "day", "1w": "day"
        }
        
        # Map timeframe to aggregation value
        agg_mapping = {
            "5m": 5, "15m": 15, "30m": 30,
            "1h": 1, "4h": 4,
            "1d": 1, "1w": 7
        }
        
        base_url = "https://min-api.cryptocompare.com/data/v2/histo"
        
        # Determine endpoint and aggregation
        endpoint = tf_mapping.get(timeframe, "hour")
        aggregation = agg_mapping.get(timeframe, 1)
        
        url = f"{base_url}{endpoint}"
        params = {
            "fsym": symbol,
            "tsym": "USD",
            "limit": limit,
            "aggregate": aggregation,
            "api_key": self.api_key
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data["Response"] == "Success":
                            return data["Data"]["Data"]
                        else:
                            logger.error(f"API error: {data.get('Message', 'Unknown error')}")
                            return []
                    else:
                        logger.error(f"HTTP error: {response.status}")
                        return []
        except Exception as e:
            logger.error(f"Error fetching data: {str(e)}")
            return []
    
    def calculate_trend(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate trend indicators from price data.
        
        Args:
            data: List of price data points
            
        Returns:
            Dictionary with trend indicators
        """
        if not data or len(data) < 20:
            return {"trend": "neutral", "strength": 0.5, "direction": 0}
        
        # Extract closing prices
        closes = np.array([item["close"] for item in data])
        
        # Calculate short and long term moving averages
        sma_5 = np.mean(closes[-5:])
        sma_10 = np.mean(closes[-10:])
        sma_20 = np.mean(closes[-20:])
        
        # Calculate trend direction and strength
        direction = 0
        if sma_5 > sma_10 > sma_20:
            direction = 1  # Strong uptrend
            trend = "bullish"
        elif sma_5 < sma_10 < sma_20:
            direction = -1  # Strong downtrend
            trend = "bearish"
        elif sma_5 > sma_10:
            direction = 0.5  # Weak uptrend
            trend = "slightly bullish"
        elif sma_5 < sma_10:
            direction = -0.5  # Weak downtrend
            trend = "slightly bearish"
        else:
            trend = "neutral"
        
        # Calculate trend strength (0.5 to 1.0)
        if direction != 0:
            # Calculate percentage difference between short and long MA
            pct_diff = abs((sma_5 - sma_20) / sma_20)
            strength = min(1.0, 0.5 + pct_diff)
        else:
            strength = 0.5
        
        return {
            "trend": trend,
            "strength": strength,
            "direction": direction,
            "sma_5": sma_5,
            "sma_10": sma_10,
            "sma_20": sma_20
        }
    
    def calculate_momentum(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate momentum indicators from price data.
        
        Args:
            data: List of price data points
            
        Returns:
            Dictionary with momentum indicators
        """
        if not data or len(data) < 14:
            return {"momentum": "neutral", "strength": 0.5, "rsi": 50}
        
        # Extract closing prices
        closes = np.array([item["close"] for item in data])
        
        # Calculate RSI
        deltas = np.diff(closes)
        seed = deltas[:14]
        up = seed[seed >= 0].sum() / 14
        down = -seed[seed < 0].sum() / 14
        rs = up / down if down != 0 else float('inf')
        rsi = 100 - (100 / (1 + rs))
        
        # Determine momentum
        if rsi > 70:
            momentum = "overbought"
            direction = -1
        elif rsi < 30:
            momentum = "oversold"
            direction = 1
        elif rsi > 50:
            momentum = "positive"
            direction = 0.5
        elif rsi < 50:
            momentum = "negative"
            direction = -0.5
        else:
            momentum = "neutral"
            direction = 0
        
        # Calculate momentum strength (0.5 to 1.0)
        if rsi > 50:
            strength = 0.5 + min(0.5, (rsi - 50) / 40)
        else:
            strength = 0.5 + min(0.5, (50 - rsi) / 40)
        
        return {
            "momentum": momentum,
            "strength": strength,
            "direction": direction,
            "rsi": rsi
        }
    
    def calculate_volatility(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate volatility indicators from price data.
        
        Args:
            data: List of price data points
            
        Returns:
            Dictionary with volatility indicators
        """
        if not data or len(data) < 20:
            return {"volatility": "medium", "atr": 0, "atr_pct": 0}
        
        # Extract high, low, close prices
        highs = np.array([item["high"] for item in data])
        lows = np.array([item["low"] for item in data])
        closes = np.array([item["close"] for item in data])
        
        # Calculate ATR (Average True Range)
        tr1 = np.abs(highs[1:] - lows[1:])
        tr2 = np.abs(highs[1:] - closes[:-1])
        tr3 = np.abs(lows[1:] - closes[:-1])
        
        # True range is the maximum of the three
        tr = np.maximum(np.maximum(tr1, tr2), tr3)
        
        # 14-period ATR
        atr = np.mean(tr[-14:])
        
        # ATR as percentage of price
        atr_pct = atr / closes[-1] * 100
        
        # Determine volatility level
        if atr_pct < 1:
            volatility = "low"
        elif atr_pct < 3:
            volatility = "medium"
        else:
            volatility = "high"
        
        return {
            "volatility": volatility,
            "atr": atr,
            "atr_pct": atr_pct
        }
    
    def identify_key_levels(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Identify key support and resistance levels.
        
        Args:
            data: List of price data points
            
        Returns:
            Dictionary with key levels
        """
        if not data or len(data) < 20:
            return {"support": [], "resistance": []}
        
        # Extract high, low, close prices
        highs = np.array([item["high"] for item in data])
        lows = np.array([item["low"] for item in data])
        closes = np.array([item["close"] for item in data])
        
        current_price = closes[-1]
        
        # Simple method to identify support/resistance:
        # Find local minima and maxima
        support_levels = []
        resistance_levels = []
        
        # Look for patterns in the last 30 periods
        window = min(30, len(closes))
        
        for i in range(2, window - 2):
            # Check for local minima (support)
            if lows[-i] < lows[-i-1] and lows[-i] < lows[-i-2] and lows[-i] < lows[-i+1] and lows[-i] < lows[-i+2]:
                support_levels.append(lows[-i])
            
            # Check for local maxima (resistance)
            if highs[-i] > highs[-i-1] and highs[-i] > highs[-i-2] and highs[-i] > highs[-i+1] and highs[-i] > highs[-i+2]:
                resistance_levels.append(highs[-i])
        
        # Filter levels that are close to current price (within 10%)
        support_levels = [level for level in support_levels if level < current_price and level > current_price * 0.9]
        resistance_levels = [level for level in resistance_levels if level > current_price and level < current_price * 1.1]
        
        # Sort levels
        support_levels.sort(reverse=True)
        resistance_levels.sort()
        
        # Take top 3 closest levels
        support_levels = support_levels[:3]
        resistance_levels = resistance_levels[:3]
        
        return {
            "support": support_levels,
            "resistance": resistance_levels
        }
    
    def generate_signal_for_timeframe(self, data: List[Dict[str, Any]], timeframe: str) -> Dict[str, Any]:
        """
        Generate trading signal for a specific timeframe.
        
        Args:
            data: List of price data points
            timeframe: Timeframe (e.g., 1h, 4h, 1d)
            
        Returns:
            Dictionary with signal information
        """
        if not data:
            return {
                "timeframe": timeframe,
                "signal": "neutral",
                "confidence": 0.5,
                "price": 0
            }
        
        # Calculate indicators
        trend = self.calculate_trend(data)
        momentum = self.calculate_momentum(data)
        volatility = self.calculate_volatility(data)
        key_levels = self.identify_key_levels(data)
        
        # Current price
        current_price = data[-1]["close"]
        
        # Determine signal based on indicators
        trend_direction = trend["direction"]
        momentum_direction = momentum["direction"]
        
        # Combined signal direction (-1 to 1)
        combined_direction = (trend_direction * 0.6) + (momentum_direction * 0.4)
        
        # Determine signal type
        if combined_direction > 0.5:
            signal = "buy"
        elif combined_direction < -0.5:
            signal = "sell"
        else:
            signal = "neutral"
        
        # Calculate confidence (0.5 to 1.0)
        confidence = 0.5 + min(0.5, abs(combined_direction) * 0.5)
        
        # Adjust confidence based on volatility
        if volatility["volatility"] == "high":
            confidence = max(0.5, confidence * 0.9)  # Reduce confidence in high volatility
        
        return {
            "timeframe": timeframe,
            "signal": signal,
            "confidence": confidence,
            "price": current_price,
            "indicators": {
                "trend": trend,
                "momentum": momentum,
                "volatility": volatility,
                "key_levels": key_levels
            }
        }
    
    def generate_consensus_signal(self, signals: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate consensus signal from multiple timeframe signals.
        
        Args:
            signals: Dictionary of signals for different timeframes
            
        Returns:
            Dictionary with consensus signal information
        """
        if not signals:
            return {
                "signal": "neutral",
                "confidence": 0.5,
                "timeframes": {}
            }
        
        # Count signals by type
        signal_counts = {"buy": 0, "sell": 0, "neutral": 0}
        signal_confidence = {"buy": 0, "sell": 0, "neutral": 0}
        
        # Assign weights to different timeframes
        timeframe_weights = {
            "5m": 0.05,
            "15m": 0.1,
            "1h": 0.15,
            "4h": 0.25,
            "1d": 0.3,
            "1w": 0.15
        }
        
        # Calculate weighted signals
        for tf, signal in signals.items():
            weight = timeframe_weights.get(tf, 0.1)
            signal_type = signal["signal"]
            signal_counts[signal_type] += weight
            signal_confidence[signal_type] += signal["confidence"] * weight
        
        # Determine consensus signal
        if signal_counts["buy"] > signal_counts["sell"] and signal_counts["buy"] > signal_counts["neutral"]:
            consensus = "buy"
            confidence = signal_confidence["buy"] / max(0.01, signal_counts["buy"])
        elif signal_counts["sell"] > signal_counts["buy"] and signal_counts["sell"] > signal_counts["neutral"]:
            consensus = "sell"
            confidence = signal_confidence["sell"] / max(0.01, signal_counts["sell"])
        else:
            consensus = "neutral"
            confidence = signal_confidence["neutral"] / max(0.01, signal_counts["neutral"])
        
        # Calculate strength of consensus (how many timeframes agree)
        max_count = max(signal_counts.values())
        total_weight = sum(timeframe_weights.values())
        consensus_strength = max_count / total_weight
        
        # Adjust confidence based on consensus strength
        confidence = min(0.95, confidence * consensus_strength)
        
        return {
            "signal": consensus,
            "confidence": confidence,
            "consensus_strength": consensus_strength,
            "timeframes": signals
        }
    
    async def analyze_multiple_timeframes(self, symbol: str, timeframes: List[str] = None) -> Dict[str, Any]:
        """
        Analyze a symbol across multiple timeframes.
        
        Args:
            symbol: Cryptocurrency symbol (e.g., BTC)
            timeframes: List of timeframes to analyze
            
        Returns:
            Dictionary with analysis results
        """
        if timeframes is None:
            timeframes = TIMEFRAMES
        
        # Fetch data for each timeframe
        timeframe_data = {}
        for tf in timeframes:
            data = await self.fetch_historical_data(symbol, tf)
            if data:
                timeframe_data[tf] = data
        
        # Generate signals for each timeframe
        timeframe_signals = {}
        for tf, data in timeframe_data.items():
            signal = self.generate_signal_for_timeframe(data, tf)
            timeframe_signals[tf] = signal
        
        # Generate consensus signal
        consensus = self.generate_consensus_signal(timeframe_signals)
        
        return {
            "symbol": symbol,
            "timestamp": datetime.utcnow().isoformat(),
            "consensus": consensus,
            "timeframes": timeframe_signals
        }

async def main():
    """Main function for testing the multi-timeframe analyzer."""
    analyzer = MultiTimeframeAnalyzer()
    
    # Test with BTC
    result = await analyzer.analyze_multiple_timeframes("BTC")
    
    # Print results
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
