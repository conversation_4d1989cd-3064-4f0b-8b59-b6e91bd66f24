{"name": "@chakra-ui/theme-tools", "version": "2.2.8", "description": "Set of helpers that makes theming and styling easier", "keywords": ["theme", "theming", "color", "utilities"], "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/chakra-ui/chakra-ui#readme", "license": "MIT", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.mjs", "types": "dist/types/index.d.ts", "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/chakra-ui/chakra-ui.git", "directory": "packages/components/theme-tools"}, "bugs": {"url": "https://github.com/chakra-ui/chakra-ui/issues"}, "dependencies": {"color2k": "^2.0.2", "@chakra-ui/anatomy": "2.3.6", "@chakra-ui/utils": "2.2.4"}, "peerDependencies": {"@chakra-ui/styled-system": ">=2.0.0"}, "devDependencies": {"@chakra-ui/styled-system": "2.12.2"}, "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.cjs"}}, "./color": {"import": {"types": "./dist/types/color.d.ts", "default": "./dist/esm/color.mjs"}, "require": {"types": "./dist/types/color.d.ts", "default": "./dist/cjs/color.cjs"}}, "./component": {"import": {"types": "./dist/types/component.d.ts", "default": "./dist/esm/component.mjs"}, "require": {"types": "./dist/types/component.d.ts", "default": "./dist/cjs/component.cjs"}}, "./create-breakpoints": {"import": {"types": "./dist/types/create-breakpoints.d.ts", "default": "./dist/esm/create-breakpoints.mjs"}, "require": {"types": "./dist/types/create-breakpoints.d.ts", "default": "./dist/cjs/create-breakpoints.cjs"}}, "./css-calc": {"import": {"types": "./dist/types/css-calc.d.ts", "default": "./dist/esm/css-calc.mjs"}, "require": {"types": "./dist/types/css-calc.d.ts", "default": "./dist/cjs/css-calc.cjs"}}, "./css-var": {"import": {"types": "./dist/types/css-var.d.ts", "default": "./dist/esm/css-var.mjs"}, "require": {"types": "./dist/types/css-var.d.ts", "default": "./dist/cjs/css-var.cjs"}}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build:fast": "tsup src"}}