"""
Signal Updater for Project Ruby.

This script runs in the background and periodically updates the multi-timeframe signals.
"""

import time
import sys
import os
import subprocess
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("signal_updater.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("SignalUpdater")

# Update interval in seconds (5 minutes)
UPDATE_INTERVAL = 300

def update_signals():
    """Update the multi-timeframe signals."""
    try:
        logger.info("Updating multi-timeframe signals...")
        
        # Run the signal generator script
        result = subprocess.run(
            ["python", "generate_timeframe_signals.py"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            logger.info("Multi-timeframe signals updated successfully!")
        else:
            logger.error(f"Error updating signals: {result.stderr}")
    
    except Exception as e:
        logger.error(f"Error updating signals: {str(e)}")

def main():
    """Main function."""
    logger.info("Starting Signal Updater for Project Ruby...")
    
    # Initial update
    update_signals()
    
    # Main loop
    try:
        while True:
            # Wait for the next update
            logger.info(f"Next update in {UPDATE_INTERVAL} seconds...")
            time.sleep(UPDATE_INTERVAL)
            
            # Update signals
            update_signals()
    
    except KeyboardInterrupt:
        logger.info("Signal Updater stopped by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
