(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[29],{261:function(n,e,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/simple-governance",function(){return i(8793)}])},8793:function(n,e,i){"use strict";i.r(e),i.d(e,{default:function(){return s}});var r=i(5893);function s(){return(0,r.jsxs)("div",{style:{padding:"20px"},children:[(0,r.jsx)("h1",{children:"Simple Governance Page"}),(0,r.jsx)("p",{children:"This is a very simple governance page to test if routing works."}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Uniswap: High participation proposal (80% strength)"}),(0,r.jsx)("li",{children:"Aave: Contentious proposal (65% strength)"}),(0,r.jsx)("li",{children:"Maker: High impact proposal (90% strength)"})]})]})}}},function(n){n.O(0,[888,774,179],function(){return n(n.s=261)}),_N_E=n.O()}]);