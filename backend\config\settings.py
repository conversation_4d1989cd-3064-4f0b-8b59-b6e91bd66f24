"""
Centralized Configuration Management for MCP Trading Platform

This module provides centralized configuration management for all platform components.
"""

import os
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from enum import Enum

class Environment(str, Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    url: str = Field(default="sqlite:///./trading_platform.db", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")

class APISettings(BaseSettings):
    """API configuration settings."""
    # External API Keys
    coingecko_api_key: Optional[str] = Field(default=None, env="COINGECKO_API_KEY")
    cryptocompare_api_key: Optional[str] = Field(default=None, env="CRYPTOCOMPARE_API_KEY")
    coinmarketcap_api_key: Optional[str] = Field(default=None, env="COINMARKETCAP_API_KEY")
    binance_api_key: Optional[str] = Field(default=None, env="BINANCE_API_KEY")
    binance_secret_key: Optional[str] = Field(default=None, env="BINANCE_SECRET_KEY")
    alpha_vantage_api_key: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    newsapi_key: Optional[str] = Field(default=None, env="NEWSAPI_KEY")
    
    # Rate limiting
    rate_limit_per_minute: int = Field(default=60, env="API_RATE_LIMIT_PER_MINUTE")
    rate_limit_burst: int = Field(default=10, env="API_RATE_LIMIT_BURST")

class ServerSettings(BaseSettings):
    """Server configuration settings."""
    host: str = Field(default="0.0.0.0", env="SERVER_HOST")
    port: int = Field(default=8004, env="SERVER_PORT")
    workers: int = Field(default=1, env="SERVER_WORKERS")
    reload: bool = Field(default=False, env="SERVER_RELOAD")
    
    # CORS settings
    cors_origins: list = Field(default=["http://localhost:3000", "http://localhost:8085"], env="CORS_ORIGINS")
    cors_methods: list = Field(default=["GET", "POST", "PUT", "DELETE"], env="CORS_METHODS")

class SecuritySettings(BaseSettings):
    """Security configuration settings."""
    secret_key: str = Field(default="development_secret_key_change_in_production", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Password hashing
    password_hash_rounds: int = Field(default=12, env="PASSWORD_HASH_ROUNDS")

class CacheSettings(BaseSettings):
    """Cache configuration settings."""
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    cache_ttl_seconds: int = Field(default=300, env="CACHE_TTL_SECONDS")  # 5 minutes
    price_cache_ttl: int = Field(default=60, env="PRICE_CACHE_TTL")  # 1 minute
    signal_cache_ttl: int = Field(default=300, env="SIGNAL_CACHE_TTL")  # 5 minutes

class MLSettings(BaseSettings):
    """Machine Learning configuration settings."""
    model_update_interval: int = Field(default=3600, env="ML_MODEL_UPDATE_INTERVAL")  # 1 hour
    prediction_horizon_days: int = Field(default=7, env="ML_PREDICTION_HORIZON_DAYS")
    confidence_threshold: float = Field(default=0.6, env="ML_CONFIDENCE_THRESHOLD")
    
    # Model paths
    models_directory: str = Field(default="./models", env="ML_MODELS_DIRECTORY")
    lstm_model_path: str = Field(default="./models/lstm_model.pkl", env="LSTM_MODEL_PATH")

class LoggingSettings(BaseSettings):
    """Logging configuration settings."""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    file_path: Optional[str] = Field(default=None, env="LOG_FILE_PATH")
    max_file_size: int = Field(default=10485760, env="LOG_MAX_FILE_SIZE")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")

class Settings(BaseSettings):
    """Main application settings."""
    
    # Environment
    environment: Environment = Field(default=Environment.DEVELOPMENT, env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # Component settings
    database: DatabaseSettings = DatabaseSettings()
    api: APISettings = APISettings()
    server: ServerSettings = ServerSettings()
    security: SecuritySettings = SecuritySettings()
    cache: CacheSettings = CacheSettings()
    ml: MLSettings = MLSettings()
    logging: LoggingSettings = LoggingSettings()
    
    # Data collection settings
    data_collection_interval: int = Field(default=60, env="DATA_COLLECTION_INTERVAL")  # seconds
    signal_generation_interval: int = Field(default=300, env="SIGNAL_GENERATION_INTERVAL")  # 5 minutes
    
    # Supported assets
    supported_cryptocurrencies: list = Field(
        default=["BTC", "ETH", "SOL", "ADA", "DOT", "LINK", "UNI", "AAVE"],
        env="SUPPORTED_CRYPTOCURRENCIES"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings

def update_settings(**kwargs) -> None:
    """Update settings dynamically."""
    global settings
    for key, value in kwargs.items():
        if hasattr(settings, key):
            setattr(settings, key, value)

def get_api_key(service: str) -> Optional[str]:
    """Get API key for a specific service."""
    api_key_mapping = {
        "coingecko": settings.api.coingecko_api_key,
        "cryptocompare": settings.api.cryptocompare_api_key,
        "coinmarketcap": settings.api.coinmarketcap_api_key,
        "binance": settings.api.binance_api_key,
        "alpha_vantage": settings.api.alpha_vantage_api_key,
        "newsapi": settings.api.newsapi_key,
    }
    return api_key_mapping.get(service.lower())

def is_production() -> bool:
    """Check if running in production environment."""
    return settings.environment == Environment.PRODUCTION

def is_development() -> bool:
    """Check if running in development environment."""
    return settings.environment == Environment.DEVELOPMENT
