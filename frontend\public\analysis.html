<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Analysis | Project Ruby</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/ruby-core.css">
    <style>
        .analysis-tabs {
            display: flex;
            background: var(--ruby-bg-light);
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .analysis-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: var(--ruby-text-secondary);
            font-weight: 500;
        }
        
        .analysis-tab.active {
            background: var(--ruby-gold);
            color: var(--ruby-bg-dark);
            font-weight: 600;
        }
        
        .analysis-tab:hover:not(.active) {
            background: var(--ruby-bg-dark);
            color: var(--ruby-text-primary);
        }
        
        .analysis-content {
            display: none;
        }
        
        .analysis-content.active {
            display: block;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .analysis-card {
            background: var(--ruby-bg-dark);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-header i {
            color: var(--ruby-gold);
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .card-title {
            color: var(--ruby-text-primary);
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .signal-list {
            space-y: 10px;
        }
        
        .signal-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--ruby-border);
        }
        
        .signal-row:last-child {
            border-bottom: none;
        }
        
        .signal-info {
            display: flex;
            align-items: center;
        }
        
        .signal-symbol {
            font-weight: bold;
            color: var(--ruby-text-primary);
            margin-right: 10px;
        }
        
        .signal-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .badge-buy { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .badge-sell { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .badge-hold { background: rgba(156, 163, 175, 0.2); color: #9ca3af; }
        .badge-bullish { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .badge-bearish { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .badge-neutral { background: rgba(156, 163, 175, 0.2); color: #9ca3af; }
        
        .signal-strength {
            font-weight: 600;
            color: var(--ruby-gold);
        }
        
        .consensus-item {
            background: var(--ruby-bg-light);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid var(--ruby-border);
        }
        
        .consensus-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .consensus-symbol {
            font-weight: bold;
            color: var(--ruby-text-primary);
        }
        
        .consensus-direction {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .consensus-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .consensus-metric {
            text-align: center;
        }
        
        .metric-label {
            font-size: 0.8em;
            color: var(--ruby-text-secondary);
        }
        
        .metric-value {
            font-weight: bold;
            color: var(--ruby-gold);
        }
        
        .sentiment-overview {
            background: var(--ruby-bg-light);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .sentiment-score {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .score-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--ruby-gold);
        }
        
        .score-label {
            color: var(--ruby-text-secondary);
            margin-top: 5px;
        }
        
        .sentiment-breakdown {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .sentiment-item {
            text-align: center;
        }
        
        .sentiment-percentage {
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .sentiment-positive { color: #22c55e; }
        .sentiment-negative { color: #ef4444; }
        .sentiment-neutral { color: #9ca3af; }
    </style>
</head>
<body class="ruby-theme">
    <div class="container">
        <header>
            <div>
                <div style="margin-bottom: 15px;">
                    <img src="images/project-ruby-text-logo.svg" alt="Project Ruby" style="height: 50px;" />
                </div>
                <h1>Market Analysis</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading.html">Trading</a>
                    <a href="analysis.html" style="font-weight: bold;">Analysis</a>
                    <a href="charts.html">Charts</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="assistant.html">Assistant</a>
                    <a href="portfolio.html">Portfolio</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>
        </header>

        <!-- Analysis Tabs -->
        <div class="analysis-tabs">
            <div class="analysis-tab active" data-tab="signals">Trading Signals</div>
            <div class="analysis-tab" data-tab="sentiment">Market Sentiment</div>
            <div class="analysis-tab" data-tab="consensus">AI Consensus</div>
            <div class="analysis-tab" data-tab="timeframes">Multi-Timeframe</div>
        </div>

        <!-- Trading Signals Content -->
        <div class="analysis-content active" id="signals-content">
            <div class="analysis-grid">
                <div class="analysis-card">
                    <div class="card-header">
                        <i class="fas fa-chart-line"></i>
                        <h2 class="card-title">Active Trading Signals</h2>
                    </div>
                    <div class="signal-list" id="trading-signals-list">
                        <div class="signal-row">
                            <div class="signal-info">
                                <span class="signal-symbol">BTC</span>
                                <span class="signal-badge badge-buy">BUY</span>
                            </div>
                            <span class="signal-strength">85%</span>
                        </div>
                        <div class="signal-row">
                            <div class="signal-info">
                                <span class="signal-symbol">ETH</span>
                                <span class="signal-badge badge-buy">BUY</span>
                            </div>
                            <span class="signal-strength">72%</span>
                        </div>
                        <div class="signal-row">
                            <div class="signal-info">
                                <span class="signal-symbol">SOL</span>
                                <span class="signal-badge badge-hold">HOLD</span>
                            </div>
                            <span class="signal-strength">58%</span>
                        </div>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="card-header">
                        <i class="fas fa-clock"></i>
                        <h2 class="card-title">Recent Signal Performance</h2>
                    </div>
                    <div id="signal-performance">
                        <p>Loading performance data...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Sentiment Content -->
        <div class="analysis-content" id="sentiment-content">
            <div class="sentiment-overview">
                <div class="sentiment-score">
                    <div class="score-value" id="overall-sentiment">74</div>
                    <div class="score-label">Overall Market Sentiment</div>
                </div>
                <div class="sentiment-breakdown">
                    <div class="sentiment-item">
                        <div class="sentiment-percentage sentiment-positive">68%</div>
                        <div>Positive</div>
                    </div>
                    <div class="sentiment-item">
                        <div class="sentiment-percentage sentiment-neutral">22%</div>
                        <div>Neutral</div>
                    </div>
                    <div class="sentiment-item">
                        <div class="sentiment-percentage sentiment-negative">10%</div>
                        <div>Negative</div>
                    </div>
                </div>
            </div>

            <div class="analysis-grid">
                <div class="analysis-card">
                    <div class="card-header">
                        <i class="fas fa-comments"></i>
                        <h2 class="card-title">Asset Sentiment</h2>
                    </div>
                    <div class="signal-list" id="sentiment-signals-list">
                        <div class="signal-row">
                            <div class="signal-info">
                                <span class="signal-symbol">BTC</span>
                                <span class="signal-badge badge-bullish">Bullish</span>
                            </div>
                            <span class="signal-strength">78%</span>
                        </div>
                        <div class="signal-row">
                            <div class="signal-info">
                                <span class="signal-symbol">ETH</span>
                                <span class="signal-badge badge-bullish">Bullish</span>
                            </div>
                            <span class="signal-strength">82%</span>
                        </div>
                        <div class="signal-row">
                            <div class="signal-info">
                                <span class="signal-symbol">SOL</span>
                                <span class="signal-badge badge-neutral">Neutral</span>
                            </div>
                            <span class="signal-strength">55%</span>
                        </div>
                    </div>
                </div>

                <div class="analysis-card">
                    <div class="card-header">
                        <i class="fas fa-chart-bar"></i>
                        <h2 class="card-title">Sentiment Sources</h2>
                    </div>
                    <div id="sentiment-sources">
                        <p>Loading sentiment analysis from multiple sources...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Consensus Content -->
        <div class="analysis-content" id="consensus-content">
            <div class="analysis-card">
                <div class="card-header">
                    <i class="fas fa-brain"></i>
                    <h2 class="card-title">AI Model Consensus</h2>
                </div>
                <div id="consensus-predictions">
                    <div class="consensus-item">
                        <div class="consensus-header">
                            <span class="consensus-symbol">BTC</span>
                            <span class="signal-badge badge-buy">Bullish Consensus</span>
                        </div>
                        <div class="consensus-metrics">
                            <div class="consensus-metric">
                                <div class="metric-label">Agreement</div>
                                <div class="metric-value">85%</div>
                            </div>
                            <div class="consensus-metric">
                                <div class="metric-label">Confidence</div>
                                <div class="metric-value">78%</div>
                            </div>
                            <div class="consensus-metric">
                                <div class="metric-label">Models</div>
                                <div class="metric-value">6/7</div>
                            </div>
                        </div>
                    </div>
                    <div class="consensus-item">
                        <div class="consensus-header">
                            <span class="consensus-symbol">ETH</span>
                            <span class="signal-badge badge-buy">Bullish Consensus</span>
                        </div>
                        <div class="consensus-metrics">
                            <div class="consensus-metric">
                                <div class="metric-label">Agreement</div>
                                <div class="metric-value">72%</div>
                            </div>
                            <div class="consensus-metric">
                                <div class="metric-label">Confidence</div>
                                <div class="metric-value">69%</div>
                            </div>
                            <div class="consensus-metric">
                                <div class="metric-label">Models</div>
                                <div class="metric-value">5/7</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Multi-Timeframe Content -->
        <div class="analysis-content" id="timeframes-content">
            <div class="analysis-card">
                <div class="card-header">
                    <i class="fas fa-layer-group"></i>
                    <h2 class="card-title">Multi-Timeframe Analysis</h2>
                </div>
                <div id="timeframe-analysis">
                    <p>Loading multi-timeframe analysis...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-service.js"></script>
    <script src="js/ruby-common.js"></script>
    <script>
        // Tab switching functionality
        document.querySelectorAll('.analysis-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.analysis-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.analysis-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab + '-content').classList.add('active');
                
                // Load content for the selected tab
                loadTabContent(tab.dataset.tab);
            });
        });

        function loadTabContent(tabName) {
            console.log('Loading content for tab:', tabName);
            // This will be implemented to load real data based on the tab
        }

        // Initialize with signals tab
        document.addEventListener('DOMContentLoaded', function() {
            loadTabContent('signals');
        });
    </script>
</body>
</html>
