"""
Machine Learning Price Predictor

This module provides a simple machine learning model for price prediction.
"""

import logging
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class MLPricePredictor:
    """
    Machine learning model for price prediction.
    
    This class implements a simple machine learning model for predicting
    cryptocurrency prices based on various features.
    """
    
    def __init__(self):
        """Initialize the price predictor."""
        logger.info("ML price predictor initialized")
    
    def predict_price(
        self,
        symbol: str,
        current_price: float,
        derivatives_data: Dict[str, Any],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Any],
        news_sentiment: Dict[str, Any],
        time_horizon: str = "24h"
    ) -> <PERSON>ple[float, float]:
        """
        Predict the price for a specific symbol.
        
        Args:
            symbol: Cryptocurrency symbol
            current_price: Current price
            derivatives_data: Derivatives market data
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics
            news_sentiment: News sentiment analysis
            time_horizon: Time horizon for prediction (24h, 7d, 30d)
            
        Returns:
            Tuple of (predicted_price, confidence)
        """
        # Extract features
        features = self._extract_features(
            symbol=symbol,
            current_price=current_price,
            derivatives_data=derivatives_data,
            economic_trends=economic_trends,
            onchain_metrics=onchain_metrics,
            news_sentiment=news_sentiment
        )
        
        # Calculate price change percentage based on features
        price_change_pct = self._calculate_price_change(features, time_horizon)
        
        # Calculate predicted price
        predicted_price = current_price * (1 + price_change_pct / 100)
        
        # Calculate confidence (0.5 to 0.9)
        confidence = self._calculate_confidence(features)
        
        logger.info(f"Predicted {time_horizon} price for {symbol}: ${predicted_price:.2f} (confidence: {confidence:.2f})")
        return predicted_price, confidence
    
    def _extract_features(
        self,
        symbol: str,
        current_price: float,
        derivatives_data: Dict[str, Any],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Any],
        news_sentiment: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        Extract features for the prediction model.
        
        Args:
            symbol: Cryptocurrency symbol
            current_price: Current price
            derivatives_data: Derivatives market data
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics
            news_sentiment: News sentiment analysis
            
        Returns:
            Dictionary of features
        """
        features = {}
        
        # Derivatives features
        features["funding_rate"] = derivatives_data.get("funding_rate", 0.0)
        
        # Economic features
        features["economic_score"] = self._economic_score(economic_trends)
        
        # On-chain features
        market_cap = onchain_metrics.get("market_cap_usd", 0.0)
        volume = onchain_metrics.get("total_volume_usd", 0.0)
        
        features["market_cap"] = market_cap
        features["volume"] = volume
        features["mcap_to_volume"] = market_cap / volume if volume > 0 else 0
        
        # Sentiment features
        features["news_sentiment"] = self._sentiment_score(news_sentiment.get("overall_sentiment", "neutral"))
        
        return features
    
    def _economic_score(self, economic_trends: Dict[str, str]) -> float:
        """
        Calculate economic score based on economic trends.
        
        Args:
            economic_trends: Economic trend analysis
            
        Returns:
            Economic score (-1.0 to 1.0)
        """
        score = 0.0
        
        # Count positive and negative trends
        positive_count = sum(1 for trend in economic_trends.values() if trend == "positive")
        negative_count = sum(1 for trend in economic_trends.values() if trend == "negative")
        
        # Calculate score
        if positive_count > negative_count:
            score = min(positive_count * 0.2, 1.0)
        elif negative_count > positive_count:
            score = -min(negative_count * 0.2, 1.0)
        
        return score
    
    def _sentiment_score(self, sentiment: str) -> float:
        """
        Convert sentiment to score.
        
        Args:
            sentiment: Sentiment (bullish, bearish, neutral)
            
        Returns:
            Sentiment score (-1.0 to 1.0)
        """
        if sentiment == "bullish":
            return 1.0
        elif sentiment == "slightly_bullish":
            return 0.5
        elif sentiment == "bearish":
            return -1.0
        elif sentiment == "slightly_bearish":
            return -0.5
        else:
            return 0.0
    
    def _calculate_price_change(self, features: Dict[str, float], time_horizon: str) -> float:
        """
        Calculate price change percentage based on features.
        
        Args:
            features: Dictionary of features
            time_horizon: Time horizon for prediction (24h, 7d, 30d)
            
        Returns:
            Price change percentage
        """
        # Base volatility by time horizon
        base_volatility = {
            "24h": 3.0,   # 3% daily volatility
            "7d": 10.0,   # 10% weekly volatility
            "30d": 25.0   # 25% monthly volatility
        }.get(time_horizon, 3.0)
        
        # Calculate weighted score
        weighted_score = (
            -5.0 * features["funding_rate"] +  # Negative funding rate is bullish
            2.0 * features["economic_score"] +  # Economic score
            0.5 * features["news_sentiment"]    # News sentiment
        )
        
        # Add some randomness to simulate real-world uncertainty
        np.random.seed(int(datetime.now().timestamp()))
        random_factor = np.random.normal(0, 1.0)
        
        # Calculate price change percentage
        price_change_pct = weighted_score * base_volatility + random_factor
        
        return price_change_pct
    
    def _calculate_confidence(self, features: Dict[str, float]) -> float:
        """
        Calculate confidence in the prediction.
        
        Args:
            features: Dictionary of features
            
        Returns:
            Confidence (0.5 to 0.9)
        """
        # Base confidence
        confidence = 0.5
        
        # Adjust confidence based on features
        if abs(features["funding_rate"]) > 0.001:
            confidence += 0.1  # Strong funding rate signal
        
        if abs(features["economic_score"]) > 0.5:
            confidence += 0.05  # Strong economic signal
        
        if abs(features["news_sentiment"]) > 0.5:
            confidence += 0.05  # Strong sentiment signal
        
        # Cap confidence at 0.9
        return min(confidence, 0.9)
