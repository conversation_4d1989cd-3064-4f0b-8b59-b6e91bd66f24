"""
On-Chain Signal Generator

This module integrates on-chain metrics with technical analysis to generate enhanced trading signals.
It combines insights from on-chain data with traditional technical indicators.
"""

import os
import json
import logging
from datetime import datetime
import pandas as pd
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('onchain_signals')

# Data paths
ANALYSIS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'analysis')
SIGNALS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'signals')
os.makedirs(SIGNALS_DIR, exist_ok=True)

class OnChainSignalGenerator:
    """Generates trading signals enhanced with on-chain metrics."""
    
    def __init__(self):
        self.onchain_insights = {}
        self.technical_signals = {}
        self.combined_signals = {}
    
    def load_onchain_insights(self):
        """Load the latest on-chain insights."""
        insights_file = os.path.join(ANALYSIS_DIR, "onchain_insights_latest.json")
        if os.path.exists(insights_file):
            try:
                with open(insights_file, 'r') as f:
                    self.onchain_insights = json.load(f)
                logger.info(f"Loaded on-chain insights from {insights_file}")
            except Exception as e:
                logger.error(f"Error loading on-chain insights: {str(e)}")
        else:
            logger.warning(f"No on-chain insights file found at {insights_file}")
    
    def load_technical_signals(self):
        """
        Load technical analysis signals.
        In a real implementation, this would load actual technical signals from your system.
        """
        # For demonstration, we'll create synthetic technical signals
        coins = ["BTC", "ETH", "SOL"]
        timeframes = ["1h", "2h", "4h", "8h"]
        
        for coin in coins:
            self.technical_signals[coin] = {}
            
            for timeframe in timeframes:
                # Generate random signal (in reality, this would be based on technical analysis)
                signal_strength = np.random.random()
                
                if signal_strength > 0.6:
                    direction = "LONG"
                elif signal_strength < 0.4:
                    direction = "SHORT"
                else:
                    direction = "NEUTRAL"
                
                # Create technical signal
                self.technical_signals[coin][timeframe] = {
                    "direction": direction,
                    "strength": signal_strength,
                    "indicators": {
                        "rsi": np.random.randint(30, 70),
                        "macd": "bullish" if direction == "LONG" else "bearish",
                        "ma_cross": "golden" if direction == "LONG" else "death" if direction == "SHORT" else "none"
                    }
                }
        
        logger.info("Generated synthetic technical signals")
    
    def generate_combined_signals(self):
        """
        Combine on-chain insights with technical signals to generate enhanced trading signals.
        """
        for coin in self.technical_signals.keys():
            self.combined_signals[coin] = {}
            
            # Get on-chain insight for this coin
            onchain_insight = self.onchain_insights.get(coin, {})
            onchain_direction = onchain_insight.get("signal_direction", "NEUTRAL")
            onchain_strength = onchain_insight.get("signal_strength", 0.5)
            onchain_timeframes = onchain_insight.get("timeframes", [])
            
            for timeframe in self.technical_signals[coin].keys():
                # Get technical signal for this timeframe
                tech_signal = self.technical_signals[coin][timeframe]
                tech_direction = tech_signal.get("direction", "NEUTRAL")
                tech_strength = tech_signal.get("strength", 0.5)
                
                # Determine if on-chain signal is relevant for this timeframe
                onchain_relevant = timeframe in onchain_timeframes
                
                # Calculate weights for combining signals
                # Give more weight to on-chain data for longer timeframes
                if timeframe in ["4h", "8h"]:
                    onchain_weight = 0.4 if onchain_relevant else 0.2
                else:
                    onchain_weight = 0.3 if onchain_relevant else 0.1
                
                tech_weight = 1 - onchain_weight
                
                # Combine signals
                combined_signal = self._combine_signals(
                    tech_direction, tech_strength, tech_weight,
                    onchain_direction, onchain_strength, onchain_weight
                )
                
                # Add indicators and reasoning
                combined_signal["indicators"] = {
                    **tech_signal.get("indicators", {}),
                    "onchain": {
                        "network_health": onchain_insight.get("network_health", {}).get("status", "neutral"),
                        "active_addresses_trend": onchain_insight.get("reasoning", [])[0] if onchain_insight.get("reasoning") else "",
                        "transaction_count_trend": onchain_insight.get("reasoning", [])[1] if onchain_insight.get("reasoning") else ""
                    }
                }
                
                # Add reasoning
                combined_signal["reasoning"] = self._generate_reasoning(
                    coin, timeframe, tech_signal, onchain_insight, combined_signal
                )
                
                # Add entry/exit points
                combined_signal["trade_levels"] = self._generate_trade_levels(
                    coin, combined_signal["direction"], combined_signal["strength"]
                )
                
                # Store combined signal
                self.combined_signals[coin][timeframe] = combined_signal
        
        logger.info("Generated combined signals with on-chain insights")
    
    def _combine_signals(self, tech_dir, tech_str, tech_weight, onchain_dir, onchain_str, onchain_weight):
        """
        Combine technical and on-chain signals.
        
        Returns:
            dict: Combined signal with direction, strength, and confidence
        """
        # Convert directions to numeric values
        dir_values = {"LONG": 1, "NEUTRAL": 0, "SHORT": -1}
        tech_val = dir_values.get(tech_dir, 0) * tech_str
        onchain_val = dir_values.get(onchain_dir, 0) * onchain_str
        
        # Calculate weighted average
        combined_val = (tech_val * tech_weight) + (onchain_val * onchain_weight)
        
        # Determine combined direction
        if combined_val > 0.2:
            direction = "LONG"
        elif combined_val < -0.2:
            direction = "SHORT"
        else:
            direction = "NEUTRAL"
        
        # Calculate strength and confidence
        strength = abs(combined_val)
        
        # Higher confidence if technical and on-chain signals agree
        if tech_dir == onchain_dir:
            confidence = 0.7 + (strength * 0.3)
        else:
            confidence = 0.5 + (strength * 0.3)
        
        return {
            "direction": direction,
            "strength": min(0.95, strength),
            "confidence": min(0.95, confidence),
            "tech_contribution": tech_weight,
            "onchain_contribution": onchain_weight
        }
    
    def _generate_reasoning(self, coin, timeframe, tech_signal, onchain_insight, combined_signal):
        """Generate reasoning for the combined signal."""
        reasoning = []
        
        # Add technical analysis reasoning
        if tech_signal.get("direction") == "LONG":
            reasoning.append(f"Technical analysis shows bullish signals on the {timeframe} timeframe")
        elif tech_signal.get("direction") == "SHORT":
            reasoning.append(f"Technical analysis shows bearish signals on the {timeframe} timeframe")
        else:
            reasoning.append(f"Technical analysis shows neutral signals on the {timeframe} timeframe")
        
        # Add indicator details
        indicators = tech_signal.get("indicators", {})
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            if rsi > 70:
                reasoning.append(f"RSI is overbought at {rsi}")
            elif rsi < 30:
                reasoning.append(f"RSI is oversold at {rsi}")
            else:
                reasoning.append(f"RSI is neutral at {rsi}")
        
        if "macd" in indicators:
            reasoning.append(f"MACD shows {indicators['macd']} momentum")
        
        if "ma_cross" in indicators and indicators["ma_cross"] != "none":
            reasoning.append(f"Moving averages show a {indicators['ma_cross']} cross")
        
        # Add on-chain reasoning
        if onchain_insight:
            for reason in onchain_insight.get("reasoning", []):
                reasoning.append(f"On-chain data: {reason}")
            
            # Add recommendation
            if onchain_insight.get("recommendation"):
                reasoning.append(f"On-chain recommendation: {onchain_insight['recommendation']}")
        
        # Add signal strength context
        strength = combined_signal.get("strength", 0)
        if strength > 0.8:
            reasoning.append(f"This is a very strong {combined_signal.get('direction', 'NEUTRAL').lower()} signal")
        elif strength > 0.6:
            reasoning.append(f"This is a strong {combined_signal.get('direction', 'NEUTRAL').lower()} signal")
        elif strength > 0.4:
            reasoning.append(f"This is a moderate {combined_signal.get('direction', 'NEUTRAL').lower()} signal")
        else:
            reasoning.append(f"This is a weak {combined_signal.get('direction', 'NEUTRAL').lower()} signal")
        
        return reasoning
    
    def _generate_trade_levels(self, coin, direction, strength):
        """
        Generate entry, take profit, and stop loss levels.
        In a real implementation, this would use actual price data.
        """
        # For demonstration, we'll use placeholder prices
        prices = {
            "BTC": 60245.00,
            "ETH": 3045.00,
            "SOL": 145.20
        }
        
        current_price = prices.get(coin, 1000)
        
        # Calculate levels based on direction and strength
        if direction == "LONG":
            # Entry slightly below current price
            entry_low = current_price * 0.995
            entry_high = current_price * 1.005
            
            # Take profit based on strength
            take_profit = current_price * (1 + (0.02 + strength * 0.08))
            
            # Stop loss based on strength (tighter for stronger signals)
            stop_loss = current_price * (1 - (0.01 + (1 - strength) * 0.04))
            
        elif direction == "SHORT":
            # Entry slightly above current price
            entry_low = current_price * 0.995
            entry_high = current_price * 1.005
            
            # Take profit based on strength
            take_profit = current_price * (1 - (0.02 + strength * 0.08))
            
            # Stop loss based on strength (tighter for stronger signals)
            stop_loss = current_price * (1 + (0.01 + (1 - strength) * 0.04))
            
        else:
            # Neutral signal - no trade levels
            return {
                "entry": "No clear entry",
                "take_profit": "No clear target",
                "stop_loss": "No clear stop loss"
            }
        
        return {
            "current_price": current_price,
            "entry": f"${entry_low:.2f} - ${entry_high:.2f}",
            "take_profit": f"${take_profit:.2f}",
            "stop_loss": f"${stop_loss:.2f}"
        }
    
    def save_signals(self):
        """Save combined signals to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(SIGNALS_DIR, f"combined_signals_{timestamp}.json")
        
        with open(filename, 'w') as f:
            json.dump(self.combined_signals, f, indent=2)
        
        # Also save to latest file
        latest_file = os.path.join(SIGNALS_DIR, "combined_signals_latest.json")
        with open(latest_file, 'w') as f:
            json.dump(self.combined_signals, f, indent=2)
        
        logger.info(f"Saved combined signals to {filename}")
    
    def get_signals_for_ui(self):
        """
        Format signals for display in the UI.
        
        Returns:
            dict: Signals formatted for the UI
        """
        ui_signals = {}
        
        for coin, timeframes in self.combined_signals.items():
            ui_signals[coin] = {}
            
            for timeframe, signal in timeframes.items():
                # Format signal for UI
                ui_signals[coin][timeframe] = {
                    "direction": signal.get("direction", "NEUTRAL"),
                    "strength": f"{int(signal.get('strength', 0) * 100)}%",
                    "confidence": f"{int(signal.get('confidence', 0) * 100)}%",
                    "indicators": {
                        "technical": {
                            "rsi": signal.get("indicators", {}).get("rsi", "N/A"),
                            "macd": signal.get("indicators", {}).get("macd", "N/A"),
                            "ma_cross": signal.get("indicators", {}).get("ma_cross", "N/A")
                        },
                        "onchain": signal.get("indicators", {}).get("onchain", {})
                    },
                    "reasoning": signal.get("reasoning", []),
                    "trade_levels": signal.get("trade_levels", {})
                }
        
        return ui_signals

def main():
    """Main function to generate combined signals with on-chain insights."""
    generator = OnChainSignalGenerator()
    
    # Load on-chain insights
    generator.load_onchain_insights()
    
    # Load or generate technical signals
    generator.load_technical_signals()
    
    # Generate combined signals
    generator.generate_combined_signals()
    
    # Save signals
    generator.save_signals()
    
    # Get signals formatted for UI
    ui_signals = generator.get_signals_for_ui()
    
    logger.info("Completed generation of combined signals with on-chain insights")
    return ui_signals

if __name__ == "__main__":
    main()
