<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Ruby | Trading Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/ruby-core.css">
    <style>
        /* Consolidated Dashboard Styles */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .dashboard-section {
            background: var(--ruby-bg-dark);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .section-title {
            color: var(--ruby-gold);
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .view-all-link {
            color: var(--ruby-accent);
            text-decoration: none;
            font-size: 0.9em;
        }
        
        .view-all-link:hover {
            text-decoration: underline;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: var(--ruby-bg-light);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--ruby-border);
        }
        
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: var(--ruby-gold);
        }
        
        .stat-label {
            color: var(--ruby-text-secondary);
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .signal-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--ruby-border);
        }
        
        .signal-item:last-child {
            border-bottom: none;
        }
        
        .signal-asset {
            font-weight: 600;
            color: var(--ruby-text-primary);
        }
        
        .signal-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .signal-buy { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
        .signal-sell { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .signal-hold { background: rgba(156, 163, 175, 0.2); color: #9ca3af; }
        
        .signal-strength {
            font-weight: 600;
            color: var(--ruby-gold);
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid var(--ruby-border);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: var(--ruby-text-secondary);
            transition: all 0.3s ease;
        }
        
        .tab.active {
            color: var(--ruby-gold);
            border-bottom-color: var(--ruby-gold);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .price-ticker {
            background: var(--ruby-bg-light);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--ruby-border);
        }
        
        .ticker-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .ticker-item {
            text-align: center;
        }
        
        .ticker-symbol {
            font-weight: bold;
            color: var(--ruby-text-primary);
        }
        
        .ticker-price {
            color: var(--ruby-gold);
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .ticker-change {
            font-size: 0.9em;
        }
        
        .positive { color: #22c55e; }
        .negative { color: #ef4444; }
    </style>
</head>
<body class="ruby-theme">
    <div class="container">
        <header>
            <div>
                <div style="margin-bottom: 15px;">
                    <img src="images/project-ruby-text-logo.svg" alt="Project Ruby" style="height: 50px;" />
                </div>
                <h1>Trading Dashboard</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html" style="font-weight: bold;">Dashboard</a>
                    <a href="trading.html">Trading</a>
                    <a href="analysis.html">Analysis</a>
                    <a href="charts.html">Charts</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="assistant.html">Assistant</a>
                    <a href="portfolio.html">Portfolio</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>
        </header>

        <!-- Live Price Ticker -->
        <div class="price-ticker">
            <div class="ticker-items">
                <div class="ticker-item">
                    <div class="ticker-symbol">BTC</div>
                    <div class="ticker-price" id="btc-price">$97,350</div>
                    <div class="ticker-change positive" id="btc-change">+2.4%</div>
                </div>
                <div class="ticker-item">
                    <div class="ticker-symbol">ETH</div>
                    <div class="ticker-price" id="eth-price">$3,485</div>
                    <div class="ticker-change positive" id="eth-change">+1.8%</div>
                </div>
                <div class="ticker-item">
                    <div class="ticker-symbol">SOL</div>
                    <div class="ticker-price" id="sol-price">$248</div>
                    <div class="ticker-change negative" id="sol-change">-0.5%</div>
                </div>
                <div class="ticker-item">
                    <div class="ticker-symbol">ADA</div>
                    <div class="ticker-price" id="ada-price">$1.12</div>
                    <div class="ticker-change positive" id="ada-change">+3.2%</div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-value" id="active-signals">12</div>
                <div class="stat-label">Active Signals</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="portfolio-value">$25,430</div>
                <div class="stat-label">Portfolio Value</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="daily-pnl">+$1,245</div>
                <div class="stat-label">24h P&L</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="win-rate">68%</div>
                <div class="stat-label">Win Rate</div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="dashboard-grid">
            <!-- Trading Signals Section -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2 class="section-title"><i class="fas fa-chart-line"></i> Trading Signals</h2>
                    <a href="trading.html" class="view-all-link">View All →</a>
                </div>
                <div id="trading-signals-summary">
                    <div class="signal-item">
                        <div>
                            <div class="signal-asset">BTC</div>
                            <div class="signal-type signal-buy">BUY</div>
                        </div>
                        <div class="signal-strength">85%</div>
                    </div>
                    <div class="signal-item">
                        <div>
                            <div class="signal-asset">ETH</div>
                            <div class="signal-type signal-buy">BUY</div>
                        </div>
                        <div class="signal-strength">72%</div>
                    </div>
                    <div class="signal-item">
                        <div>
                            <div class="signal-asset">SOL</div>
                            <div class="signal-type signal-hold">HOLD</div>
                        </div>
                        <div class="signal-strength">58%</div>
                    </div>
                </div>
            </div>

            <!-- Market Sentiment Section -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2 class="section-title"><i class="fas fa-comments"></i> Market Sentiment</h2>
                    <a href="analysis.html#sentiment" class="view-all-link">View All →</a>
                </div>
                <div id="sentiment-summary">
                    <div class="signal-item">
                        <div>
                            <div class="signal-asset">BTC</div>
                            <div class="signal-type signal-buy">Bullish</div>
                        </div>
                        <div class="signal-strength">78%</div>
                    </div>
                    <div class="signal-item">
                        <div>
                            <div class="signal-asset">ETH</div>
                            <div class="signal-type signal-buy">Bullish</div>
                        </div>
                        <div class="signal-strength">82%</div>
                    </div>
                    <div class="signal-item">
                        <div>
                            <div class="signal-asset">Market</div>
                            <div class="signal-type signal-buy">Optimistic</div>
                        </div>
                        <div class="signal-strength">71%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabbed Content Area -->
        <div class="dashboard-section">
            <div class="tabs">
                <div class="tab active" data-tab="predictions">AI Predictions</div>
                <div class="tab" data-tab="consensus">Consensus Models</div>
                <div class="tab" data-tab="timeframes">Multi-Timeframe</div>
            </div>
            
            <div class="tab-content active" id="predictions-content">
                <div id="predictions-summary">
                    <p>Loading AI predictions...</p>
                </div>
            </div>
            
            <div class="tab-content" id="consensus-content">
                <div id="consensus-summary">
                    <p>Loading consensus models...</p>
                </div>
            </div>
            
            <div class="tab-content" id="timeframes-content">
                <div id="timeframes-summary">
                    <p>Loading multi-timeframe analysis...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-service.js"></script>
    <script src="js/ruby-common.js"></script>
    <script>
        // Tab switching functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab + '-content').classList.add('active');
            });
        });

        // Initialize dashboard data
        document.addEventListener('DOMContentLoaded', function() {
            // Load real-time data
            if (typeof DataService !== 'undefined') {
                DataService.authenticate().then(() => {
                    loadDashboardData();
                    setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
                });
            }
        });

        function loadDashboardData() {
            // This will be implemented to load real data
            console.log('Loading dashboard data...');
        }
    </script>
</body>
</html>
