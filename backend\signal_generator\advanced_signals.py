"""
Advanced Signal Generation Module

This module provides sophisticated algorithms for generating trading signals
by combining multiple data sources: derivatives data, economic indicators,
on-chain metrics, and news sentiment.
"""

import logging
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple

# Import ML price predictor
from .ml_predictor import MLPricePredictor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class AdvancedSignalGenerator:
    """
    Advanced signal generator that combines multiple data sources.

    This class implements sophisticated algorithms for generating trading signals
    by analyzing and combining data from various sources:
    - Derivatives market data (funding rates, open interest)
    - Economic indicators (inflation, interest rates)
    - On-chain metrics (market cap, volume, development activity)
    - News sentiment (positive/negative news coverage)
    """

    def __init__(self):
        """Initialize the advanced signal generator."""
        self.ml_predictor = MLPricePredictor()
        logger.info("Advanced signal generator initialized with ML price predictor")

    def generate_signals(
        self,
        derivatives_data: Dict[str, Dict[str, Any]],
        derivatives_sentiment: Dict[str, Dict[str, Any]],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Dict[str, Any]],
        onchain_sentiment: Dict[str, Dict[str, Any]],
        news_sentiment: Dict[str, Dict[str, Any]],
        price_data: Dict[str, float] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate advanced trading signals by combining multiple data sources.

        Args:
            derivatives_data: Derivatives market data by symbol
            derivatives_sentiment: Derivatives sentiment analysis by symbol
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics by symbol
            onchain_sentiment: On-chain sentiment analysis by symbol
            news_sentiment: News sentiment analysis by symbol
            price_data: Current price data by symbol (optional)

        Returns:
            List of trading signals
        """
        signals = []

        # Define the symbols to generate signals for
        symbols = ["BTC", "ETH", "SOL"]

        for symbol in symbols:
            # Generate signal for each symbol
            signal = self._generate_signal_for_symbol(
                symbol=symbol,
                derivatives_data=derivatives_data.get(symbol, {}),
                derivatives_sentiment=derivatives_sentiment.get(symbol, {}),
                economic_trends=economic_trends,
                onchain_metrics=onchain_metrics.get(symbol, {}),
                onchain_sentiment=onchain_sentiment.get(symbol, {}),
                news_sentiment=news_sentiment.get(symbol, {}),
                price=price_data.get(symbol, 0.0) if price_data else None
            )

            signals.append(signal)

        logger.info(f"Generated {len(signals)} advanced trading signals")
        return signals

    def _generate_signal_for_symbol(
        self,
        symbol: str,
        derivatives_data: Dict[str, Any],
        derivatives_sentiment: Dict[str, Any],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Any],
        onchain_sentiment: Dict[str, Any],
        news_sentiment: Dict[str, Any],
        price: float = None
    ) -> Dict[str, Any]:
        """
        Generate a trading signal for a specific symbol.

        Args:
            symbol: Cryptocurrency symbol
            derivatives_data: Derivatives market data
            derivatives_sentiment: Derivatives sentiment analysis
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics
            onchain_sentiment: On-chain sentiment analysis
            news_sentiment: News sentiment analysis
            price: Current price (optional)

        Returns:
            Trading signal
        """
        # Calculate signal type and confidence
        signal_type, confidence, indicators = self._calculate_signal(
            symbol=symbol,
            derivatives_data=derivatives_data,
            derivatives_sentiment=derivatives_sentiment,
            economic_trends=economic_trends,
            onchain_metrics=onchain_metrics,
            onchain_sentiment=onchain_sentiment,
            news_sentiment=news_sentiment
        )

        # Generate price predictions using ML model
        if price is not None:
            # Predict prices for different time horizons
            price_24h, confidence_24h = self.ml_predictor.predict_price(
                symbol=symbol,
                current_price=price,
                derivatives_data=derivatives_data,
                economic_trends=economic_trends,
                onchain_metrics=onchain_metrics,
                news_sentiment=news_sentiment,
                time_horizon="24h"
            )

            price_7d, confidence_7d = self.ml_predictor.predict_price(
                symbol=symbol,
                current_price=price,
                derivatives_data=derivatives_data,
                economic_trends=economic_trends,
                onchain_metrics=onchain_metrics,
                news_sentiment=news_sentiment,
                time_horizon="7d"
            )

            # Add price predictions to indicators
            indicators["predicted_price_24h"] = price_24h
            indicators["prediction_confidence_24h"] = confidence_24h
            indicators["predicted_price_7d"] = price_7d
            indicators["prediction_confidence_7d"] = confidence_7d
            indicators["predicted_change_24h"] = ((price_24h - price) / price) * 100
            indicators["predicted_change_7d"] = ((price_7d - price) / price) * 100

        # Map symbol to asset details
        asset_details = {
            "BTC": {"id": "bitcoin", "name": "Bitcoin"},
            "ETH": {"id": "ethereum", "name": "Ethereum"},
            "SOL": {"id": "solana", "name": "Solana"}
        }

        # Create the signal
        signal = {
            "id": f"signal-{symbol.lower()}-{uuid.uuid4()}",
            "asset": symbol,
            "asset_id": asset_details.get(symbol, {}).get("id", symbol.lower()),
            "asset_symbol": symbol,
            "asset_name": asset_details.get(symbol, {}).get("name", symbol),
            "signal_type": signal_type,
            "time_frame": "1d",
            "confidence": confidence,
            "price": price or derivatives_data.get("mark_price", 0.0),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "expiration": (datetime.now(timezone.utc) + timedelta(days=1)).isoformat(),
            "indicators": indicators,
            "additional_context": self._create_additional_context(
                symbol=symbol,
                derivatives_data=derivatives_data,
                derivatives_sentiment=derivatives_sentiment,
                economic_trends=economic_trends,
                onchain_metrics=onchain_metrics,
                onchain_sentiment=onchain_sentiment,
                news_sentiment=news_sentiment,
                indicators=indicators
            )
        }

        return signal

    def _calculate_signal(
        self,
        symbol: str,
        derivatives_data: Dict[str, Any],
        derivatives_sentiment: Dict[str, Any],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Any],
        onchain_sentiment: Dict[str, Any],
        news_sentiment: Dict[str, Any]
    ) -> Tuple[str, float, Dict[str, Any]]:
        """
        Calculate signal type, confidence, and indicators.

        Args:
            symbol: Cryptocurrency symbol
            derivatives_data: Derivatives market data
            derivatives_sentiment: Derivatives sentiment analysis
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics
            onchain_sentiment: On-chain sentiment analysis
            news_sentiment: News sentiment analysis

        Returns:
            Tuple of (signal_type, confidence, indicators)
        """
        # Initialize scores for different factors
        factor_scores = {
            "derivatives": 0.0,
            "economic": 0.0,
            "onchain": 0.0,
            "news": 0.0
        }

        # Initialize factor weights (can be adjusted based on importance)
        factor_weights = {
            "derivatives": 0.35,  # Highest weight for derivatives data
            "economic": 0.15,     # Lower weight for economic data
            "onchain": 0.25,      # Medium weight for on-chain data
            "news": 0.25          # Medium weight for news sentiment
        }

        # Initialize indicators
        indicators = {}

        # 1. Analyze derivatives data
        funding_rate = derivatives_data.get("funding_rate", 0.0)
        indicators["funding_rate"] = funding_rate

        # Funding rate analysis (negative funding rate is bullish)
        if funding_rate < -0.0005:
            factor_scores["derivatives"] += 1.0
        elif funding_rate < -0.0001:
            factor_scores["derivatives"] += 0.5
        elif funding_rate > 0.0005:
            factor_scores["derivatives"] -= 1.0
        elif funding_rate > 0.0001:
            factor_scores["derivatives"] -= 0.5

        # Derivatives sentiment
        deriv_sentiment = derivatives_sentiment.get("overall_sentiment", "neutral")
        if deriv_sentiment == "bullish":
            factor_scores["derivatives"] += 1.0
        elif deriv_sentiment == "slightly_bullish":
            factor_scores["derivatives"] += 0.5
        elif deriv_sentiment == "bearish":
            factor_scores["derivatives"] -= 1.0
        elif deriv_sentiment == "slightly_bearish":
            factor_scores["derivatives"] -= 0.5

        # 2. Analyze economic trends
        inflation_trend = economic_trends.get("inflation_trend", "neutral")
        interest_rate_trend = economic_trends.get("interest_rate_trend", "neutral")
        overall_economic_trend = economic_trends.get("overall_economic_trend", "neutral")

        indicators["inflation_trend"] = inflation_trend
        indicators["interest_rate_trend"] = interest_rate_trend
        indicators["overall_economic_trend"] = overall_economic_trend

        # Economic trends analysis
        if inflation_trend == "positive":
            factor_scores["economic"] += 0.5
        elif inflation_trend == "negative":
            factor_scores["economic"] -= 0.5

        if interest_rate_trend == "positive":
            factor_scores["economic"] += 0.5
        elif interest_rate_trend == "negative":
            factor_scores["economic"] -= 0.5

        if overall_economic_trend == "positive":
            factor_scores["economic"] += 1.0
        elif overall_economic_trend == "negative":
            factor_scores["economic"] -= 1.0

        # 3. Analyze on-chain metrics
        market_cap = onchain_metrics.get("market_cap_usd", 0.0)
        volume = onchain_metrics.get("total_volume_usd", 0.0)

        # Calculate market cap to volume ratio (if available)
        if market_cap and volume:
            mcap_to_volume = market_cap / volume if volume > 0 else 0
            indicators["mcap_to_volume"] = mcap_to_volume

            # Lower ratio is generally better (more liquid)
            if mcap_to_volume < 10:
                factor_scores["onchain"] += 0.5
            elif mcap_to_volume > 50:
                factor_scores["onchain"] -= 0.5

        # On-chain sentiment
        onchain_sentiment_value = onchain_sentiment.get("overall_sentiment", "neutral")
        if onchain_sentiment_value == "bullish":
            factor_scores["onchain"] += 1.0
        elif onchain_sentiment_value == "slightly_bullish":
            factor_scores["onchain"] += 0.5
        elif onchain_sentiment_value == "bearish":
            factor_scores["onchain"] -= 1.0
        elif onchain_sentiment_value == "slightly_bearish":
            factor_scores["onchain"] -= 0.5

        # Development activity (if available)
        github_commits = onchain_metrics.get("github_commits_4_weeks", 0)
        if github_commits > 100:
            factor_scores["onchain"] += 0.5
        elif github_commits < 10:
            factor_scores["onchain"] -= 0.5

        # 4. Analyze news sentiment
        news_sentiment_value = news_sentiment.get("overall_sentiment", "neutral")
        indicators["news_sentiment"] = news_sentiment_value

        if news_sentiment_value == "bullish":
            factor_scores["news"] += 1.0
        elif news_sentiment_value == "bearish":
            factor_scores["news"] -= 1.0

        # Calculate weighted average score
        total_score = sum(score * factor_weights[factor] for factor, score in factor_scores.items())

        # Determine signal type based on total score
        if total_score > 0.3:
            signal_type = "buy"
        elif total_score < -0.3:
            signal_type = "sell"
        else:
            signal_type = "hold"

        # Calculate confidence (0.5 to 1.0)
        confidence = 0.5 + min(abs(total_score) * 0.5, 0.45)

        # Add factor scores to indicators
        for factor, score in factor_scores.items():
            indicators[f"{factor}_score"] = score

        indicators["total_score"] = total_score

        return signal_type, confidence, indicators

    def _create_additional_context(
        self,
        symbol: str,
        derivatives_data: Dict[str, Any],
        derivatives_sentiment: Dict[str, Any],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Any],
        onchain_sentiment: Dict[str, Any],
        news_sentiment: Dict[str, Any],
        indicators: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create additional context for the signal.

        Args:
            symbol: Cryptocurrency symbol
            derivatives_data: Derivatives market data
            derivatives_sentiment: Derivatives sentiment analysis
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics
            onchain_sentiment: On-chain sentiment analysis
            news_sentiment: News sentiment analysis

        Returns:
            Dictionary with additional context
        """
        context = {
            # Economic indicators
            "inflation_trend": economic_trends.get("inflation_trend", "neutral"),
            "interest_rate_trend": economic_trends.get("interest_rate_trend", "neutral"),
            "unemployment_trend": economic_trends.get("unemployment_trend", "neutral"),
            "overall_economic_trend": economic_trends.get("overall_economic_trend", "neutral"),

            # Market sentiment
            "market_sentiment": onchain_sentiment.get("market_sentiment", "neutral"),
            "social_sentiment": onchain_sentiment.get("social_sentiment", "neutral"),
            "development_sentiment": onchain_sentiment.get("development_sentiment", "neutral"),
            "onchain_sentiment": onchain_sentiment.get("overall_sentiment", "neutral"),
            "onchain_confidence": onchain_sentiment.get("confidence", 0.5),

            # News sentiment
            "news_sentiment": news_sentiment.get("overall_sentiment", "neutral"),
            "news_confidence": news_sentiment.get("confidence", 0.5),
            "positive_news_count": news_sentiment.get("positive_count", 0),
            "negative_news_count": news_sentiment.get("negative_count", 0),

            # Derivatives data
            "derivatives_sentiment": derivatives_sentiment.get("overall_sentiment", "neutral"),
            "derivatives_confidence": derivatives_sentiment.get("confidence", 0.5),
            "funding_rate": derivatives_data.get("funding_rate", 0.0),
            "open_interest": derivatives_data.get("open_interest", 0),

            # On-chain metrics
            "market_cap_usd": onchain_metrics.get("market_cap_usd", 0),
            "total_volume_usd": onchain_metrics.get("total_volume_usd", 0),
            "github_commits_4_weeks": onchain_metrics.get("github_commits_4_weeks", 0),
            "twitter_followers": onchain_metrics.get("twitter_followers", 0),

            # Signal explanation
            "signal_explanation": self._generate_signal_explanation(
                symbol=symbol,
                derivatives_data=derivatives_data,
                derivatives_sentiment=derivatives_sentiment,
                economic_trends=economic_trends,
                onchain_metrics=onchain_metrics,
                onchain_sentiment=onchain_sentiment,
                news_sentiment=news_sentiment,
                indicators=indicators
            )
        }

        # Add price predictions if available
        if indicators is not None:
            if "predicted_price_24h" in indicators:
                context["predicted_price_24h"] = indicators["predicted_price_24h"]
                context["prediction_confidence_24h"] = indicators["prediction_confidence_24h"]
                context["predicted_change_24h"] = indicators["predicted_change_24h"]

            if "predicted_price_7d" in indicators:
                context["predicted_price_7d"] = indicators["predicted_price_7d"]
                context["prediction_confidence_7d"] = indicators["prediction_confidence_7d"]
                context["predicted_change_7d"] = indicators["predicted_change_7d"]

        return context

    def _generate_signal_explanation(
        self,
        symbol: str,
        derivatives_data: Dict[str, Any],
        derivatives_sentiment: Dict[str, Any],
        economic_trends: Dict[str, str],
        onchain_metrics: Dict[str, Any],
        onchain_sentiment: Dict[str, Any],
        news_sentiment: Dict[str, Any],
        indicators: Dict[str, Any] = None
    ) -> str:
        """
        Generate a human-readable explanation for the signal.

        Args:
            symbol: Cryptocurrency symbol
            derivatives_data: Derivatives market data
            derivatives_sentiment: Derivatives sentiment analysis
            economic_trends: Economic trend analysis
            onchain_metrics: On-chain metrics
            onchain_sentiment: On-chain sentiment analysis
            news_sentiment: News sentiment analysis

        Returns:
            Signal explanation
        """
        explanations = []

        # Derivatives explanation
        funding_rate = derivatives_data.get("funding_rate", 0.0)
        if funding_rate < -0.0001:
            explanations.append(f"Negative funding rate ({funding_rate:.4f}) indicates bullish sentiment in derivatives markets")
        elif funding_rate > 0.0001:
            explanations.append(f"Positive funding rate ({funding_rate:.4f}) indicates bearish sentiment in derivatives markets")

        deriv_sentiment = derivatives_sentiment.get("overall_sentiment", "neutral")
        if deriv_sentiment != "neutral":
            explanations.append(f"Derivatives sentiment is {deriv_sentiment}")

        # Economic explanation
        overall_economic = economic_trends.get("overall_economic_trend", "neutral")
        if overall_economic != "neutral":
            explanations.append(f"Overall economic trend is {overall_economic}")

        # On-chain explanation
        onchain_sentiment_value = onchain_sentiment.get("overall_sentiment", "neutral")
        if onchain_sentiment_value != "neutral":
            explanations.append(f"On-chain metrics indicate {onchain_sentiment_value} sentiment")

        # News explanation
        news_sentiment_value = news_sentiment.get("overall_sentiment", "neutral")
        if news_sentiment_value != "neutral":
            explanations.append(f"News sentiment is {news_sentiment_value}")

        # Add price prediction explanation if available
        if "predicted_price_24h" in indicators:
            price_change_24h = indicators["predicted_change_24h"]
            if price_change_24h > 3.0:
                explanations.append(f"ML model predicts a strong upward move of {price_change_24h:.1f}% in 24h")
            elif price_change_24h < -3.0:
                explanations.append(f"ML model predicts a strong downward move of {price_change_24h:.1f}% in 24h")
            elif price_change_24h > 1.0:
                explanations.append(f"ML model predicts a moderate upward move of {price_change_24h:.1f}% in 24h")
            elif price_change_24h < -1.0:
                explanations.append(f"ML model predicts a moderate downward move of {price_change_24h:.1f}% in 24h")

        # If we have explanations, join them
        if explanations:
            return " | ".join(explanations)
        else:
            return "Signal based on combined analysis of multiple data sources"
