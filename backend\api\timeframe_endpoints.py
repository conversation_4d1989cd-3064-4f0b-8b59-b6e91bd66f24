"""
API endpoints for multi-timeframe analysis and signals.
"""

import logging
import json
import os
from typing import Dict, List, Any
from fastapi import APIRouter, HTTPException
import asyncio

from backend.signal_generator.timeframe_signals import TimeframeSignalGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize signal generator
signal_generator = TimeframeSignalGenerator()

# Cache for signals
signals_cache = {}
last_update_time = None

@router.get("/timeframe/signals")
async def get_timeframe_signals():
    """
    Get multi-timeframe trading signals.
    
    Returns:
        Dictionary with signals for each symbol
    """
    try:
        global signals_cache, last_update_time
        
        # Check if we need to refresh the cache
        from datetime import datetime, timedelta
        current_time = datetime.utcnow()
        
        if last_update_time is None or (current_time - last_update_time) > timedelta(minutes=15):
            # Generate new signals
            signals_cache = await signal_generator.generate_signals()
            last_update_time = current_time
            logger.info(f"Generated fresh timeframe signals for {len(signals_cache)} symbols")
        else:
            logger.info(f"Using cached timeframe signals for {len(signals_cache)} symbols")
        
        return signals_cache
    
    except Exception as e:
        logger.error(f"Error getting timeframe signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting signals: {str(e)}")

@router.get("/timeframe/signals/{symbol}")
async def get_symbol_signals(symbol: str):
    """
    Get multi-timeframe trading signals for a specific symbol.
    
    Args:
        symbol: Cryptocurrency symbol (e.g., BTC)
        
    Returns:
        Dictionary with signals for the specified symbol
    """
    try:
        # Get all signals
        signals = await get_timeframe_signals()
        
        # Check if symbol exists
        if symbol.upper() not in signals:
            raise HTTPException(status_code=404, detail=f"Symbol {symbol} not found")
        
        return signals[symbol.upper()]
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting timeframe signals for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting signals: {str(e)}")

@router.get("/timeframe/refresh")
async def refresh_signals():
    """
    Force refresh of timeframe signals.
    
    Returns:
        Dictionary with refreshed signals
    """
    try:
        global signals_cache, last_update_time
        
        # Generate new signals
        signals_cache = await signal_generator.generate_signals()
        last_update_time = datetime.utcnow()
        
        logger.info(f"Manually refreshed timeframe signals for {len(signals_cache)} symbols")
        
        return {
            "status": "success",
            "message": f"Refreshed signals for {len(signals_cache)} symbols",
            "signals": signals_cache
        }
    
    except Exception as e:
        logger.error(f"Error refreshing timeframe signals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error refreshing signals: {str(e)}")
