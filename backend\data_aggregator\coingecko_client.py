"""
CoinGecko API Client

This module provides functions to fetch cryptocurrency data from the CoinGecko API.
"""

import os
import json
import logging
import time
import requests
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('coingecko_client')

# Data storage path
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')
os.makedirs(DATA_DIR, exist_ok=True)

# API configuration
API_KEY = "CG-Ag17QEEjKxkZAo4yFr66pE9L"
BASE_URL = "https://api.coingecko.com/api/v3"
PRO_BASE_URL = "https://api.coingecko.com/api/v3"  # Using regular API URL for demo key

# Coin ID mapping
COIN_IDS = {
    "BTC": "bitcoin",
    "ETH": "ethereum",
    "SOL": "solana",
    "ADA": "cardano",
    "DOT": "polkadot",
    "AVAX": "avalanche-2"
}

class CoinGeckoClient:
    """Client for fetching data from CoinGecko API."""

    def __init__(self, api_key=None):
        """Initialize the CoinGecko API client."""
        # Import API key from config if not provided
        if not api_key:
            try:
                import sys
                import os
                sys.path.append(os.path.dirname(os.path.dirname(__file__)))
                from config.api_keys import COINGECKO_API_KEY
                api_key = COINGECKO_API_KEY
            except ImportError:
                api_key = None

        self.api_key = api_key
        self.use_pro = bool(api_key)  # Use Pro API if key is provided
        self.base_url = BASE_URL
        self.headers = {}

        # Add API key to headers if available
        if self.api_key:
            self.headers["x-cg-pro-api-key"] = self.api_key
            logger.info(f"Initialized CoinGecko client with Pro API")
        else:
            logger.info(f"Initialized CoinGecko client with free API")

        self.rate_limit_remaining = 50  # Default value
        self.rate_limit_reset_at = 0

    def _handle_rate_limit(self):
        """Handle API rate limiting."""
        if self.rate_limit_remaining <= 1:
            # Wait until rate limit resets
            current_time = time.time()
            if current_time < self.rate_limit_reset_at:
                sleep_time = self.rate_limit_reset_at - current_time + 1
                logger.info(f"Rate limit reached. Waiting for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

    def _update_rate_limit_info(self, response):
        """Update rate limit information from response headers."""
        try:
            if 'x-ratelimit-remaining' in response.headers:
                self.rate_limit_remaining = int(response.headers['x-ratelimit-remaining'])

            if 'x-ratelimit-reset' in response.headers:
                self.rate_limit_reset_at = int(response.headers['x-ratelimit-reset'])
        except Exception as e:
            logger.warning(f"Error updating rate limit info: {str(e)}")

    def _make_request(self, endpoint, params=None):
        """
        Make a request to the CoinGecko API.

        Args:
            endpoint: API endpoint to call
            params: Query parameters

        Returns:
            Response data as JSON
        """
        try:
            self._handle_rate_limit()

            url = f"{self.base_url}/{endpoint}"
            response = requests.get(url, headers=self.headers, params=params)

            self._update_rate_limit_info(response)

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning("Rate limit exceeded. Waiting and retrying...")
                time.sleep(60)  # Wait for 1 minute
                return self._make_request(endpoint, params)
            else:
                logger.error(f"API request failed with status code {response.status_code}: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error making request to {endpoint}: {str(e)}")
            return None

    def ping(self):
        """Check if the API is up and running."""
        result = self._make_request("ping")
        return result is not None

    def get_price(self, coins, vs_currencies="usd"):
        """
        Get current price of cryptocurrencies.

        Args:
            coins: List of coin symbols or IDs
            vs_currencies: Comma-separated list of currencies

        Returns:
            Dictionary with price data
        """
        # Convert symbols to IDs if needed
        coin_ids = [COIN_IDS.get(coin, coin) for coin in coins]
        coin_ids_str = ",".join(coin_ids)

        params = {
            "ids": coin_ids_str,
            "vs_currencies": vs_currencies,
            "include_market_cap": "true",
            "include_24hr_vol": "true",
            "include_24hr_change": "true",
            "include_last_updated_at": "true"
        }

        return self._make_request("simple/price", params)

    def get_coin_data(self, coin):
        """
        Get detailed data for a specific coin.

        Args:
            coin: Coin symbol or ID

        Returns:
            Dictionary with coin data
        """
        # Convert symbol to ID if needed
        coin_id = COIN_IDS.get(coin, coin)

        params = {
            "localization": "false",
            "tickers": "false",
            "market_data": "true",
            "community_data": "true",
            "developer_data": "true",
            "sparkline": "false"
        }

        return self._make_request(f"coins/{coin_id}", params)

    def get_market_chart(self, coin, days=30, interval="daily"):
        """
        Get historical market data for a specific coin.

        Args:
            coin: Coin symbol or ID
            days: Number of days of data to retrieve
            interval: Data interval (daily, hourly)

        Returns:
            Dictionary with historical price, market cap, and volume data
        """
        # Convert symbol to ID if needed
        coin_id = COIN_IDS.get(coin, coin)

        params = {
            "vs_currency": "usd",
            "days": days,
            "interval": interval
        }

        return self._make_request(f"coins/{coin_id}/market_chart", params)

    def get_ohlc(self, coin, days=30):
        """
        Get OHLC (Open, High, Low, Close) data for a specific coin.

        Args:
            coin: Coin symbol or ID
            days: Number of days of data to retrieve

        Returns:
            List of OHLC data points
        """
        # Convert symbol to ID if needed
        coin_id = COIN_IDS.get(coin, coin)

        params = {
            "vs_currency": "usd",
            "days": days
        }

        return self._make_request(f"coins/{coin_id}/ohlc", params)

    def get_global_data(self):
        """
        Get global cryptocurrency market data.

        Returns:
            Dictionary with global market data
        """
        return self._make_request("global")

    def get_trending(self):
        """
        Get trending coins (top-7 trending coins on CoinGecko).

        Returns:
            Dictionary with trending coins data
        """
        return self._make_request("search/trending")

    def save_market_data(self, coin):
        """
        Fetch and save market data for a specific coin.

        Args:
            coin: Coin symbol

        Returns:
            Dictionary with saved data paths
        """
        try:
            # Get current timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create directory for this coin if it doesn't exist
            coin_dir = os.path.join(DATA_DIR, coin.lower())
            os.makedirs(coin_dir, exist_ok=True)

            saved_files = {}

            # Get and save current price data
            price_data = self.get_price([coin])
            if price_data:
                price_file = os.path.join(coin_dir, f"price_{timestamp}.json")
                with open(price_file, 'w') as f:
                    json.dump(price_data, f, indent=2)

                # Also save as latest
                latest_price_file = os.path.join(coin_dir, "price_latest.json")
                with open(latest_price_file, 'w') as f:
                    json.dump(price_data, f, indent=2)

                saved_files["price"] = price_file

            # Get and save detailed coin data
            coin_data = self.get_coin_data(coin)
            if coin_data:
                coin_file = os.path.join(coin_dir, f"data_{timestamp}.json")
                with open(coin_file, 'w') as f:
                    json.dump(coin_data, f, indent=2)

                # Also save as latest
                latest_coin_file = os.path.join(coin_dir, "data_latest.json")
                with open(latest_coin_file, 'w') as f:
                    json.dump(coin_data, f, indent=2)

                saved_files["data"] = coin_file

            # Get and save market chart data
            chart_data = self.get_market_chart(coin)
            if chart_data:
                chart_file = os.path.join(coin_dir, f"chart_{timestamp}.json")
                with open(chart_file, 'w') as f:
                    json.dump(chart_data, f, indent=2)

                # Also save as latest
                latest_chart_file = os.path.join(coin_dir, "chart_latest.json")
                with open(latest_chart_file, 'w') as f:
                    json.dump(chart_data, f, indent=2)

                saved_files["chart"] = chart_file

            # Get and save OHLC data
            ohlc_data = self.get_ohlc(coin)
            if ohlc_data:
                ohlc_file = os.path.join(coin_dir, f"ohlc_{timestamp}.json")
                with open(ohlc_file, 'w') as f:
                    json.dump(ohlc_data, f, indent=2)

                # Also save as latest
                latest_ohlc_file = os.path.join(coin_dir, "ohlc_latest.json")
                with open(latest_ohlc_file, 'w') as f:
                    json.dump(ohlc_data, f, indent=2)

                saved_files["ohlc"] = ohlc_file

            logger.info(f"Saved market data for {coin}")
            return saved_files
        except Exception as e:
            logger.error(f"Error saving market data for {coin}: {str(e)}")
            return {}

    def save_all_market_data(self):
        """
        Fetch and save market data for all supported coins.

        Returns:
            Dictionary with saved data paths for each coin
        """
        results = {}
        for coin in COIN_IDS.keys():
            results[coin] = self.save_market_data(coin)
        return results

    def get_latest_market_data(self, coin):
        """
        Get the latest saved market data for a specific coin.

        Args:
            coin: Coin symbol

        Returns:
            Dictionary with latest market data
        """
        try:
            coin_dir = os.path.join(DATA_DIR, coin.lower())

            # Check if directory exists
            if not os.path.exists(coin_dir):
                logger.warning(f"No data directory found for {coin}")
                return {}

            result = {}

            # Load latest price data
            price_file = os.path.join(coin_dir, "price_latest.json")
            if os.path.exists(price_file):
                with open(price_file, 'r') as f:
                    result["price"] = json.load(f)

            # Load latest coin data
            data_file = os.path.join(coin_dir, "data_latest.json")
            if os.path.exists(data_file):
                with open(data_file, 'r') as f:
                    result["data"] = json.load(f)

            # Load latest chart data
            chart_file = os.path.join(coin_dir, "chart_latest.json")
            if os.path.exists(chart_file):
                with open(chart_file, 'r') as f:
                    result["chart"] = json.load(f)

            # Load latest OHLC data
            ohlc_file = os.path.join(coin_dir, "ohlc_latest.json")
            if os.path.exists(ohlc_file):
                with open(ohlc_file, 'r') as f:
                    result["ohlc"] = json.load(f)

            return result
        except Exception as e:
            logger.error(f"Error getting latest market data for {coin}: {str(e)}")
            return {}

# Singleton instance
_instance = None

def get_instance():
    """Get the singleton instance of CoinGeckoClient."""
    global _instance
    if _instance is None:
        _instance = CoinGeckoClient()
    return _instance

def main():
    """Main function to test the CoinGecko client."""
    client = CoinGeckoClient()

    # Test API connection
    if not client.ping():
        logger.error("Failed to connect to CoinGecko API")
        return False

    logger.info("Successfully connected to CoinGecko API")

    # Save market data for all coins
    results = client.save_all_market_data()

    # Print results
    for coin, files in results.items():
        if files:
            logger.info(f"Saved {len(files)} files for {coin}")
        else:
            logger.warning(f"Failed to save data for {coin}")

    return True

if __name__ == "__main__":
    main()
