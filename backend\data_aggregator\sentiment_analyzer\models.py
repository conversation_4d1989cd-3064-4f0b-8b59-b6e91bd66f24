"""
Data models for sentiment analysis.
"""

from enum import Enum
from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel


class SentimentSource(str, Enum):
    """Source of sentiment data."""
    TWITTER = "twitter"
    REDDIT = "reddit"
    NEWS = "news"
    GITHUB = "github"
    DISCORD = "discord"
    TELEGRAM = "telegram"
    COMBINED = "combined"


class SentimentType(str, Enum):
    """Type of sentiment."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    BULLISH = "bullish"
    BEARISH = "bearish"
    MIXED = "mixed"


class SentimentData(BaseModel):
    """Model for sentiment data."""
    id: str
    asset_id: str
    asset_symbol: str
    source: SentimentSource
    sentiment_type: SentimentType
    sentiment_score: float  # -1.0 to 1.0
    volume: int  # Number of mentions/posts
    timestamp: datetime
    timeframe: str  # e.g., "1h", "24h", "7d"
    keywords: List[str] = []
    topics: List[str] = []
    raw_data: Optional[Dict[str, Any]] = None
    
    class Config:
        arbitrary_types_allowed = True


class SentimentSignal(BaseModel):
    """Model for sentiment-based trading signals."""
    id: str
    asset_id: str
    asset_symbol: str
    signal_type: str  # e.g., "sentiment_shift", "volume_spike", "trend_reversal"
    direction: str  # "bullish" or "bearish"
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    timestamp: datetime
    expiration: datetime
    sources: List[SentimentSource]
    description: str
    context_ids: List[str] = []
    
    class Config:
        arbitrary_types_allowed = True
