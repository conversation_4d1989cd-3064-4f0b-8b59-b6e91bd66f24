"""
Technical Analysis Module

This module provides technical analysis indicators for cryptocurrency price data.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class TechnicalAnalysis:
    """Technical analysis indicators for cryptocurrency price data."""
    
    @staticmethod
    def calculate_rsi(prices: List[float], period: int = 14) -> Optional[float]:
        """
        Calculate the Relative Strength Index (RSI).
        
        Args:
            prices: List of price values
            period: RSI period
            
        Returns:
            RSI value (0-100) or None if not enough data
        """
        if len(prices) < period + 1:
            return None
            
        # Convert to numpy array for calculations
        price_array = np.array(prices)
        
        # Calculate price changes
        deltas = np.diff(price_array)
        
        # Create arrays for gains and losses
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        # Calculate average gains and losses
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])
        
        # Calculate RS and RSI
        if avg_loss == 0:
            return 100.0
            
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def calculate_macd(prices: List[float], fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Optional[Dict[str, float]]:
        """
        Calculate the Moving Average Convergence Divergence (MACD).
        
        Args:
            prices: List of price values
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal line period
            
        Returns:
            Dictionary with MACD values or None if not enough data
        """
        if len(prices) < slow_period + signal_period:
            return None
            
        # Convert to pandas Series for EMA calculations
        price_series = pd.Series(prices)
        
        # Calculate EMAs
        fast_ema = price_series.ewm(span=fast_period, adjust=False).mean()
        slow_ema = price_series.ewm(span=slow_period, adjust=False).mean()
        
        # Calculate MACD line
        macd_line = fast_ema - slow_ema
        
        # Calculate signal line
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()
        
        # Calculate histogram
        histogram = macd_line - signal_line
        
        # Get the latest values
        latest_macd = macd_line.iloc[-1]
        latest_signal = signal_line.iloc[-1]
        latest_histogram = histogram.iloc[-1]
        
        return {
            "macd": latest_macd,
            "signal": latest_signal,
            "histogram": latest_histogram
        }
    
    @staticmethod
    def calculate_bollinger_bands(prices: List[float], period: int = 20, num_std: float = 2.0) -> Optional[Dict[str, float]]:
        """
        Calculate Bollinger Bands.
        
        Args:
            prices: List of price values
            period: Moving average period
            num_std: Number of standard deviations
            
        Returns:
            Dictionary with Bollinger Bands values or None if not enough data
        """
        if len(prices) < period:
            return None
            
        # Convert to numpy array for calculations
        price_array = np.array(prices)
        
        # Calculate SMA
        sma = np.mean(price_array[-period:])
        
        # Calculate standard deviation
        std = np.std(price_array[-period:])
        
        # Calculate upper and lower bands
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        
        # Calculate bandwidth and %B
        bandwidth = (upper_band - lower_band) / sma
        percent_b = (price_array[-1] - lower_band) / (upper_band - lower_band) if upper_band != lower_band else 0.5
        
        return {
            "sma": sma,
            "upper_band": upper_band,
            "lower_band": lower_band,
            "bandwidth": bandwidth,
            "percent_b": percent_b
        }
    
    @staticmethod
    def calculate_stochastic_oscillator(high_prices: List[float], low_prices: List[float], close_prices: List[float], k_period: int = 14, d_period: int = 3) -> Optional[Dict[str, float]]:
        """
        Calculate the Stochastic Oscillator.
        
        Args:
            high_prices: List of high price values
            low_prices: List of low price values
            close_prices: List of close price values
            k_period: %K period
            d_period: %D period
            
        Returns:
            Dictionary with Stochastic Oscillator values or None if not enough data
        """
        if len(close_prices) < k_period + d_period:
            return None
            
        # Convert to numpy arrays for calculations
        high_array = np.array(high_prices)
        low_array = np.array(low_prices)
        close_array = np.array(close_prices)
        
        # Calculate %K
        k_values = []
        for i in range(len(close_array) - k_period + 1):
            c = close_array[i + k_period - 1]
            h = np.max(high_array[i:i + k_period])
            l = np.min(low_array[i:i + k_period])
            
            if h == l:
                k = 50.0  # Default to middle if high equals low
            else:
                k = 100 * (c - l) / (h - l)
                
            k_values.append(k)
        
        # Calculate %D (SMA of %K)
        d_values = []
        for i in range(len(k_values) - d_period + 1):
            d = np.mean(k_values[i:i + d_period])
            d_values.append(d)
        
        # Get the latest values
        latest_k = k_values[-1]
        latest_d = d_values[-1]
        
        return {
            "k": latest_k,
            "d": latest_d
        }
    
    @staticmethod
    def calculate_volume_profile(prices: List[float], volumes: List[float], num_bins: int = 10) -> List[Dict[str, Any]]:
        """
        Calculate Volume Profile.
        
        Args:
            prices: List of price values
            volumes: List of volume values
            num_bins: Number of price bins
            
        Returns:
            List of volume profile data points
        """
        if not prices or not volumes or len(prices) != len(volumes):
            return []
            
        # Convert to numpy arrays for calculations
        price_array = np.array(prices)
        volume_array = np.array(volumes)
        
        # Calculate price range
        min_price = np.min(price_array)
        max_price = np.max(price_array)
        
        # Create price bins
        bin_edges = np.linspace(min_price, max_price, num_bins + 1)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        # Calculate volume in each bin
        volume_profile = []
        for i in range(num_bins):
            lower_bound = bin_edges[i]
            upper_bound = bin_edges[i + 1]
            
            # Find prices in this bin
            in_bin = (price_array >= lower_bound) & (price_array < upper_bound)
            
            # Sum volumes in this bin
            bin_volume = np.sum(volume_array[in_bin])
            
            volume_profile.append({
                "price": bin_centers[i],
                "volume": bin_volume,
                "lower_bound": lower_bound,
                "upper_bound": upper_bound
            })
        
        return volume_profile
    
    @staticmethod
    def detect_support_resistance(prices: List[float], window_size: int = 5, threshold: float = 0.02) -> Dict[str, List[float]]:
        """
        Detect support and resistance levels.
        
        Args:
            prices: List of price values
            window_size: Window size for local extrema detection
            threshold: Minimum price difference threshold
            
        Returns:
            Dictionary with support and resistance levels
        """
        if len(prices) < 2 * window_size + 1:
            return {"support": [], "resistance": []}
            
        # Convert to numpy array for calculations
        price_array = np.array(prices)
        
        # Find local minima (support) and maxima (resistance)
        support_levels = []
        resistance_levels = []
        
        for i in range(window_size, len(price_array) - window_size):
            # Check if this is a local minimum (support)
            if all(price_array[i] <= price_array[i-j] for j in range(1, window_size+1)) and \
               all(price_array[i] <= price_array[i+j] for j in range(1, window_size+1)):
                support_levels.append(price_array[i])
            
            # Check if this is a local maximum (resistance)
            if all(price_array[i] >= price_array[i-j] for j in range(1, window_size+1)) and \
               all(price_array[i] >= price_array[i+j] for j in range(1, window_size+1)):
                resistance_levels.append(price_array[i])
        
        # Filter out levels that are too close to each other
        filtered_support = []
        for level in support_levels:
            if not filtered_support or min(abs(level - s) / s for s in filtered_support) > threshold:
                filtered_support.append(level)
        
        filtered_resistance = []
        for level in resistance_levels:
            if not filtered_resistance or min(abs(level - r) / r for r in filtered_resistance) > threshold:
                filtered_resistance.append(level)
        
        return {
            "support": filtered_support,
            "resistance": filtered_resistance
        }
    
    @staticmethod
    def generate_signals(prices: List[float], volumes: List[float] = None, high_prices: List[float] = None, low_prices: List[float] = None) -> Dict[str, Any]:
        """
        Generate technical analysis signals.
        
        Args:
            prices: List of close price values
            volumes: List of volume values (optional)
            high_prices: List of high price values (optional)
            low_prices: List of low price values (optional)
            
        Returns:
            Dictionary with technical analysis signals
        """
        signals = {}
        
        # Use close prices for high/low if not provided
        if high_prices is None:
            high_prices = prices
        if low_prices is None:
            low_prices = prices
        if volumes is None:
            volumes = [1.0] * len(prices)
        
        # Calculate RSI
        rsi = TechnicalAnalysis.calculate_rsi(prices)
        if rsi is not None:
            signals["rsi"] = {
                "value": rsi,
                "signal": "oversold" if rsi < 30 else "overbought" if rsi > 70 else "neutral",
                "strength": min(1.0, abs(rsi - 50) / 30)
            }
        
        # Calculate MACD
        macd = TechnicalAnalysis.calculate_macd(prices)
        if macd is not None:
            signals["macd"] = {
                "value": macd,
                "signal": "bullish" if macd["histogram"] > 0 else "bearish",
                "strength": min(1.0, abs(macd["histogram"]) / (abs(macd["macd"]) + 0.0001))
            }
        
        # Calculate Bollinger Bands
        bb = TechnicalAnalysis.calculate_bollinger_bands(prices)
        if bb is not None:
            signals["bollinger_bands"] = {
                "value": bb,
                "signal": "oversold" if prices[-1] < bb["lower_band"] else "overbought" if prices[-1] > bb["upper_band"] else "neutral",
                "strength": min(1.0, abs(bb["percent_b"] - 0.5) * 2)
            }
        
        # Calculate Stochastic Oscillator
        stoch = TechnicalAnalysis.calculate_stochastic_oscillator(high_prices, low_prices, prices)
        if stoch is not None:
            signals["stochastic"] = {
                "value": stoch,
                "signal": "oversold" if stoch["k"] < 20 else "overbought" if stoch["k"] > 80 else "neutral",
                "strength": min(1.0, abs(stoch["k"] - 50) / 30)
            }
        
        # Detect support and resistance levels
        sr_levels = TechnicalAnalysis.detect_support_resistance(prices)
        signals["support_resistance"] = {
            "value": sr_levels,
            "nearest_support": min(sr_levels["support"], key=lambda x: abs(x - prices[-1])) if sr_levels["support"] else None,
            "nearest_resistance": min(sr_levels["resistance"], key=lambda x: abs(x - prices[-1])) if sr_levels["resistance"] else None
        }
        
        # Generate overall signal
        bullish_signals = sum(1 for k, v in signals.items() if k != "support_resistance" and v.get("signal") in ["bullish", "oversold"])
        bearish_signals = sum(1 for k, v in signals.items() if k != "support_resistance" and v.get("signal") in ["bearish", "overbought"])
        
        total_signals = bullish_signals + bearish_signals
        if total_signals > 0:
            signal_bias = (bullish_signals - bearish_signals) / total_signals
            
            if signal_bias > 0.3:
                overall_signal = "bullish"
                strength = min(1.0, signal_bias)
            elif signal_bias < -0.3:
                overall_signal = "bearish"
                strength = min(1.0, -signal_bias)
            else:
                overall_signal = "neutral"
                strength = min(1.0, abs(signal_bias))
        else:
            overall_signal = "neutral"
            strength = 0.0
        
        signals["overall"] = {
            "signal": overall_signal,
            "strength": strength,
            "bullish_count": bullish_signals,
            "bearish_count": bearish_signals
        }
        
        return signals

def main():
    """Main function for testing the technical analysis module."""
    # Sample price data
    prices = [
        42000, 42500, 43000, 43200, 43100, 42800, 42600, 42400, 42300, 42100,
        41800, 41500, 41200, 41000, 40800, 40500, 40200, 40000, 39800, 39500,
        39200, 39000, 38800, 38500, 38200, 38000, 38200, 38500, 38800, 39000,
        39200, 39500, 39800, 40000, 40200, 40500, 40800, 41000, 41200, 41500,
        41800, 42100, 42300, 42500, 42800, 43000, 43200, 43500, 43800, 44000
    ]
    
    volumes = [
        1000, 1200, 1500, 1800, 2000, 1800, 1600, 1400, 1200, 1000,
        800, 600, 500, 400, 300, 200, 300, 400, 500, 600,
        700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600,
        1700, 1800, 1900, 2000, 2100, 2200, 2300, 2400, 2500, 2600,
        2700, 2800, 2900, 3000, 3100, 3200, 3300, 3400, 3500, 3600
    ]
    
    # Generate signals
    signals = TechnicalAnalysis.generate_signals(prices, volumes)
    
    # Print signals
    print("Technical Analysis Signals:")
    for indicator, data in signals.items():
        if indicator == "support_resistance":
            print(f"\n{indicator.upper()}:")
            print(f"  Support levels: {data['value']['support']}")
            print(f"  Resistance levels: {data['value']['resistance']}")
            print(f"  Nearest support: {data['nearest_support']}")
            print(f"  Nearest resistance: {data['nearest_resistance']}")
        elif indicator == "overall":
            print(f"\n{indicator.upper()}:")
            print(f"  Signal: {data['signal']}")
            print(f"  Strength: {data['strength']:.2f}")
            print(f"  Bullish count: {data['bullish_count']}")
            print(f"  Bearish count: {data['bearish_count']}")
        else:
            print(f"\n{indicator.upper()}:")
            print(f"  Signal: {data['signal']}")
            print(f"  Strength: {data['strength']:.2f}")
            if "value" in data:
                print(f"  Value: {data['value']}")

if __name__ == "__main__":
    main()
