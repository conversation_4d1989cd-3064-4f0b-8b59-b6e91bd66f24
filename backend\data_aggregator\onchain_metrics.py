"""
On-Chain Metrics Collector

This module collects on-chain metrics for major cryptocurrencies using public APIs.
It focuses on active addresses and transaction volume as initial metrics.
"""

import os
import json
import time
import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('onchain_metrics')

# Data storage path
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'onchain')
os.makedirs(DATA_DIR, exist_ok=True)

# API endpoints
BLOCKCHAIN_INFO_API = "https://api.blockchain.info/stats"
BLOCKCHAIN_BALANCE_API = "https://api.blockchain.info/balance"
BLOCKCHAIN_UNSPENT_API = "https://api.blockchain.info/unspent"
BLOCKCHAIN_BLOCKS_API = "https://api.blockchain.info/blocks"

ETHERSCAN_API = "https://api.etherscan.io/api"
ETHERSCAN_BALANCE_API = "https://api.etherscan.io/api?module=account&action=balance"
ETHERSCAN_TOKENTX_API = "https://api.etherscan.io/api?module=account&action=tokentx"

SOLSCAN_API = "https://public-api.solscan.io/transaction/last"
SOLSCAN_ACCOUNT_API = "https://public-api.solscan.io/account/"

GLASSNODE_API = "https://api.glassnode.com/v1/metrics"
CRYPTOQUANT_API = "https://api.cryptoquant.com/v1"
COINMETRICS_API = "https://api.coinmetrics.io/v4"

# API keys
ETHERSCAN_API_KEY = os.getenv("ETHERSCAN_API_KEY", "")
GLASSNODE_API_KEY = os.getenv("GLASSNODE_API_KEY", "")
CRYPTOQUANT_API_KEY = os.getenv("CRYPTOQUANT_API_KEY", "")
COINMETRICS_API_KEY = os.getenv("COINMETRICS_API_KEY", "")

# Whale addresses to track (top holders)
WHALE_ADDRESSES = {
    "BTC": [
        "**********************************",  # Binance
        "**********************************",  # Bitfinex
        "**********************************"   # Huobi
    ],
    "ETH": [
        "******************************************",  # Binance
        "******************************************",  # Bitfinex
        "******************************************"   # FTX
    ]
}

class OnChainMetricsCollector:
    """Collects on-chain metrics for major cryptocurrencies."""

    def __init__(self):
        self.session = None
        self.metrics = {
            "BTC": {},
            "ETH": {},
            "SOL": {}
        }

    async def initialize(self):
        """Initialize HTTP session."""
        self.session = aiohttp.ClientSession()

    async def close(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()

    async def fetch_bitcoin_metrics(self):
        """Fetch Bitcoin on-chain metrics from Blockchain.info API."""
        try:
            # Fetch basic metrics
            async with self.session.get(BLOCKCHAIN_INFO_API) as response:
                if response.status == 200:
                    data = await response.json()

                    # Extract relevant metrics
                    self.metrics["BTC"] = {
                        "timestamp": datetime.now().isoformat(),
                        "active_addresses_24h": data.get("n_unique_addresses", 0),
                        "transaction_volume_24h": data.get("total_transaction_value", 0),
                        "transaction_count_24h": data.get("n_transactions", 0),
                        "average_transaction_value": data.get("average_transaction_value", 0),
                        "hash_rate": data.get("hash_rate", 0),
                        "difficulty": data.get("difficulty", 0),
                        "mempool_size": data.get("mempool_size", 0)
                    }
                    logger.info(f"Successfully fetched basic Bitcoin on-chain metrics")
                else:
                    logger.error(f"Failed to fetch Bitcoin metrics: HTTP {response.status}")
                    return

            # Fetch whale balances
            whale_balances = {}
            total_whale_balance = 0

            for address in WHALE_ADDRESSES["BTC"]:
                try:
                    url = f"{BLOCKCHAIN_BALANCE_API}?active={address}"
                    async with self.session.get(url) as response:
                        if response.status == 200:
                            balance_data = await response.json()
                            balance_satoshi = balance_data.get(address, {}).get("final_balance", 0)
                            balance_btc = balance_satoshi / 100000000  # Convert satoshi to BTC
                            whale_balances[address] = balance_btc
                            total_whale_balance += balance_btc
                except Exception as e:
                    logger.error(f"Error fetching balance for {address}: {str(e)}")

            # Add whale metrics
            self.metrics["BTC"]["whale_balances"] = whale_balances
            self.metrics["BTC"]["total_whale_balance"] = total_whale_balance
            self.metrics["BTC"]["whale_balance_change_24h"] = 0  # Would need historical data to calculate

            # Fetch recent blocks for miner concentration
            try:
                url = f"{BLOCKCHAIN_BLOCKS_API}/1000?format=json"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        blocks_data = await response.json()

                        # Count blocks by mining pool
                        pool_distribution = {}
                        for block in blocks_data:
                            pool = block.get("pool_name", "Unknown")
                            if pool in pool_distribution:
                                pool_distribution[pool] += 1
                            else:
                                pool_distribution[pool] = 1

                        # Calculate mining concentration (percentage of top 3 pools)
                        sorted_pools = sorted(pool_distribution.items(), key=lambda x: x[1], reverse=True)
                        top_3_blocks = sum(count for _, count in sorted_pools[:3])
                        mining_concentration = (top_3_blocks / len(blocks_data)) * 100 if blocks_data else 0

                        # Add mining metrics
                        self.metrics["BTC"]["mining_pool_distribution"] = pool_distribution
                        self.metrics["BTC"]["mining_concentration"] = mining_concentration
            except Exception as e:
                logger.error(f"Error fetching mining data: {str(e)}")

            # Add exchange flow metrics (would use paid APIs in production)
            self.metrics["BTC"]["exchange_inflow_24h"] = 12500  # Placeholder
            self.metrics["BTC"]["exchange_outflow_24h"] = 13200  # Placeholder
            self.metrics["BTC"]["exchange_netflow_24h"] = self.metrics["BTC"]["exchange_outflow_24h"] - self.metrics["BTC"]["exchange_inflow_24h"]

            logger.info(f"Successfully fetched extended Bitcoin on-chain metrics")
        except Exception as e:
            logger.error(f"Error fetching Bitcoin metrics: {str(e)}")

    async def fetch_ethereum_metrics(self):
        """Fetch Ethereum on-chain metrics from Etherscan API."""
        try:
            # Get daily transactions
            params = {
                "module": "stats",
                "action": "dailytx",
                "apikey": ETHERSCAN_API_KEY
            }

            async with self.session.get(ETHERSCAN_API, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "1":
                        result = data.get("result", [])
                        if result:
                            # Get the most recent day's data
                            latest = result[-1]
                            tx_count = int(latest.get("transactions", 0))
                        else:
                            tx_count = 0
                    else:
                        tx_count = 0
                        logger.warning(f"Etherscan API returned error: {data.get('message')}")
                else:
                    tx_count = 0
                    logger.error(f"Failed to fetch Ethereum transactions: HTTP {response.status}")
                    return

            # Get network hash rate
            params = {
                "module": "stats",
                "action": "hashrate",
                "apikey": ETHERSCAN_API_KEY
            }

            async with self.session.get(ETHERSCAN_API, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "1":
                        hash_rate = int(data.get("result", 0))
                    else:
                        hash_rate = 0
                        logger.warning(f"Etherscan API returned error: {data.get('message')}")
                else:
                    hash_rate = 0
                    logger.error(f"Failed to fetch Ethereum hash rate: HTTP {response.status}")

            # Get gas price
            params = {
                "module": "gastracker",
                "action": "gasoracle",
                "apikey": ETHERSCAN_API_KEY
            }

            gas_price_data = {}
            async with self.session.get(ETHERSCAN_API, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "1":
                        result = data.get("result", {})
                        gas_price_data = {
                            "safe_gas_price": result.get("SafeGasPrice", "0"),
                            "propose_gas_price": result.get("ProposeGasPrice", "0"),
                            "fast_gas_price": result.get("FastGasPrice", "0")
                        }
                    else:
                        logger.warning(f"Etherscan API returned error: {data.get('message')}")
                else:
                    logger.error(f"Failed to fetch Ethereum gas price: HTTP {response.status}")

            # Get total supply
            params = {
                "module": "stats",
                "action": "ethsupply",
                "apikey": ETHERSCAN_API_KEY
            }

            total_supply = 0
            async with self.session.get(ETHERSCAN_API, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "1":
                        total_supply = int(data.get("result", 0)) / 1e18  # Convert wei to ETH
                    else:
                        logger.warning(f"Etherscan API returned error: {data.get('message')}")
                else:
                    logger.error(f"Failed to fetch Ethereum supply: HTTP {response.status}")

            # Fetch whale balances
            whale_balances = {}
            total_whale_balance = 0

            for address in WHALE_ADDRESSES["ETH"]:
                try:
                    params = {
                        "module": "account",
                        "action": "balance",
                        "address": address,
                        "tag": "latest",
                        "apikey": ETHERSCAN_API_KEY
                    }

                    async with self.session.get(ETHERSCAN_API, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("status") == "1":
                                balance_wei = int(data.get("result", 0))
                                balance_eth = balance_wei / 1e18  # Convert wei to ETH
                                whale_balances[address] = balance_eth
                                total_whale_balance += balance_eth
                except Exception as e:
                    logger.error(f"Error fetching balance for {address}: {str(e)}")

            # Get DeFi stats (would use paid APIs in production)
            defi_stats = {
                "total_value_locked": 45000000000,  # Placeholder in USD
                "active_defi_users_24h": 125000,    # Placeholder
                "defi_transaction_volume_24h": 3500000000  # Placeholder in USD
            }

            # Store all metrics
            self.metrics["ETH"] = {
                "timestamp": datetime.now().isoformat(),
                "transaction_count_24h": tx_count,
                "hash_rate": hash_rate,
                "active_addresses_24h": int(tx_count * 0.35),  # Estimate based on tx count
                "gas_prices": gas_price_data,
                "total_supply": total_supply,
                "whale_balances": whale_balances,
                "total_whale_balance": total_whale_balance,
                "whale_balance_change_24h": 0,  # Would need historical data
                "defi_stats": defi_stats,
                "exchange_inflow_24h": 85000,  # Placeholder
                "exchange_outflow_24h": 92000,  # Placeholder
                "exchange_netflow_24h": 7000   # Placeholder
            }

            logger.info(f"Successfully fetched extended Ethereum on-chain metrics")

        except Exception as e:
            logger.error(f"Error fetching Ethereum metrics: {str(e)}")

    async def fetch_solana_metrics(self):
        """Fetch Solana on-chain metrics from Solscan API."""
        try:
            # Fetch recent transactions
            async with self.session.get(SOLSCAN_API) as response:
                if response.status == 200:
                    data = await response.json()

                    # Count transactions in the last 24 hours
                    # This is a simplified approach - in production you'd want to use a more comprehensive API
                    tx_count = len(data) if isinstance(data, list) else 0

                    # Initialize metrics
                    self.metrics["SOL"] = {
                        "timestamp": datetime.now().isoformat(),
                        "transaction_count_recent": tx_count,
                        # Rough estimates for demonstration
                        "active_addresses_24h": int(tx_count * 0.4)
                    }
                else:
                    logger.error(f"Failed to fetch Solana transactions: HTTP {response.status}")
                    return

            # Fetch network stats (in production, you'd use a more comprehensive API)
            # For now, we'll use placeholder data
            network_stats = {
                "current_slot": 150000000,  # Placeholder
                "current_epoch": 350,       # Placeholder
                "tps": 2500,                # Placeholder - transactions per second
                "max_tps": 65000,           # Placeholder - theoretical max TPS
                "active_validators": 1800,   # Placeholder
                "total_validators": 3000,    # Placeholder
                "stake_concentration": 33    # Placeholder - percentage held by top 10 validators
            }

            # Add network stats to metrics
            self.metrics["SOL"].update(network_stats)

            # Fetch NFT metrics (in production, you'd use a paid API)
            nft_metrics = {
                "daily_nft_volume": 250000,    # Placeholder in USD
                "daily_nft_transactions": 15000,  # Placeholder
                "active_nft_collections": 350     # Placeholder
            }

            # Add NFT metrics to metrics
            self.metrics["SOL"]["nft_metrics"] = nft_metrics

            # Fetch DeFi metrics (in production, you'd use a paid API)
            defi_metrics = {
                "total_value_locked": 500000000,  # Placeholder in USD
                "daily_swap_volume": 120000000,   # Placeholder in USD
                "active_defi_users": 45000        # Placeholder
            }

            # Add DeFi metrics to metrics
            self.metrics["SOL"]["defi_metrics"] = defi_metrics

            # Add exchange flow metrics (in production, you'd use a paid API)
            self.metrics["SOL"]["exchange_inflow_24h"] = 2500000  # Placeholder in SOL
            self.metrics["SOL"]["exchange_outflow_24h"] = 2700000  # Placeholder in SOL
            self.metrics["SOL"]["exchange_netflow_24h"] = self.metrics["SOL"]["exchange_outflow_24h"] - self.metrics["SOL"]["exchange_inflow_24h"]

            # Add developer activity metrics (in production, you'd use GitHub API)
            dev_metrics = {
                "github_commits_7d": 350,     # Placeholder
                "github_contributors": 120,   # Placeholder
                "github_stars": 12500,       # Placeholder
                "github_forks": 2800         # Placeholder
            }

            # Add developer metrics to metrics
            self.metrics["SOL"]["developer_metrics"] = dev_metrics

            logger.info(f"Successfully fetched extended Solana on-chain metrics")
        except Exception as e:
            logger.error(f"Error fetching Solana metrics: {str(e)}")

    def save_metrics(self):
        """Save collected metrics to JSON files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for coin, data in self.metrics.items():
            if data:
                filename = os.path.join(DATA_DIR, f"{coin.lower()}_metrics_{timestamp}.json")
                with open(filename, 'w') as f:
                    json.dump(data, f, indent=2)

                # Also save to latest file for easy access
                latest_file = os.path.join(DATA_DIR, f"{coin.lower()}_metrics_latest.json")
                with open(latest_file, 'w') as f:
                    json.dump(data, f, indent=2)

                logger.info(f"Saved {coin} metrics to {filename}")

    def get_metrics_for_signal_generation(self):
        """
        Process and format metrics for use in trading signal generation.
        Returns metrics in a format suitable for the signal generator.
        """
        signal_metrics = {}

        for coin, data in self.metrics.items():
            if not data:
                continue

            # Initialize metrics dictionary for this coin
            signal_metrics[coin] = {}

            # Add basic metrics
            signal_metrics[coin]["active_addresses"] = {
                "value": data.get("active_addresses_24h", 0),
                "trend": "neutral"  # We'll need historical data to determine trend
            }

            signal_metrics[coin]["transaction_volume"] = {
                "value": data.get("transaction_volume_24h", 0),
                "trend": "neutral"
            }

            signal_metrics[coin]["transaction_count"] = {
                "value": data.get("transaction_count_24h", 0) or data.get("transaction_count_recent", 0),
                "trend": "neutral"
            }

            # Add exchange flow metrics if available
            if "exchange_inflow_24h" in data and "exchange_outflow_24h" in data:
                signal_metrics[coin]["exchange_flow"] = {
                    "inflow": data.get("exchange_inflow_24h", 0),
                    "outflow": data.get("exchange_outflow_24h", 0),
                    "net_flow": data.get("exchange_netflow_24h", 0),
                    "trend": "bullish" if data.get("exchange_netflow_24h", 0) > 0 else "bearish"
                }

            # Add whale metrics if available
            if "total_whale_balance" in data:
                signal_metrics[coin]["whale_holdings"] = {
                    "total_balance": data.get("total_whale_balance", 0),
                    "balance_change": data.get("whale_balance_change_24h", 0),
                    "trend": "neutral"  # Would need historical data to determine trend
                }

            # Add coin-specific metrics
            if coin == "BTC":
                # Add Bitcoin-specific metrics
                if "hash_rate" in data:
                    signal_metrics[coin]["hash_rate"] = {
                        "value": data.get("hash_rate", 0),
                        "trend": "neutral"
                    }

                if "mining_concentration" in data:
                    signal_metrics[coin]["mining_decentralization"] = {
                        "concentration": data.get("mining_concentration", 0),
                        "trend": "bearish" if data.get("mining_concentration", 0) > 60 else "neutral"
                    }

                if "mempool_size" in data:
                    signal_metrics[coin]["mempool"] = {
                        "size": data.get("mempool_size", 0),
                        "trend": "bearish" if data.get("mempool_size", 0) > 20000 else "neutral"
                    }

            elif coin == "ETH":
                # Add Ethereum-specific metrics
                if "hash_rate" in data:
                    signal_metrics[coin]["hash_rate"] = {
                        "value": data.get("hash_rate", 0),
                        "trend": "neutral"
                    }

                if "gas_prices" in data:
                    gas_prices = data.get("gas_prices", {})
                    fast_gas = float(gas_prices.get("fast_gas_price", 0))
                    signal_metrics[coin]["gas_price"] = {
                        "value": fast_gas,
                        "trend": "bearish" if fast_gas > 100 else "neutral"
                    }

                if "defi_stats" in data:
                    defi_stats = data.get("defi_stats", {})
                    signal_metrics[coin]["defi"] = {
                        "tvl": defi_stats.get("total_value_locked", 0),
                        "active_users": defi_stats.get("active_defi_users_24h", 0),
                        "volume": defi_stats.get("defi_transaction_volume_24h", 0),
                        "trend": "neutral"  # Would need historical data to determine trend
                    }

            elif coin == "SOL":
                # Add Solana-specific metrics
                if "tps" in data:
                    signal_metrics[coin]["performance"] = {
                        "tps": data.get("tps", 0),
                        "max_tps": data.get("max_tps", 0),
                        "utilization": (data.get("tps", 0) / data.get("max_tps", 1)) * 100,
                        "trend": "neutral"
                    }

                if "stake_concentration" in data:
                    signal_metrics[coin]["decentralization"] = {
                        "stake_concentration": data.get("stake_concentration", 0),
                        "active_validators": data.get("active_validators", 0),
                        "trend": "bearish" if data.get("stake_concentration", 0) > 40 else "neutral"
                    }

                if "nft_metrics" in data:
                    nft_metrics = data.get("nft_metrics", {})
                    signal_metrics[coin]["nft_activity"] = {
                        "volume": nft_metrics.get("daily_nft_volume", 0),
                        "transactions": nft_metrics.get("daily_nft_transactions", 0),
                        "trend": "neutral"  # Would need historical data to determine trend
                    }

                if "developer_metrics" in data:
                    dev_metrics = data.get("developer_metrics", {})
                    signal_metrics[coin]["developer_activity"] = {
                        "commits": dev_metrics.get("github_commits_7d", 0),
                        "contributors": dev_metrics.get("github_contributors", 0),
                        "trend": "neutral"  # Would need historical data to determine trend
                    }

        return signal_metrics

async def main():
    """Main function to collect and save on-chain metrics."""
    collector = OnChainMetricsCollector()

    try:
        await collector.initialize()

        # Fetch metrics for each coin
        await collector.fetch_bitcoin_metrics()
        await collector.fetch_ethereum_metrics()
        await collector.fetch_solana_metrics()

        # Save metrics to files
        collector.save_metrics()

        # Get metrics formatted for signal generation
        signal_metrics = collector.get_metrics_for_signal_generation()
        logger.info(f"Metrics for signal generation: {json.dumps(signal_metrics, indent=2)}")

    finally:
        await collector.close()

if __name__ == "__main__":
    asyncio.run(main())
