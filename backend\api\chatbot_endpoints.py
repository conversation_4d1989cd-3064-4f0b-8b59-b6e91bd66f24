"""
Chatbot API Endpoints

This module provides API endpoints for the MCP Trading Platform chatbot.
"""

import os
import json
import logging
import re
from datetime import datetime
from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import Dict, List, Optional, Any

# Import data clients
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
try:
    from data_aggregator.coingecko_client import CoinGeckoClient, COIN_IDS
    has_coingecko = True
except ImportError:
    has_coingecko = False

try:
    from data_aggregator.coinmarketcap_client import CoinMarketCapClient
    has_coinmarketcap = True
except ImportError:
    has_coinmarketcap = False

try:
    from data_aggregator.cryptocompare_client import CryptoCompareClient
    has_cryptocompare = True
except ImportError:
    has_cryptocompare = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('chatbot_api')

# Create router
router = APIRouter(
    prefix="/api/chatbot",
    tags=["chatbot"],
    responses={404: {"description": "Not found"}},
)

# Initialize clients
if has_coingecko:
    coingecko_client = CoinGeckoClient()

if has_coinmarketcap:
    coinmarketcap_client = CoinMarketCapClient()

if has_cryptocompare:
    cryptocompare_client = CryptoCompareClient()

# Data paths
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
MARKET_DIR = os.path.join(DATA_DIR, 'market')
SIGNALS_DIR = os.path.join(DATA_DIR, 'signals')
ANALYSIS_DIR = os.path.join(DATA_DIR, 'analysis')

# Models
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]

class ChatResponse(BaseModel):
    response: str
    data: Optional[Dict[str, Any]] = None

# Knowledge base for common questions
KNOWLEDGE_BASE = {
    "greeting": [
        "Hello! I'm your MCP Trading Assistant. How can I help you with cryptocurrency trading today?",
        "Hi there! I'm here to help with your crypto trading questions. What would you like to know?",
        "Welcome to MCP Trading Platform! I can provide information about crypto prices, trading signals, and on-chain metrics."
    ],
    "capabilities": [
        "I can help you with:\n- Current cryptocurrency prices and market data\n- Trading signals and recommendations\n- On-chain metrics and analysis\n- Technical indicators and chart patterns\n- Historical price data and trends"
    ],
    "about_platform": [
        "MCP Trading Platform is an AI-driven trading platform that combines market data, on-chain metrics, and technical analysis to provide trading signals and insights for cryptocurrencies."
    ]
}

def get_coin_from_message(message):
    """Extract cryptocurrency name or symbol from message."""
    # Common cryptocurrency names and their variations
    coin_patterns = {
        "BTC": r"\b(btc|bitcoin|bitcoins)\b",
        "ETH": r"\b(eth|ethereum|ether)\b",
        "SOL": r"\b(sol|solana)\b",
        "ADA": r"\b(ada|cardano)\b",
        "DOT": r"\b(dot|polkadot)\b",
        "AVAX": r"\b(avax|avalanche)\b"
    }

    for symbol, pattern in coin_patterns.items():
        if re.search(pattern, message.lower()):
            return symbol

    return None

def get_price_data(coin):
    """Get price data for a specific coin."""
    try:
        # Try CryptoCompare first (most comprehensive data source)
        if has_cryptocompare:
            try:
                # Try to get saved CC data
                coin_dir = os.path.join(MARKET_DIR, coin.lower())
                cc_file = os.path.join(coin_dir, "cc_price_latest.json")

                if os.path.exists(cc_file):
                    with open(cc_file, 'r') as f:
                        cc_data = json.load(f)
                        if cc_data and "USD" in cc_data:
                            usd_data = cc_data["USD"]
                            return {
                                "price": usd_data.get("PRICE", 0),
                                "market_cap": usd_data.get("MKTCAP", 0),
                                "volume_24h": usd_data.get("VOLUME24HOUR", 0),
                                "price_change_24h": usd_data.get("CHANGEPCT24HOUR", 0),
                                "high_24h": usd_data.get("HIGH24HOUR", 0),
                                "low_24h": usd_data.get("LOW24HOUR", 0),
                                "source": "cryptocompare"
                            }
            except Exception as cc_error:
                logger.warning(f"Error getting CryptoCompare data for {coin}: {str(cc_error)}")

        # Try CoinMarketCap next (premium data source)
        if has_coinmarketcap:
            try:
                # Try to get saved CMC data
                coin_dir = os.path.join(MARKET_DIR, coin.lower())
                cmc_file = os.path.join(coin_dir, "cmc_quotes_latest.json")

                if os.path.exists(cmc_file):
                    with open(cmc_file, 'r') as f:
                        cmc_data = json.load(f)
                        if cmc_data and "quote" in cmc_data and "USD" in cmc_data["quote"]:
                            usd_quote = cmc_data["quote"]["USD"]
                            return {
                                "price": usd_quote.get("price", 0),
                                "market_cap": usd_quote.get("market_cap", 0),
                                "volume_24h": usd_quote.get("volume_24h", 0),
                                "price_change_24h": usd_quote.get("percent_change_24h", 0),
                                "price_change_7d": usd_quote.get("percent_change_7d", 0),
                                "source": "coinmarketcap"
                            }
            except Exception as cmc_error:
                logger.warning(f"Error getting CMC data for {coin}: {str(cmc_error)}")

        # Fall back to CoinGecko
        if has_coingecko:
            # Try to get live data
            price_data = coingecko_client.get_price([coin])
            if price_data:
                coin_id = COIN_IDS[coin]
                if coin_id in price_data:
                    return {
                        "price": price_data[coin_id].get("usd", 0),
                        "market_cap": price_data[coin_id].get("usd_market_cap", 0),
                        "volume_24h": price_data[coin_id].get("usd_24h_vol", 0),
                        "price_change_24h": price_data[coin_id].get("usd_24h_change", 0),
                        "source": "coingecko"
                    }

        # Try to get saved CoinGecko data
        coin_dir = os.path.join(MARKET_DIR, coin.lower())
        price_file = os.path.join(coin_dir, "price_latest.json")

        if os.path.exists(price_file):
            with open(price_file, 'r') as f:
                saved_data = json.load(f)
                coin_id = COIN_IDS[coin]
                if coin_id in saved_data:
                    return {
                        "price": saved_data[coin_id].get("usd", 0),
                        "market_cap": saved_data[coin_id].get("usd_market_cap", 0),
                        "volume_24h": saved_data[coin_id].get("usd_24h_vol", 0),
                        "price_change_24h": saved_data[coin_id].get("usd_24h_change", 0),
                        "source": "saved"
                    }

        return None
    except Exception as e:
        logger.error(f"Error getting price data for {coin}: {str(e)}")
        return None

def get_trading_signal(coin):
    """Get trading signal for a specific coin."""
    try:
        # Try to get saved signals
        signals_file = os.path.join(SIGNALS_DIR, "combined_signals_latest.json")

        if os.path.exists(signals_file):
            with open(signals_file, 'r') as f:
                signals = json.load(f)
                if coin in signals:
                    return signals[coin]

        # Return placeholder data if no signals found
        return {
            "1h": {
                "direction": "NEUTRAL",
                "strength": "Moderate (50%)",
                "confidence": "Medium",
                "indicators": {
                    "technical": {
                        "rsi": "Neutral (50)",
                        "macd": "Neutral",
                        "ma_cross": "No Cross"
                    },
                    "onchain": {
                        "network_health": "neutral",
                        "active_addresses_trend": "stable",
                        "transaction_count_trend": "stable"
                    }
                }
            }
        }
    except Exception as e:
        logger.error(f"Error getting trading signal for {coin}: {str(e)}")
        return None

def get_onchain_metrics(coin):
    """Get on-chain metrics for a specific coin."""
    try:
        # Try to get saved analysis
        analysis_file = os.path.join(ANALYSIS_DIR, f"{coin.lower()}_analysis_latest.json")

        if os.path.exists(analysis_file):
            with open(analysis_file, 'r') as f:
                analysis = json.load(f)
                return analysis

        # Return placeholder data if no analysis found
        return {
            "active_addresses": {
                "value": 1000000 if coin == "BTC" else 500000 if coin == "ETH" else 100000,
                "trend": "neutral"
            },
            "transaction_volume": {
                "value": **********0 if coin == "BTC" else 5000000000 if coin == "ETH" else **********,
                "trend": "neutral"
            },
            "transaction_count": {
                "value": 300000 if coin == "BTC" else 1000000 if coin == "ETH" else 50000000,
                "trend": "neutral"
            }
        }
    except Exception as e:
        logger.error(f"Error getting on-chain metrics for {coin}: {str(e)}")
        return None

def format_price_response(coin, price_data):
    """Format price data into a readable response."""
    if not price_data:
        return f"I'm sorry, I couldn't find price data for {coin}."

    price = price_data["price"]
    change_24h = price_data["price_change_24h"]
    market_cap = price_data["market_cap"]
    volume = price_data["volume_24h"]

    # Format numbers
    formatted_price = f"${price:,.2f}" if price < 100 else f"${price:,.0f}"
    formatted_market_cap = f"${market_cap/**********:.2f} billion"
    formatted_volume = f"${volume/**********:.2f} billion"

    # Determine if price is up or down
    change_24h_text = "up" if change_24h > 0 else "down"

    response = f"The current price of {coin} is {formatted_price}, {change_24h_text} {abs(change_24h):.2f}% in the last 24 hours. "
    response += f"It has a market cap of {formatted_market_cap} and 24-hour trading volume of {formatted_volume}."

    # Add high and low if available
    if "high_24h" in price_data and "low_24h" in price_data:
        high_24h = price_data["high_24h"]
        low_24h = price_data["low_24h"]
        formatted_high = f"${high_24h:,.2f}" if high_24h < 100 else f"${high_24h:,.0f}"
        formatted_low = f"${low_24h:,.2f}" if low_24h < 100 else f"${low_24h:,.0f}"
        response += f" The 24-hour range is {formatted_low} to {formatted_high}."

    # Add 7-day change if available
    if "price_change_7d" in price_data:
        change_7d = price_data["price_change_7d"]
        change_7d_text = "up" if change_7d > 0 else "down"
        response += f" Over the past 7 days, {coin} is {change_7d_text} {abs(change_7d):.2f}%."

    # Add data source
    if "source" in price_data:
        source = price_data["source"]
        if source == "cryptocompare":
            response += "\n\nThis data is provided by CryptoCompare, a comprehensive cryptocurrency data provider with real-time price information and technical indicators."
        elif source == "coinmarketcap":
            response += "\n\nThis data is provided by CoinMarketCap, a premium cryptocurrency data provider."
        elif source == "coingecko":
            response += "\n\nThis data is provided by CoinGecko."

    return response

def format_signal_response(coin, signal_data):
    """Format trading signal into a readable response."""
    if not signal_data or "1h" not in signal_data:
        return f"I'm sorry, I couldn't find trading signals for {coin}."

    # Get 1-hour signal
    signal = signal_data["1h"]
    direction = signal.get("direction", "NEUTRAL")
    strength = signal.get("strength", "Moderate (50%)")

    # Get indicators
    indicators = signal.get("indicators", {})
    technical = indicators.get("technical", {})
    onchain = indicators.get("onchain", {})

    rsi = technical.get("rsi", "Neutral")
    macd = technical.get("macd", "Neutral")
    network_health = onchain.get("network_health", "neutral")
    addresses_trend = onchain.get("active_addresses_trend", "stable")

    # Format response
    response = f"The current trading signal for {coin} is {direction} with {strength} strength. "

    if direction == "LONG":
        response += "Technical indicators suggest a bullish trend. "
    elif direction == "SHORT":
        response += "Technical indicators suggest a bearish trend. "
    else:
        response += "Technical indicators are neutral. "

    response += f"RSI is {rsi} and MACD is {macd}. "

    # Add on-chain insights
    response += f"On-chain metrics show {network_health} network health with {addresses_trend} active addresses."

    return response

def format_onchain_response(coin, metrics):
    """Format on-chain metrics into a readable response."""
    if not metrics:
        return f"I'm sorry, I couldn't find on-chain metrics for {coin}."

    active_addresses = metrics.get("active_addresses", {})
    transaction_volume = metrics.get("transaction_volume", {})
    transaction_count = metrics.get("transaction_count", {})

    addresses_value = active_addresses.get("value", 0)
    addresses_trend = active_addresses.get("trend", "neutral")

    volume_value = transaction_volume.get("value", 0)
    volume_trend = transaction_volume.get("trend", "neutral")

    count_value = transaction_count.get("value", 0)
    count_trend = transaction_count.get("trend", "neutral")

    # Format numbers
    formatted_addresses = f"{addresses_value:,}"
    formatted_volume = f"${volume_value/**********:.2f} billion"
    formatted_count = f"{count_value:,}"

    # Format trends
    trend_words = {
        "bullish": "increasing",
        "strongly_bullish": "rapidly increasing",
        "bearish": "decreasing",
        "strongly_bearish": "rapidly decreasing",
        "neutral": "stable"
    }

    addresses_trend_word = trend_words.get(addresses_trend, "stable")
    volume_trend_word = trend_words.get(volume_trend, "stable")
    count_trend_word = trend_words.get(count_trend, "stable")

    response = f"On-chain metrics for {coin} show {formatted_addresses} active addresses ({addresses_trend_word}), "
    response += f"{formatted_volume} in transaction volume ({volume_trend_word}), and "
    response += f"{formatted_count} transactions ({count_trend_word})."

    return response

def process_message(message):
    """Process a user message and generate a response."""
    message_lower = message.lower()

    # Check for greetings
    if re.search(r'\b(hi|hello|hey|greetings)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["greeting"][0]}

    # Check for capability questions
    if re.search(r'\b(what can you do|help me|capabilities|features)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["capabilities"][0]}

    # Check for platform questions
    if re.search(r'\b(what is|about|platform|mcp)\b', message_lower) and re.search(r'\b(mcp|platform|this)\b', message_lower):
        return {"response": KNOWLEDGE_BASE["about_platform"][0]}

    # Check for price questions
    if re.search(r'\b(price|worth|value|cost|rate)\b', message_lower):
        coin = get_coin_from_message(message)
        if coin:
            price_data = get_price_data(coin)
            response = format_price_response(coin, price_data)
            return {"response": response, "data": {"type": "price", "coin": coin, "price_data": price_data}}

    # Check for trading signal questions
    if re.search(r'\b(signal|recommendation|trade|buy|sell|long|short)\b', message_lower):
        coin = get_coin_from_message(message)
        if coin:
            signal_data = get_trading_signal(coin)
            response = format_signal_response(coin, signal_data)
            return {"response": response, "data": {"type": "signal", "coin": coin, "signal_data": signal_data}}

    # Check for on-chain metrics questions
    if re.search(r'\b(on-chain|onchain|blockchain|network|metrics)\b', message_lower):
        coin = get_coin_from_message(message)
        if coin:
            metrics = get_onchain_metrics(coin)
            response = format_onchain_response(coin, metrics)
            return {"response": response, "data": {"type": "onchain", "coin": coin, "metrics": metrics}}

    # Check for general coin questions
    coin = get_coin_from_message(message)
    if coin:
        # Get price data
        price_data = get_price_data(coin)
        price_response = format_price_response(coin, price_data)

        # Get trading signal
        signal_data = get_trading_signal(coin)
        signal_response = format_signal_response(coin, signal_data)

        # Combine responses
        response = f"{price_response}\n\n{signal_response}"

        return {
            "response": response,
            "data": {
                "type": "combined",
                "coin": coin,
                "price_data": price_data,
                "signal_data": signal_data
            }
        }

    # Default response
    return {
        "response": "I'm not sure I understand your question. You can ask me about cryptocurrency prices, trading signals, or on-chain metrics. For example, 'What's the price of Bitcoin?' or 'What's the trading signal for Ethereum?'"
    }

@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest = Body(...)):
    """
    Process a chat message and generate a response.

    Args:
        request: Chat request with messages
    """
    try:
        # Get the last user message
        last_message = None
        for message in reversed(request.messages):
            if message.role == "user":
                last_message = message.content
                break

        if not last_message:
            raise HTTPException(status_code=400, detail="No user message found")

        # Process the message
        result = process_message(last_message)

        return result
    except Exception as e:
        logger.error(f"Error processing chat message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/health")
async def health_check():
    """Check if the chatbot API is healthy."""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}
