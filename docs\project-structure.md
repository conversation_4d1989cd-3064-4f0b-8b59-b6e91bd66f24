# MCP Trading Platform Project Structure

## Overview

This document provides an overview of the project structure for the MCP Trading Platform.

## Main Directories

```
MCP_TRADE/
├── backend/                  # Backend code
│   ├── api/                  # API endpoints
│   ├── data_aggregator/      # Data collection modules
│   ├── integrations/         # External service integrations
│   ├── mcp_server/           # Model Context Protocol server
│   ├── ml/                   # Machine learning models
│   └── signal_generator/     # Trading signal generation
├── docs/                     # Documentation
│   ├── prediction-explanations.md
│   └── project-structure.md
├── frontend/                 # Frontend code
│   ├── node_modules/         # Node.js dependencies
│   └── public/               # Public assets
│       ├── css/              # CSS stylesheets
│       │   ├── price-prediction.css
│       │   └── styles.css
│       ├── js/               # JavaScript files
│       │   ├── data-service.js
│       │   ├── enhanced-signals.js
│       │   └── price-prediction.js
│       ├── dashboard.html
│       ├── governance.html
│       ├── index.html
│       ├── predictions.html
│       ├── sentiment.html
│       ├── settings.html
│       └── trading.html
├── README.md                 # Project README
└── start.bat                 # Startup script
```

## Key Files

### Backend

- `backend/server.py`: Main server file
- `backend/ml/lstm_prediction.py`: LSTM neural network model
- `backend/ml/simple_prediction.py`: Simple prediction models
- `backend/signal_generator/ml_predictor.py`: ML-based price predictor
- `backend/signal_generator/advanced_signals.py`: Advanced signal generation

### Frontend

- `frontend/public/predictions.html`: Predictions page
- `frontend/public/js/price-prediction.js`: Price prediction widget
- `frontend/public/css/price-prediction.css`: Styles for prediction widget
- `frontend/public/js/data-service.js`: Data service for API communication

### Documentation

- `docs/prediction-explanations.md`: Documentation for prediction explanations feature
- `docs/1-day-option-and-explanations-implementation.md`: Implementation details
- `docs/project-structure.md`: Project structure overview

### Scripts

- `start.bat`: Windows batch script to start the application
