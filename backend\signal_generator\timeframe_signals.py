"""
Multi-Timeframe Signal Generator for Project Ruby.

This module provides functionality to analyze cryptocurrency price data across
multiple timeframes and generate consensus signals.
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import aiohttp
import asyncio
import json
import os
import random

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define timeframes
TIMEFRAMES = ["5m", "15m", "1h", "4h", "1d", "1w"]

class TimeframeSignalGenerator:
    """Generates trading signals across multiple timeframes."""
    
    def __init__(self):
        """Initialize the timeframe signal generator."""
        self.api_key = os.environ.get("CRYPTOCOMPARE_API_KEY", "****************************************************************")
        logger.info("Initialized TimeframeSignalGenerator")
    
    async def fetch_historical_data(self, symbol: str, timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Fetch historical price data for a specific symbol and timeframe.
        
        Args:
            symbol: Cryptocurrency symbol (e.g., BTC)
            timeframe: Timeframe (e.g., 1h, 4h, 1d)
            limit: Number of data points to fetch
            
        Returns:
            List of price data points
        """
        try:
            # For now, we'll use synthetic data to avoid API rate limits
            # In a production environment, you would use the actual API call
            
            # Create synthetic data
            data = []
            base_price = self._get_base_price(symbol)
            
            for i in range(limit):
                # Add some randomness to the price
                price = base_price * (1 + 0.1 * np.sin(i / 10) + 0.05 * np.random.randn())
                
                # Create data point
                data.append({
                    "time": int((datetime.now() - timedelta(minutes=i * self._get_minutes(timeframe))).timestamp()),
                    "open": price * (1 - 0.01 * np.random.random()),
                    "high": price * (1 + 0.02 * np.random.random()),
                    "low": price * (1 - 0.02 * np.random.random()),
                    "close": price,
                    "volumefrom": 1000 * np.random.random(),
                    "volumeto": price * 1000 * np.random.random()
                })
            
            # Reverse to get chronological order
            data.reverse()
            
            return data
        except Exception as e:
            logger.error(f"Error fetching data: {str(e)}")
            return []
    
    def _get_base_price(self, symbol: str) -> float:
        """Get base price for a symbol."""
        prices = {
            "BTC": 60000,
            "ETH": 3000,
            "SOL": 100,
            "ADA": 0.5
        }
        return prices.get(symbol, 100)
    
    def _get_minutes(self, timeframe: str) -> int:
        """Convert timeframe to minutes."""
        mapping = {
            "5m": 5,
            "15m": 15,
            "30m": 30,
            "1h": 60,
            "4h": 240,
            "1d": 1440,
            "1w": 10080
        }
        return mapping.get(timeframe, 60)
    
    def analyze_timeframe(self, data: List[Dict[str, Any]], timeframe: str) -> Dict[str, Any]:
        """
        Analyze price data for a specific timeframe.
        
        Args:
            data: List of price data points
            timeframe: Timeframe (e.g., 1h, 4h, 1d)
            
        Returns:
            Dictionary with analysis results
        """
        if not data or len(data) < 20:
            return {
                "timeframe": timeframe,
                "signal": "neutral",
                "confidence": 0.5,
                "indicators": {
                    "trend": "neutral",
                    "momentum": "neutral",
                    "volatility": "medium",
                    "rsi": 50
                }
            }
        
        # Extract prices
        closes = np.array([item["close"] for item in data])
        
        # Calculate indicators
        sma_5 = np.mean(closes[-5:])
        sma_10 = np.mean(closes[-10:])
        sma_20 = np.mean(closes[-20:])
        
        # Calculate RSI
        deltas = np.diff(closes)
        seed = deltas[-14:]
        ups = seed[seed >= 0].sum() / 14
        downs = -seed[seed < 0].sum() / 14
        rs = ups / downs if downs != 0 else float('inf')
        rsi = 100 - (100 / (1 + rs))
        
        # Determine trend
        if sma_5 > sma_10 > sma_20:
            trend = "bullish"
        elif sma_5 < sma_10 < sma_20:
            trend = "bearish"
        elif sma_5 > sma_10:
            trend = "slightly bullish"
        elif sma_5 < sma_10:
            trend = "slightly bearish"
        else:
            trend = "neutral"
        
        # Determine momentum
        if rsi > 70:
            momentum = "overbought"
        elif rsi < 30:
            momentum = "oversold"
        elif rsi > 50:
            momentum = "positive"
        elif rsi < 50:
            momentum = "negative"
        else:
            momentum = "neutral"
        
        # Determine signal
        if trend in ["bullish", "slightly bullish"] and momentum in ["positive", "oversold"]:
            signal = "buy"
            confidence = 0.7 + 0.2 * np.random.random()
        elif trend in ["bearish", "slightly bearish"] and momentum in ["negative", "overbought"]:
            signal = "sell"
            confidence = 0.7 + 0.2 * np.random.random()
        else:
            signal = "neutral"
            confidence = 0.5 + 0.1 * np.random.random()
        
        return {
            "timeframe": timeframe,
            "signal": signal,
            "confidence": confidence,
            "indicators": {
                "trend": trend,
                "momentum": momentum,
                "volatility": "medium",
                "rsi": rsi,
                "sma_5": sma_5,
                "sma_10": sma_10,
                "sma_20": sma_20
            }
        }
    
    def generate_consensus_signal(self, timeframe_signals: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate consensus signal from multiple timeframe signals.
        
        Args:
            timeframe_signals: Dictionary of signals for different timeframes
            
        Returns:
            Dictionary with consensus signal information
        """
        if not timeframe_signals:
            return {
                "signal": "neutral",
                "confidence": 0.5,
                "timeframes": {}
            }
        
        # Count signals by type
        signal_counts = {"buy": 0, "sell": 0, "neutral": 0}
        signal_confidence = {"buy": 0, "sell": 0, "neutral": 0}
        
        # Assign weights to different timeframes
        timeframe_weights = {
            "5m": 0.05,
            "15m": 0.1,
            "1h": 0.15,
            "4h": 0.25,
            "1d": 0.3,
            "1w": 0.15
        }
        
        # Calculate weighted signals
        for tf, signal in timeframe_signals.items():
            weight = timeframe_weights.get(tf, 0.1)
            signal_type = signal["signal"]
            signal_counts[signal_type] += weight
            signal_confidence[signal_type] += signal["confidence"] * weight
        
        # Determine consensus signal
        if signal_counts["buy"] > signal_counts["sell"] and signal_counts["buy"] > signal_counts["neutral"]:
            consensus = "buy"
            confidence = signal_confidence["buy"] / max(0.01, signal_counts["buy"])
        elif signal_counts["sell"] > signal_counts["buy"] and signal_counts["sell"] > signal_counts["neutral"]:
            consensus = "sell"
            confidence = signal_confidence["sell"] / max(0.01, signal_counts["sell"])
        else:
            consensus = "neutral"
            confidence = signal_confidence["neutral"] / max(0.01, signal_counts["neutral"])
        
        # Calculate strength of consensus (how many timeframes agree)
        max_count = max(signal_counts.values())
        total_weight = sum(timeframe_weights.values())
        consensus_strength = max_count / total_weight
        
        # Adjust confidence based on consensus strength
        confidence = min(0.95, confidence * consensus_strength)
        
        return {
            "signal": consensus,
            "confidence": confidence,
            "consensus_strength": consensus_strength,
            "timeframes": timeframe_signals
        }
    
    async def generate_signals(self, symbols: List[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Generate trading signals for specified symbols across multiple timeframes.
        
        Args:
            symbols: List of cryptocurrency symbols
            
        Returns:
            Dictionary with signals for each symbol
        """
        if symbols is None:
            symbols = ["BTC", "ETH", "SOL", "ADA"]
        
        logger.info(f"Generating multi-timeframe signals for {len(symbols)} symbols")
        
        # Generate signals for each symbol
        signals = {}
        for symbol in symbols:
            try:
                # Fetch data and analyze each timeframe
                timeframe_signals = {}
                for tf in TIMEFRAMES:
                    data = await self.fetch_historical_data(symbol, tf)
                    timeframe_signals[tf] = self.analyze_timeframe(data, tf)
                
                # Generate consensus signal
                consensus = self.generate_consensus_signal(timeframe_signals)
                
                # Store signals
                signals[symbol] = {
                    "symbol": symbol,
                    "name": self._get_asset_name(symbol),
                    "price": self._get_base_price(symbol),
                    "timestamp": datetime.utcnow().isoformat(),
                    "consensus": consensus,
                    "timeframes": timeframe_signals
                }
                
                logger.info(f"Generated {consensus['signal']} signal for {symbol} with confidence {consensus['confidence']:.2f}")
            except Exception as e:
                logger.error(f"Error generating signal for {symbol}: {str(e)}")
        
        return signals
    
    def _get_asset_name(self, symbol: str) -> str:
        """Get asset name from symbol."""
        mapping = {
            "BTC": "Bitcoin",
            "ETH": "Ethereum",
            "SOL": "Solana",
            "ADA": "Cardano",
            "XRP": "Ripple",
            "DOT": "Polkadot",
            "DOGE": "Dogecoin",
            "AVAX": "Avalanche",
            "MATIC": "Polygon"
        }
        return mapping.get(symbol, symbol)

async def main():
    """Main function for testing the timeframe signal generator."""
    generator = TimeframeSignalGenerator()
    
    # Generate signals
    signals = await generator.generate_signals()
    
    # Print results
    print(json.dumps(signals, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
