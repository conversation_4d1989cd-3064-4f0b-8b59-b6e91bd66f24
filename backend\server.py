"""
MCP Trading Platform Server

This is the main server file for the MCP Trading Platform.
It provides API endpoints for trading signals, governance signals, and other data.
"""

import logging
import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import the endpoints
from enhanced_signals_endpoint import register_endpoints

# Import on-chain endpoints
try:
    from api.onchain_endpoints import router as onchain_router
    logger.info("On-chain endpoints loaded successfully")
    has_onchain_endpoints = True
except ImportError:
    logger.warning("On-chain endpoints module not found. Creating directory structure...")
    os.makedirs(os.path.join(os.path.dirname(__file__), 'api'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'onchain'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'analysis'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'signals'), exist_ok=True)
    logger.info("Directory structure created. Please restart the server.")
    has_onchain_endpoints = False

# Import market data endpoints
try:
    from api.market_endpoints import router as market_router
    logger.info("Market data endpoints loaded successfully")
    has_market_endpoints = True
except ImportError:
    logger.warning("Market data endpoints module not found. Creating directory structure...")
    os.makedirs(os.path.join(os.path.dirname(__file__), 'api'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'market'), exist_ok=True)
    logger.info("Directory structure created. Please restart the server.")
    has_market_endpoints = False

# Import chatbot endpoints
try:
    from api.chatbot_endpoints import router as chatbot_router
    logger.info("Chatbot endpoints loaded successfully")
    has_chatbot_endpoints = True
except ImportError:
    logger.warning("Chatbot endpoints module not found.")
    has_chatbot_endpoints = False

# Import CoinMarketCap endpoints
try:
    from api.cmc_endpoints import router as cmc_router
    logger.info("CoinMarketCap endpoints loaded successfully")
    has_cmc_endpoints = True
except ImportError:
    logger.warning("CoinMarketCap endpoints module not found.")
    has_cmc_endpoints = False

# Import CryptoCompare endpoints
try:
    from api.cc_endpoints import router as cc_router
    logger.info("CryptoCompare endpoints loaded successfully")
    has_cc_endpoints = True
except ImportError:
    logger.warning("CryptoCompare endpoints module not found.")
    has_cc_endpoints = False

# Import Timeframe endpoints
try:
    from api.timeframe_endpoints import router as timeframe_router
    logger.info("Timeframe endpoints loaded successfully")
    has_timeframe_endpoints = True
except ImportError:
    logger.warning("Timeframe endpoints module not found.")
    has_timeframe_endpoints = False

# Import Consensus endpoints
try:
    from api.consensus_endpoints import router as consensus_router
    logger.info("Consensus endpoints loaded successfully")
    has_consensus_endpoints = True
except ImportError:
    logger.warning("Consensus endpoints module not found.")
    has_consensus_endpoints = False

# Import Paper Trading endpoints
try:
    from api.paper_trading_endpoints import router as paper_trading_router
    logger.info("Paper Trading endpoints loaded successfully")
    has_paper_trading_endpoints = True
except ImportError:
    logger.warning("Paper Trading endpoints module not found.")
    has_paper_trading_endpoints = False

# Import Real-time Price endpoints
try:
    from api.realtime_price_endpoints import router as realtime_price_router
    logger.info("Real-time Price endpoints loaded successfully")
    has_realtime_price_endpoints = True
except ImportError:
    logger.warning("Real-time Price endpoints module not found.")
    has_realtime_price_endpoints = False

# Create FastAPI app
app = FastAPI(
    title="MCP Trading Platform API",
    description="API for the MCP Trading Platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Root endpoint
@app.get("/")
def read_root():
    return {
        "name": "MCP Trading Platform API",
        "version": "1.0.0",
        "status": "running"
    }

# Health check endpoint
@app.get("/health")
def health_check():
    return {"status": "healthy"}

# Register enhanced signals endpoints
register_endpoints(app)

# Register on-chain endpoints if available
if has_onchain_endpoints:
    app.include_router(onchain_router)
    logger.info("On-chain endpoints registered")

# Register market data endpoints if available
if has_market_endpoints:
    app.include_router(market_router)
    logger.info("Market data endpoints registered")

# Register chatbot endpoints if available
if has_chatbot_endpoints:
    app.include_router(chatbot_router)
    logger.info("Chatbot endpoints registered")

# Register CoinMarketCap endpoints if available
if has_cmc_endpoints:
    app.include_router(cmc_router)
    logger.info("CoinMarketCap endpoints registered")

# Register CryptoCompare endpoints if available
if has_cc_endpoints:
    app.include_router(cc_router)
    logger.info("CryptoCompare endpoints registered")

# Register Timeframe endpoints if available
if has_timeframe_endpoints:
    app.include_router(timeframe_router)
    logger.info("Timeframe endpoints registered")

# Register Consensus endpoints if available
if has_consensus_endpoints:
    app.include_router(consensus_router)
    logger.info("Consensus endpoints registered")

# Register Paper Trading endpoints if available
if has_paper_trading_endpoints:
    app.include_router(paper_trading_router)
    logger.info("Paper Trading endpoints registered")

# Register Real-time Price endpoints if available
if has_realtime_price_endpoints:
    app.include_router(realtime_price_router)
    logger.info("Real-time Price endpoints registered")

# Mount static files
frontend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
if os.path.exists(frontend_dir):
    app.mount("/public", StaticFiles(directory=os.path.join(frontend_dir, "public")), name="public")
    logger.info(f"Static files mounted from {os.path.join(frontend_dir, 'public')}")
else:
    logger.warning(f"Frontend directory not found at {frontend_dir}")

# Run the server
if __name__ == "__main__":
    logger.info("Starting MCP Trading Platform server...")
    uvicorn.run(app, host="127.0.0.1", port=8003)
