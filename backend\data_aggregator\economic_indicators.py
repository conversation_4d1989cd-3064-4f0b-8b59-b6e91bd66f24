"""
Economic Indicators Data Fetcher

This module fetches economic indicators data from various sources to provide
macroeconomic context for trading decisions.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# API endpoints
FRED_API_URL = "https://api.stlouisfed.org/fred/series/observations"
ALPHAVANTAGE_API_URL = "https://www.alphavantage.co/query"

class EconomicIndicatorsFetcher:
    """Fetches economic indicators data from various sources."""

    def __init__(self, api_keys: Dict[str, str] = None):
        """
        Initialize the economic indicators fetcher.
        
        Args:
            api_keys: Dictionary of API keys for different services
        """
        self.api_keys = api_keys or {}
        logger.info("Economic indicators fetcher initialized")

    async def fetch_inflation_data(self) -> Dict[str, Any]:
        """
        Fetch inflation data (CPI) from FRED.
        
        Returns:
            Dictionary with inflation data
        """
        if "fred" not in self.api_keys:
            logger.warning("FRED API key not provided")
            return {}

        try:
            # Fetch CPI data
            params = {
                "series_id": "CPIAUCSL",  # Consumer Price Index for All Urban Consumers
                "api_key": self.api_keys["fred"],
                "file_type": "json",
                "sort_order": "desc",
                "limit": 12  # Last 12 months
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(FRED_API_URL, params=params)
                response.raise_for_status()
                data = response.json()

                if "observations" in data:
                    observations = data["observations"]
                    
                    # Calculate inflation rate (year-over-year)
                    if len(observations) >= 12:
                        current_cpi = float(observations[0]["value"])
                        year_ago_cpi = float(observations[11]["value"])
                        inflation_rate = (current_cpi - year_ago_cpi) / year_ago_cpi * 100
                        
                        return {
                            "indicator": "inflation",
                            "value": inflation_rate,
                            "date": observations[0]["date"],
                            "previous": (float(observations[1]["value"]) - float(observations[12]["value"] if len(observations) >= 13 else year_ago_cpi)) / float(observations[12]["value"] if len(observations) >= 13 else year_ago_cpi) * 100,
                            "source": "fred",
                            "series_id": "CPIAUCSL"
                        }

            logger.warning("No inflation data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching inflation data: {str(e)}")
            return {}

    async def fetch_interest_rate_data(self) -> Dict[str, Any]:
        """
        Fetch interest rate data (Federal Funds Rate) from FRED.
        
        Returns:
            Dictionary with interest rate data
        """
        if "fred" not in self.api_keys:
            logger.warning("FRED API key not provided")
            return {}

        try:
            # Fetch Federal Funds Rate data
            params = {
                "series_id": "FEDFUNDS",  # Federal Funds Effective Rate
                "api_key": self.api_keys["fred"],
                "file_type": "json",
                "sort_order": "desc",
                "limit": 12  # Last 12 months
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(FRED_API_URL, params=params)
                response.raise_for_status()
                data = response.json()

                if "observations" in data:
                    observations = data["observations"]
                    
                    if observations:
                        current_rate = float(observations[0]["value"])
                        
                        return {
                            "indicator": "interest_rate",
                            "value": current_rate,
                            "date": observations[0]["date"],
                            "previous": float(observations[1]["value"]) if len(observations) > 1 else None,
                            "source": "fred",
                            "series_id": "FEDFUNDS"
                        }

            logger.warning("No interest rate data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching interest rate data: {str(e)}")
            return {}

    async def fetch_unemployment_data(self) -> Dict[str, Any]:
        """
        Fetch unemployment data from FRED.
        
        Returns:
            Dictionary with unemployment data
        """
        if "fred" not in self.api_keys:
            logger.warning("FRED API key not provided")
            return {}

        try:
            # Fetch unemployment rate data
            params = {
                "series_id": "UNRATE",  # Unemployment Rate
                "api_key": self.api_keys["fred"],
                "file_type": "json",
                "sort_order": "desc",
                "limit": 12  # Last 12 months
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(FRED_API_URL, params=params)
                response.raise_for_status()
                data = response.json()

                if "observations" in data:
                    observations = data["observations"]
                    
                    if observations:
                        current_rate = float(observations[0]["value"])
                        
                        return {
                            "indicator": "unemployment",
                            "value": current_rate,
                            "date": observations[0]["date"],
                            "previous": float(observations[1]["value"]) if len(observations) > 1 else None,
                            "source": "fred",
                            "series_id": "UNRATE"
                        }

            logger.warning("No unemployment data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching unemployment data: {str(e)}")
            return {}

    async def fetch_gdp_data(self) -> Dict[str, Any]:
        """
        Fetch GDP growth data from FRED.
        
        Returns:
            Dictionary with GDP growth data
        """
        if "fred" not in self.api_keys:
            logger.warning("FRED API key not provided")
            return {}

        try:
            # Fetch GDP growth rate data
            params = {
                "series_id": "A191RL1Q225SBEA",  # Real GDP Growth Rate
                "api_key": self.api_keys["fred"],
                "file_type": "json",
                "sort_order": "desc",
                "limit": 4  # Last 4 quarters
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(FRED_API_URL, params=params)
                response.raise_for_status()
                data = response.json()

                if "observations" in data:
                    observations = data["observations"]
                    
                    if observations:
                        current_rate = float(observations[0]["value"])
                        
                        return {
                            "indicator": "gdp_growth",
                            "value": current_rate,
                            "date": observations[0]["date"],
                            "previous": float(observations[1]["value"]) if len(observations) > 1 else None,
                            "source": "fred",
                            "series_id": "A191RL1Q225SBEA"
                        }

            logger.warning("No GDP growth data found")
            return {}

        except Exception as e:
            logger.error(f"Error fetching GDP growth data: {str(e)}")
            return {}

    async def fetch_forex_data(self, from_currency: str = "USD", to_currency: str = "EUR") -> Dict[str, Any]:
        """
        Fetch forex data from Alpha Vantage.
        
        Args:
            from_currency: Base currency
            to_currency: Quote currency
            
        Returns:
            Dictionary with forex data
        """
        if "alphavantage" not in self.api_keys:
            logger.warning("Alpha Vantage API key not provided")
            return {}

        try:
            # Fetch forex data
            params = {
                "function": "CURRENCY_EXCHANGE_RATE",
                "from_currency": from_currency,
                "to_currency": to_currency,
                "apikey": self.api_keys["alphavantage"]
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(ALPHAVANTAGE_API_URL, params=params)
                response.raise_for_status()
                data = response.json()

                if "Realtime Currency Exchange Rate" in data:
                    exchange_data = data["Realtime Currency Exchange Rate"]
                    
                    return {
                        "indicator": "forex",
                        "from_currency": exchange_data["1. From_Currency Code"],
                        "to_currency": exchange_data["3. To_Currency Code"],
                        "value": float(exchange_data["5. Exchange Rate"]),
                        "date": exchange_data["6. Last Refreshed"],
                        "source": "alphavantage"
                    }

            logger.warning(f"No forex data found for {from_currency}/{to_currency}")
            return {}

        except Exception as e:
            logger.error(f"Error fetching forex data: {str(e)}")
            return {}

    async def fetch_all_indicators(self) -> List[Dict[str, Any]]:
        """
        Fetch all economic indicators.
        
        Returns:
            List of dictionaries with economic indicators data
        """
        tasks = [
            self.fetch_inflation_data(),
            self.fetch_interest_rate_data(),
            self.fetch_unemployment_data(),
            self.fetch_gdp_data(),
            self.fetch_forex_data("USD", "EUR"),
            self.fetch_forex_data("USD", "JPY"),
            self.fetch_forex_data("USD", "GBP")
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Filter out empty results
        indicators = [result for result in results if result]
        
        logger.info(f"Fetched {len(indicators)} economic indicators")
        return indicators

    def to_context_items(self, indicators: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Convert economic indicators to context items for MCP.
        
        Args:
            indicators: List of dictionaries with economic indicators data
            
        Returns:
            List of context items
        """
        context_items = []
        
        for indicator in indicators:
            context_item = {
                "id": f"economic_{indicator['indicator']}_{uuid.uuid4()}",
                "type": "economic_indicator",
                "content": indicator,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": indicator.get("source", "unknown"),
                "confidence": 1.0
            }
            
            context_items.append(context_item)
        
        return context_items

async def main():
    """Main function for testing the economic indicators fetcher."""
    # Sample API keys (replace with actual keys in production)
    api_keys = {
        "fred": "YOUR_FRED_API_KEY",
        "alphavantage": "YOUR_ALPHAVANTAGE_API_KEY"
    }
    
    fetcher = EconomicIndicatorsFetcher(api_keys)
    
    # Fetch all indicators
    indicators = await fetcher.fetch_all_indicators()
    
    # Convert to context items
    context_items = fetcher.to_context_items(indicators)
    
    # Print results
    print(f"Fetched {len(indicators)} economic indicators")
    print(f"Generated {len(context_items)} context items")
    
    if indicators:
        print("\nEconomic Indicators:")
        for indicator in indicators:
            print(f"\n{indicator['indicator']}:")
            for key, value in indicator.items():
                if key != "indicator":
                    print(f"  {key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
