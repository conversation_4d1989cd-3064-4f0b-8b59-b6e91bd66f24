"""
API Server for Trading Signal Platform

This module implements a FastAPI server that exposes trading signals and other
information to the frontend application.
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from fastapi import FastAP<PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from fastapi.middleware.cors import CORSMiddleware
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel

# Import signal generator
import sys
sys.path.append("../signal_engine")
from signal_generator import SignalGenerator, Signal, SignalType, TimeFrame, AssetType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Trading Signal API",
    description="API for accessing trading signals and market data",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "development_secret_key")  # Change in production
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Initialize signal generator
signal_generator = SignalGenerator()

# Data models
class User(BaseModel):
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: Optional[bool] = None
    subscription_tier: str = "free"  # free, basic, premium

class UserInDB(User):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class SignalFilter(BaseModel):
    asset_types: Optional[List[AssetType]] = None
    time_frames: Optional[List[TimeFrame]] = None
    min_confidence: float = 0.0
    limit: int = 20

# In-memory storage (replace with database in production)
fake_users_db = {
    "user": {
        "username": "user",
        "full_name": "Test User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("password"),
        "disabled": False,
        "subscription_tier": "free"
    },
    "premium": {
        "username": "premium",
        "full_name": "Premium User",
        "email": "<EMAIL>",
        "hashed_password": pwd_context.hash("premium"),
        "disabled": False,
        "subscription_tier": "premium"
    }
}

# Import timezone early
from datetime import timezone

# In-memory signal cache
signal_cache: List[Signal] = []
last_signal_update = datetime.now(timezone.utc) - timedelta(hours=1)  # Initialize to trigger update

# Add some sample governance signals for testing
from datetime import timezone
sample_signals = [
    Signal(
        id="gov_sample_1",
        asset_id="uniswap",
        asset_symbol="UNI",
        asset_name="Uniswap",
        asset_type=AssetType.CRYPTO,
        signal_type=SignalType.HOLD,
        time_frame=TimeFrame.DAY_1,
        confidence=0.75,
        price=5.0,
        timestamp=datetime.now(timezone.utc),
        expiration=datetime.now(timezone.utc) + timedelta(days=1),
        indicators={
            "governance_signal_type": "high_participation",
            "governance_signal_strength": 0.8,
            "proposal_id": "sample-1",
            "description": "High participation in Uniswap governance proposal"
        },
        context_ids=["sample-context-1"]
    )
]

# Security functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def get_user(db, username: str):
    if username in db:
        user_dict = db[username]
        return UserInDB(**user_dict)
    return None

def authenticate_user(fake_db, username: str, password: str):
    user = get_user(fake_db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_data = TokenData(username=username)
    except JWTError:
        raise credentials_exception
    user = get_user(fake_users_db, username=token_data.username)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

# Helper functions
async def update_signals():
    """Update the signal cache if needed."""
    global signal_cache, last_signal_update, sample_signals

    # Check if signals need to be updated (every 5 minutes)
    current_time = datetime.now(timezone.utc)
    if (current_time - last_signal_update).total_seconds() < 300:
        return

    # Generate new signals
    try:
        new_signals = await signal_generator.generate_crypto_signals()
        if new_signals:
            # Add our sample governance signals for testing
            signal_cache = new_signals + sample_signals
            last_signal_update = current_time
            logger.info(f"Updated signal cache with {len(signal_cache)} signals ({len(new_signals)} price signals, {len(sample_signals)} sample governance signals)")
    except Exception as e:
        logger.error(f"Error updating signals: {e}")

def filter_signals_for_user(signals: List[Signal], user: User) -> List[Signal]:
    """Filter signals based on user's subscription tier."""
    if user.subscription_tier == "premium":
        return signals  # Premium users get all signals in real-time

    # Free users get delayed signals with lower confidence
    current_time = datetime.now(timezone.utc)
    return [
        signal for signal in signals
        if (current_time - signal.timestamp).total_seconds() > 3600  # 1-hour delay
    ][:5]  # Limit to 5 signals for free users

# API endpoints
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(fake_users_db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/users/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return current_user

@app.post("/signals", response_model=List[Signal])
async def get_signals(
    filter_params: SignalFilter,
    current_user: User = Depends(get_current_active_user)
):
    """Get trading signals based on filter parameters."""
    await update_signals()

    # Filter signals
    filtered_signals = signal_cache

    if filter_params.asset_types:
        filtered_signals = [s for s in filtered_signals if s.asset_type in filter_params.asset_types]

    if filter_params.time_frames:
        filtered_signals = [s for s in filtered_signals if s.time_frame in filter_params.time_frames]

    if filter_params.min_confidence > 0:
        filtered_signals = [s for s in filtered_signals if s.confidence >= filter_params.min_confidence]

    # Sort by confidence (highest first)
    filtered_signals.sort(key=lambda x: x.confidence, reverse=True)

    # Apply user-specific filtering
    user_signals = filter_signals_for_user(filtered_signals, current_user)

    # Apply limit
    user_signals = user_signals[:filter_params.limit]

    return user_signals

@app.get("/assets", response_model=List[Dict[str, Any]])
async def get_assets(current_user: User = Depends(get_current_active_user)):
    """Get list of available assets."""
    await update_signals()

    # Extract unique assets from signals
    assets = {}
    for signal in signal_cache:
        if signal.asset_id not in assets:
            assets[signal.asset_id] = {
                "id": signal.asset_id,
                "symbol": signal.asset_symbol,
                "name": signal.asset_name,
                "type": signal.asset_type,
                "price": signal.price,
                "last_updated": signal.timestamp
            }

    return list(assets.values())

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
