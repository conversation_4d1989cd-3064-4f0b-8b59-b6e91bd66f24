"""
Main entry point for the data aggregator.

This script initializes and runs the data scheduler, which collects data
from various sources and sends it to the MCP server.
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime

from data_aggregator.scheduler import DataAggregationScheduler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Global flag for graceful shutdown
shutdown_flag = False

def handle_shutdown(sig, frame):
    """Handle shutdown signals."""
    global shutdown_flag
    logger.info(f"Received signal {sig}, shutting down gracefully...")
    shutdown_flag = True

async def main():
    """Main entry point."""
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, handle_shutdown)
    signal.signal(signal.SIGTERM, handle_shutdown)

    # Initialize the data scheduler
    scheduler = DataAggregationScheduler()

    # Start the scheduler
    logger.info("Starting data aggregator...")
    await scheduler.start()

    # Run until shutdown signal is received
    while not shutdown_flag:
        try:
            # Process one task and then check for shutdown signal
            await scheduler.process_next_task()
            await asyncio.sleep(1)
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            await asyncio.sleep(5)  # Wait a bit before retrying

    # Shutdown
    logger.info("Shutting down data aggregator...")
    await scheduler.stop()
    logger.info("Data aggregator stopped")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, exiting...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unhandled exception: {e}")
        sys.exit(1)
