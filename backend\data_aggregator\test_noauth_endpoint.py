"""
Test the unauthenticated context batch endpoint.

This script sends a simple test context item to the MCP server's
unauthenticated endpoint to verify it's working correctly.
"""

import asyncio
import uuid
from datetime import datetime

import httpx

async def test_noauth_endpoint():
    """Test the unauthenticated context batch endpoint."""
    url = "http://127.0.0.1:8080/context/batch/noauth"

    # Create a simple test context item
    test_item = {
        "id": f"test_{uuid.uuid4()}",
        "type": "test",
        "content": {
            "message": "This is a test context item"
        },
        "timestamp": datetime.now().isoformat(),
        "source": "test_script",
        "confidence": 1.0
    }

    print(f"Sending test item to {url}...")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                url,
                json=[test_item],
                timeout=30
            )
            response.raise_for_status()

            print(f"Success! Response: {response.json()}")
            return True

    except httpx.HTTPStatusError as e:
        print(f"HTTP error: {e}")
        print(f"Response content: {e.response.text}")
        return False

    except httpx.RequestError as e:
        print(f"Request error: {e}")
        return False

    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_noauth_endpoint())
