"""
Simplified NLP Cloud Sentiment Analyzer

This module provides a minimal implementation for sentiment analysis using NLP Cloud.
"""

import logging
import asyncio
import httpx
from datetime import datetime, timezone
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# NLP Cloud API credentials
API_KEY = "a95f288fdcaee11608cdf4de5fe5f46f657da731"

class SimpleSentimentAnalyzer:
    """Simple sentiment analyzer using NLP Cloud."""

    def __init__(self):
        """Initialize the sentiment analyzer."""
        self.api_key = API_KEY
        self.base_url = "https://api.nlpcloud.io/v1"
        self.headers = {
            "Authorization": f"Token {self.api_key}",
            "Content-Type": "application/json"
        }
        logger.info("Simple sentiment analyzer initialized")

    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of a text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        url = f"{self.base_url}/bart-large-mnli/classification"
        payload = {
            "text": text,
            "labels": ["positive", "negative", "neutral"]
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, json=payload, headers=self.headers)
                response.raise_for_status()
                data = response.json()
                
                # Extract sentiment scores
                scores = data.get("scores", [])
                labels = data.get("labels", [])
                
                if not scores or not labels:
                    raise ValueError("No scores or labels returned from API")
                
                # Find the highest scoring sentiment
                max_score_index = scores.index(max(scores))
                sentiment_label = labels[max_score_index]
                score = scores[max_score_index]
                
                # Map sentiment label to positive/negative/neutral
                if sentiment_label == "positive":
                    sentiment_type = "positive"
                elif sentiment_label == "negative":
                    sentiment_type = "negative"
                else:
                    sentiment_type = "neutral"
                
                # Create sentiment result
                sentiment = {
                    "text": text[:100] + "..." if len(text) > 100 else text,
                    "sentiment": sentiment_type,
                    "score": score,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "source": "nlp_cloud"
                }
                
                logger.info(f"Analyzed sentiment: {sentiment['sentiment']} (score: {sentiment['score']:.2f})")
                return sentiment
        
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {str(e)}")
            
            # Fallback to simple keyword-based sentiment analysis
            positive_keywords = ["bullish", "surge", "grow", "positive", "increase", "improve", "gain", "record", "high", "up"]
            negative_keywords = ["bearish", "drop", "fall", "negative", "decrease", "decline", "loss", "low", "down", "resistance"]
            
            # Count positive and negative keywords
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_keywords if word in text_lower)
            negative_count = sum(1 for word in negative_keywords if word in text_lower)
            
            # Determine sentiment
            if positive_count > negative_count:
                sentiment_label = "positive"
                score = min(0.5 + (positive_count - negative_count) * 0.1, 0.9)
            elif negative_count > positive_count:
                sentiment_label = "negative"
                score = min(0.5 + (negative_count - positive_count) * 0.1, 0.9)
            else:
                sentiment_label = "neutral"
                score = 0.5
            
            # Create sentiment result
            return {
                "text": text[:100] + "..." if len(text) > 100 else text,
                "sentiment": sentiment_label,
                "score": score,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "nlp_cloud_fallback"
            }

    async def get_crypto_sentiment(self, crypto_symbol: str) -> Dict[str, Any]:
        """
        Get sentiment for a cryptocurrency.
        
        Args:
            crypto_symbol: Cryptocurrency symbol (e.g., "BTC")
            
        Returns:
            Dictionary with sentiment analysis
        """
        # Sample news headlines for testing
        sample_news = {
            "BTC": [
                f"{crypto_symbol} price surges to new all-time high as institutional adoption grows",
                f"{crypto_symbol} faces resistance at key level as traders take profits",
                f"Analysts predict bullish outlook for {crypto_symbol} in coming months"
            ],
            "ETH": [
                f"{crypto_symbol} upgrade improves scalability and reduces gas fees",
                f"{crypto_symbol} developer activity reaches record levels",
                f"DeFi growth continues to drive {crypto_symbol} demand"
            ],
            "SOL": [
                f"{crypto_symbol} network experiences downtime due to congestion",
                f"{crypto_symbol} ecosystem expands with new DeFi and NFT projects",
                f"Institutional investors show increased interest in {crypto_symbol}"
            ]
        }
        
        # Get news for the specified crypto
        news = sample_news.get(crypto_symbol, [
            f"Market sentiment mixed on {crypto_symbol} as traders await regulatory clarity",
            f"{crypto_symbol} shows strong technical indicators despite market volatility",
            f"New developments in {crypto_symbol} ecosystem attract investor attention"
        ])
        
        # Analyze sentiment for the first news headline only (to avoid rate limiting)
        headline = news[0]
        sentiment = await self.analyze_sentiment(headline)
        
        # Create a simplified sentiment result
        result = {
            "overall_sentiment": "bullish" if sentiment["sentiment"] == "positive" else "bearish" if sentiment["sentiment"] == "negative" else "neutral",
            "confidence": sentiment["score"],
            "positive_count": 1 if sentiment["sentiment"] == "positive" else 0,
            "negative_count": 1 if sentiment["sentiment"] == "negative" else 0,
            "neutral_count": 1 if sentiment["sentiment"] == "neutral" else 0,
            "headline": headline
        }
        
        logger.info(f"Analyzed sentiment for {crypto_symbol}: {result['overall_sentiment']} (confidence: {result['confidence']:.2f})")
        return result

async def main():
    """Main function for testing the sentiment analyzer."""
    analyzer = SimpleSentimentAnalyzer()
    
    # Test with a single text
    text = "Bitcoin's price action remains bullish as institutional adoption continues to grow."
    sentiment = await analyzer.analyze_sentiment(text)
    print(f"Sentiment for text: {sentiment['sentiment']} (score: {sentiment['score']:.2f})")
    
    # Test with crypto symbols
    for symbol in ["BTC", "ETH", "SOL"]:
        print(f"\nAnalyzing {symbol} sentiment...")
        result = await analyzer.get_crypto_sentiment(symbol)
        print(f"Overall sentiment: {result['overall_sentiment']} (confidence: {result['confidence']:.2f})")
        print(f"Headline: {result['headline']}")

if __name__ == "__main__":
    asyncio.run(main())
