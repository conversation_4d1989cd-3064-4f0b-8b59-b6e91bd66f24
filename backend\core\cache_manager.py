"""
Cache Manager for MCP Trading Platform

This module provides centralized caching functionality to improve performance
and reduce redundant API calls.
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
import redis.asyncio as redis
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class CacheItem(BaseModel):
    """Represents a cached item with metadata."""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime] = None
    tags: List[str] = []

class CacheManager:
    """Centralized cache manager for the trading platform."""
    
    def __init__(self, cache_settings):
        self.settings = cache_settings
        self.redis_client: Optional[redis.Redis] = None
        self.local_cache: Dict[str, CacheItem] = {}
        self.use_redis = True
        
    async def initialize(self):
        """Initialize the cache manager."""
        try:
            # Try to connect to Redis
            self.redis_client = redis.from_url(
                self.settings.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test the connection
            await self.redis_client.ping()
            logger.info("Redis cache initialized successfully")
            
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}. Using local cache only.")
            self.use_redis = False
            self.redis_client = None
    
    async def cleanup(self):
        """Clean up cache resources."""
        if self.redis_client:
            await self.redis_client.close()
        self.local_cache.clear()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get a value from cache."""
        try:
            if self.use_redis and self.redis_client:
                # Try Redis first
                value = await self.redis_client.get(key)
                if value is not None:
                    return json.loads(value)
            
            # Fall back to local cache
            if key in self.local_cache:
                item = self.local_cache[key]
                if item.expires_at is None or item.expires_at > datetime.now():
                    return item.value
                else:
                    # Remove expired item
                    del self.local_cache[key]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set a value in cache."""
        try:
            ttl_seconds = ttl_seconds or self.settings.cache_ttl_seconds
            
            if self.use_redis and self.redis_client:
                # Store in Redis
                await self.redis_client.setex(
                    key, 
                    ttl_seconds, 
                    json.dumps(value, default=str)
                )
            
            # Also store in local cache
            expires_at = datetime.now() + timedelta(seconds=ttl_seconds) if ttl_seconds else None
            self.local_cache[key] = CacheItem(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete a key from cache."""
        try:
            if self.use_redis and self.redis_client:
                await self.redis_client.delete(key)
            
            if key in self.local_cache:
                del self.local_cache[key]
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching a pattern."""
        try:
            count = 0
            
            if self.use_redis and self.redis_client:
                # Clear from Redis
                keys = await self.redis_client.keys(pattern)
                if keys:
                    count += await self.redis_client.delete(*keys)
            
            # Clear from local cache
            keys_to_delete = [key for key in self.local_cache.keys() if pattern in key]
            for key in keys_to_delete:
                del self.local_cache[key]
                count += 1
            
            return count
            
        except Exception as e:
            logger.error(f"Error clearing cache pattern {pattern}: {e}")
            return 0
    
    async def get_or_set(self, key: str, fetch_func, ttl_seconds: Optional[int] = None) -> Any:
        """Get value from cache or fetch and cache it."""
        # Try to get from cache first
        value = await self.get(key)
        if value is not None:
            return value
        
        # Fetch the value
        try:
            if asyncio.iscoroutinefunction(fetch_func):
                value = await fetch_func()
            else:
                value = fetch_func()
            
            # Cache the value
            await self.set(key, value, ttl_seconds)
            return value
            
        except Exception as e:
            logger.error(f"Error in get_or_set for key {key}: {e}")
            return None
    
    # Specialized cache methods for common use cases
    
    async def cache_price_data(self, symbol: str, data: Dict[str, Any]) -> bool:
        """Cache price data for a symbol."""
        key = f"price:{symbol.upper()}"
        return await self.set(key, data, self.settings.price_cache_ttl)
    
    async def get_price_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached price data for a symbol."""
        key = f"price:{symbol.upper()}"
        return await self.get(key)
    
    async def cache_signal_data(self, signal_type: str, data: List[Dict[str, Any]]) -> bool:
        """Cache signal data."""
        key = f"signals:{signal_type}"
        return await self.set(key, data, self.settings.signal_cache_ttl)
    
    async def get_signal_data(self, signal_type: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached signal data."""
        key = f"signals:{signal_type}"
        return await self.get(key)
    
    async def cache_market_data(self, data_type: str, data: Any) -> bool:
        """Cache market data."""
        key = f"market:{data_type}"
        return await self.set(key, data, self.settings.cache_ttl_seconds)
    
    async def get_market_data(self, data_type: str) -> Optional[Any]:
        """Get cached market data."""
        key = f"market:{data_type}"
        return await self.get(key)
    
    async def invalidate_symbol_cache(self, symbol: str) -> int:
        """Invalidate all cache entries for a symbol."""
        pattern = f"*:{symbol.upper()}*"
        return await self.clear_pattern(pattern)
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        stats = {
            "local_cache_size": len(self.local_cache),
            "redis_connected": self.use_redis and self.redis_client is not None,
            "cache_settings": {
                "default_ttl": self.settings.cache_ttl_seconds,
                "price_ttl": self.settings.price_cache_ttl,
                "signal_ttl": self.settings.signal_cache_ttl
            }
        }
        
        if self.use_redis and self.redis_client:
            try:
                info = await self.redis_client.info()
                stats["redis_info"] = {
                    "used_memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "total_commands_processed": info.get("total_commands_processed")
                }
            except Exception as e:
                logger.error(f"Error getting Redis stats: {e}")
        
        return stats
