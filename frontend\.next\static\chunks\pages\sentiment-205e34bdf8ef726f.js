(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[55],{9628:function(e,s,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/sentiment",function(){return n(6786)}])},6786:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return c}});var t=n(5893);n(7294);var i=n(1664),r=n.n(i),l=n(9008),a=n.n(l);function c(){return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)(a(),{children:(0,t.jsx)("title",{children:"Market Sentiment Analysis"})}),(0,t.jsx)("header",{className:"flex justify-between items-center mb-8",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Market Sentiment Analysis"}),(0,t.jsxs)("div",{className:"flex mt-2 space-x-4",children:[(0,t.jsx)(r(),{href:"/",className:"text-blue-600 hover:text-blue-800",children:"Dashboard"}),(0,t.jsx)(r(),{href:"/governance",className:"text-blue-600 hover:text-blue-800",children:"Governance"}),(0,t.jsx)(r(),{href:"/sentiment",className:"text-blue-600 hover:text-blue-800 font-bold",children:"Sentiment"})]})]})}),(0,t.jsx)("p",{className:"mb-8",children:"Track market sentiment across social media, news, and forums to identify potential market movements before they happen. Our advanced sentiment analysis provides unique insights into market psychology."}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:["BTC","ETH","UNI","AAVE"].map((e,s)=>(0,t.jsxs)("div",{className:"p-5 shadow-md border border-gray-200 rounded-lg bg-white",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-bold",children:e}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-sm ".concat(s%3==0?"bg-green-100 text-green-800":s%3==1?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:s%3==0?"Bullish":s%3==1?"Neutral":"Bearish"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"BTC"===e?"Bitcoin":"ETH"===e?"Ethereum":"UNI"===e?"Uniswap":"Aave"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Sentiment"}),(0,t.jsxs)("p",{className:"font-bold",children:[65+5*s,"%"]}),(0,t.jsxs)("p",{className:"text-xs text-green-500",children:["↑ ",5+s,"%"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Volume"}),(0,t.jsx)("p",{className:"font-bold",children:(10-s)*1e3}),(0,t.jsxs)("p",{className:"text-xs text-green-500",children:["↑ ",10+2*s,"%"]})]})]})]},e))}),(0,t.jsxs)("div",{className:"mb-8 p-6 border border-gray-200 rounded-lg bg-white",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Active Sentiment Signals"}),(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full bg-white",children:[(0,t.jsx)("thead",{children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"py-2 px-4 border-b",children:"Type"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b",children:"Asset"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b",children:"Direction"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b",children:"Strength"}),(0,t.jsx)("th",{className:"py-2 px-4 border-b",children:"Time"})]})}),(0,t.jsx)("tbody",{children:[{id:1,type:"sentiment_shift",asset:"BTC",direction:"bullish",strength:.75},{id:2,type:"volume_spike",asset:"ETH",direction:"bullish",strength:.82},{id:3,type:"strong_consensus",asset:"UNI",direction:"bullish",strength:.68}].map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"py-2 px-4 border-b",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"inline-block w-3 h-3 rounded-full mr-2 ".concat("sentiment_shift"===e.type?"bg-blue-500":"volume_spike"===e.type?"bg-purple-500":"bg-teal-500")}),(0,t.jsx)("span",{children:"sentiment_shift"===e.type?"Sentiment Shift":"volume_spike"===e.type?"Volume Spike":"Strong Consensus"})]})}),(0,t.jsx)("td",{className:"py-2 px-4 border-b",children:e.asset}),(0,t.jsx)("td",{className:"py-2 px-4 border-b",children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("bullish"===e.direction?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.direction})}),(0,t.jsx)("td",{className:"py-2 px-4 border-b",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,t.jsx)("div",{className:"h-2.5 rounded-full ".concat("bullish"===e.direction?"bg-green-500":"bg-red-500"),style:{width:"".concat(100*e.strength,"%")}})})}),(0,t.jsx)("td",{className:"py-2 px-4 border-b",children:new Date().toLocaleString()})]},e.id))})]})}),(0,t.jsx)("p",{className:"mt-4 text-sm text-gray-500",children:"Note: Showing sample data. Real sentiment signals will appear here once collected."})]}),(0,t.jsxs)("div",{className:"p-6 border border-gray-200 rounded-lg bg-white",children:[(0,t.jsx)("h2",{className:"text-xl font-bold mb-4",children:"How Our Sentiment Analysis Works"}),(0,t.jsx)("p",{className:"mb-4",children:"Our revolutionary trading platform uses advanced natural language processing and machine learning to analyze sentiment across multiple sources:"}),(0,t.jsxs)("ul",{className:"list-disc pl-5 mb-4 space-y-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Social Media Analysis:"})," Real-time monitoring of Twitter, Reddit, and other platforms"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"News Sentiment:"})," Analysis of financial news and crypto publications"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Forum Activity:"})," Tracking discussions on specialized crypto forums"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Volume Analysis:"})," Measuring changes in discussion volume as an indicator of interest"]})]}),(0,t.jsx)("p",{children:"By combining these sources and applying our proprietary algorithms, we can detect sentiment shifts and generate trading signals before they become apparent in price movements."})]})]})}},9008:function(e,s,n){e.exports=n(3867)}},function(e){e.O(0,[664,888,774,179],function(){return e(e.s=9628)}),_N_E=e.O()}]);