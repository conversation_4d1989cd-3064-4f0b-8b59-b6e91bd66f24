# MCP Trading Platform Environment Configuration
# Copy this file to .env and update with your actual values

# Environment
ENVIRONMENT=development
DEBUG=true

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8004
SERVER_WORKERS=1
SERVER_RELOAD=true

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8085", "http://localhost:8080"]
CORS_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]

# Security Configuration
SECRET_KEY=your_super_secret_key_change_this_in_production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
PASSWORD_HASH_ROUNDS=12

# Database Configuration
DATABASE_URL=sqlite:///./trading_platform.db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Cache Configuration (Redis)
REDIS_URL=redis://localhost:6379
CACHE_TTL_SECONDS=300
PRICE_CACHE_TTL=60
SIGNAL_CACHE_TTL=300

# API Keys - External Services
COINGECKO_API_KEY=your_coingecko_api_key
CRYPTOCOMPARE_API_KEY=your_cryptocompare_api_key
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
NEWSAPI_KEY=your_newsapi_key

# Rate Limiting
API_RATE_LIMIT_PER_MINUTE=60
API_RATE_LIMIT_BURST=10

# Machine Learning Configuration
ML_MODEL_UPDATE_INTERVAL=3600
ML_PREDICTION_HORIZON_DAYS=7
ML_CONFIDENCE_THRESHOLD=0.6
ML_MODELS_DIRECTORY=./models
LSTM_MODEL_PATH=./models/lstm_model.pkl

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_PATH=./logs/trading_platform.log
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5

# Data Collection Configuration
DATA_COLLECTION_INTERVAL=60
SIGNAL_GENERATION_INTERVAL=300

# Supported Cryptocurrencies
SUPPORTED_CRYPTOCURRENCIES=["BTC", "ETH", "SOL", "ADA", "DOT", "LINK", "UNI", "AAVE"]

# Blockchain API Keys (Optional)
ETHERSCAN_API_KEY=your_etherscan_api_key
BSCSCAN_API_KEY=your_bscscan_api_key
POLYGONSCAN_API_KEY=your_polygonscan_api_key

# Social Media API Keys (Optional)
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password

# Monitoring and Analytics (Optional)
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_google_analytics_id

# Production Settings (Override in production)
# ENVIRONMENT=production
# DEBUG=false
# SECRET_KEY=your_production_secret_key
# DATABASE_URL=postgresql://user:password@localhost/trading_platform
# REDIS_URL=redis://redis-server:6379
