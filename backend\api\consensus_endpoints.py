"""
API endpoints for consensus predictions.
"""

import logging
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException

from models.consensus_engine import ConsensusEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize consensus engine
consensus_engine = ConsensusEngine()

@router.get("/consensus/prediction/{symbol}")
async def get_consensus_prediction(symbol: str):
    """
    Get consensus prediction for a specific symbol.

    Args:
        symbol: Cryptocurrency symbol (e.g., BTC)

    Returns:
        Consensus prediction
    """
    try:
        # Generate prediction
        prediction = consensus_engine.generate_prediction(symbol.upper())

        logger.info(f"Generated consensus prediction for {symbol}")
        return prediction

    except Exception as e:
        logger.error(f"Error getting consensus prediction for {symbol}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting prediction: {str(e)}")

@router.get("/consensus/predictions")
async def get_consensus_predictions(symbols: Optional[List[str]] = None, force_refresh: bool = False):
    """
    Get consensus predictions for multiple symbols.

    Args:
        symbols: List of cryptocurrency symbols
        force_refresh: Whether to force a refresh of cached predictions

    Returns:
        Dictionary of consensus predictions
    """
    if symbols is None:
        symbols = ["BTC", "ETH", "SOL", "ADA"]

    try:
        # Use the new method to generate predictions for multiple symbols
        predictions = consensus_engine.generate_predictions_for_symbols(
            [s.upper() for s in symbols],
            force_refresh=force_refresh
        )

        logger.info(f"Generated consensus predictions for {len(symbols)} symbols")
        return predictions

    except Exception as e:
        logger.error(f"Error getting consensus predictions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting predictions: {str(e)}")

@router.get("/consensus/health")
async def health_check():
    """Check if the consensus API is healthy."""
    try:
        # Get the current timestamp
        current_time = datetime.now().isoformat()

        # Check if we have any cached predictions
        if hasattr(consensus_engine, 'predictions_cache') and consensus_engine.predictions_cache:
            # Return the timestamp of the most recent prediction
            latest_symbol = max(
                consensus_engine.last_update.items(),
                key=lambda x: x[1]
            )[0] if consensus_engine.last_update else None

            if latest_symbol:
                return {
                    "status": "healthy",
                    "timestamp": current_time,
                    "latest_prediction": latest_symbol,
                    "latest_update": consensus_engine.last_update[latest_symbol].isoformat()
                }

        # If no cached predictions, return basic health info
        return {
            "status": "healthy",
            "timestamp": current_time,
            "message": "No predictions cached yet"
        }
    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
