"""
Authentication Manager for MCP Trading Platform

This module provides centralized authentication and authorization functionality.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from passlib.context import CryptContext
from jose import JWTError, jwt
from fastapi import HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class User(BaseModel):
    """User model."""
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    disabled: bool = False
    plan: str = "free"  # free, premium, enterprise

class UserInDB(User):
    """User model with hashed password."""
    hashed_password: str

class Token(BaseModel):
    """Token model."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Token data model."""
    username: Optional[str] = None

class AuthManager:
    """Centralized authentication manager."""
    
    def __init__(self, security_settings):
        self.settings = security_settings
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Demo users database (in production, use a real database)
        self.users_db = {
            "user": {
                "username": "user",
                "full_name": "Demo User",
                "email": "<EMAIL>",
                "hashed_password": self.get_password_hash("password"),
                "disabled": False,
                "plan": "free"
            },
            "premium": {
                "username": "premium",
                "full_name": "Premium User",
                "email": "<EMAIL>",
                "hashed_password": self.get_password_hash("premium"),
                "disabled": False,
                "plan": "premium"
            },
            "admin": {
                "username": "admin",
                "full_name": "Admin User",
                "email": "<EMAIL>",
                "hashed_password": self.get_password_hash("admin123"),
                "disabled": False,
                "plan": "enterprise"
            }
        }
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password."""
        return self.pwd_context.hash(password)
    
    def get_user(self, username: str) -> Optional[UserInDB]:
        """Get user from database."""
        if username in self.users_db:
            user_dict = self.users_db[username]
            return UserInDB(**user_dict)
        return None
    
    def authenticate_user(self, username: str, password: str) -> Union[UserInDB, bool]:
        """Authenticate a user."""
        user = self.get_user(username)
        if not user:
            return False
        if not self.verify_password(password, user.hashed_password):
            return False
        return user
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create an access token."""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.settings.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.settings.secret_key, algorithm=self.settings.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[TokenData]:
        """Verify and decode a token."""
        try:
            payload = jwt.decode(token, self.settings.secret_key, algorithms=[self.settings.algorithm])
            username: str = payload.get("sub")
            if username is None:
                return None
            token_data = TokenData(username=username)
            return token_data
        except JWTError:
            return None
    
    async def authenticate_user_endpoint(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user and return token (for API endpoint)."""
        user = self.authenticate_user(username, password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        access_token_expires = timedelta(minutes=self.settings.access_token_expire_minutes)
        access_token = self.create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "plan": user.plan
            }
        }
    
    def get_current_user(self, token: str) -> User:
        """Get current user from token."""
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
        token_data = self.verify_token(token)
        if token_data is None:
            raise credentials_exception
        
        user = self.get_user(username=token_data.username)
        if user is None:
            raise credentials_exception
        
        return User(
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            disabled=user.disabled,
            plan=user.plan
        )
    
    def check_user_permissions(self, user: User, required_plan: str = "free") -> bool:
        """Check if user has required permissions."""
        plan_hierarchy = {
            "free": 0,
            "premium": 1,
            "enterprise": 2
        }
        
        user_level = plan_hierarchy.get(user.plan, 0)
        required_level = plan_hierarchy.get(required_plan, 0)
        
        return user_level >= required_level
    
    def require_plan(self, required_plan: str):
        """Decorator to require specific plan level."""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # Extract user from kwargs (assumes user is passed as dependency)
                user = kwargs.get('current_user')
                if not user or not self.check_user_permissions(user, required_plan):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"This feature requires {required_plan} plan or higher"
                    )
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def create_user(self, username: str, password: str, email: str, full_name: str, plan: str = "free") -> bool:
        """Create a new user."""
        if username in self.users_db:
            return False
        
        self.users_db[username] = {
            "username": username,
            "full_name": full_name,
            "email": email,
            "hashed_password": self.get_password_hash(password),
            "disabled": False,
            "plan": plan
        }
        
        logger.info(f"Created new user: {username}")
        return True
    
    def update_user_plan(self, username: str, new_plan: str) -> bool:
        """Update user's plan."""
        if username not in self.users_db:
            return False
        
        self.users_db[username]["plan"] = new_plan
        logger.info(f"Updated user {username} plan to {new_plan}")
        return True
    
    def disable_user(self, username: str) -> bool:
        """Disable a user."""
        if username not in self.users_db:
            return False
        
        self.users_db[username]["disabled"] = True
        logger.info(f"Disabled user: {username}")
        return True
    
    def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics."""
        total_users = len(self.users_db)
        active_users = sum(1 for user in self.users_db.values() if not user["disabled"])
        
        plan_counts = {}
        for user in self.users_db.values():
            plan = user["plan"]
            plan_counts[plan] = plan_counts.get(plan, 0) + 1
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "disabled_users": total_users - active_users,
            "plan_distribution": plan_counts
        }
