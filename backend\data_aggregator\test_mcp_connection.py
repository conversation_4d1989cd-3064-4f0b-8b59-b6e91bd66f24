"""
Test MCP Server Connection

This script tests the connection to the MCP server.
"""

import asyncio
import httpx

async def test_connection():
    """Test connection to the MCP server."""
    # Try both localhost and 127.0.0.1
    urls = ["http://localhost:8000/health", "http://127.0.0.1:8000/health"]

    for url in urls:
        print(f"\nTesting connection to {url}...")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=10)
                response.raise_for_status()

                print(f"Connection successful! Response: {response.json()}")
                return True

        except httpx.HTTPStatusError as e:
            print(f"HTTP error: {e}")
            # Try the next URL
            continue

        except httpx.RequestError as e:
            print(f"Request error: {e}")
            # Try the next URL
            continue

        except Exception as e:
            print(f"Unexpected error: {e}")
            # Try the next URL
            continue

    # If we get here, all URLs failed
    print("\nAll connection attempts failed.")
    print("This usually means the server is not running or not accessible.")
    print("Make sure the MCP server is running in another terminal with:")
    print("cd backend\\mcp_server")
    print("python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")
    return False

if __name__ == "__main__":
    asyncio.run(test_connection())
