import os
import re

# List of HTML files to update
html_files = [
    "frontend/public/governance.html",
    "frontend/public/index.html",
    "frontend/public/sentiment.html",
    "frontend/public/settings.html",
    "frontend/public/trading.html"
]

# Regular expression pattern to match the navigation div
nav_pattern = r'<div class="nav">\s*<a href="index\.html">Home</a>.*?<a href="settings\.html">Settings</a>\s*</div>'

# New navigation HTML with Charts tab
new_nav_html = '''<div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading.html">Trading</a>
                    <a href="governance.html">Governance</a>
                    <a href="sentiment.html">Sentiment</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="charts.html">Charts</a>
                    <a href="settings.html">Settings</a>
                </div>'''

# Process each file
for file_path in html_files:
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        continue
    
    # Read the file content
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Find the current active tab and set it to bold
    current_page = os.path.basename(file_path)
    new_nav = new_nav_html.replace(f'<a href="{current_page}">', f'<a href="{current_page}" style="font-weight: bold;">')
    
    # Replace the navigation
    updated_content = re.sub(nav_pattern, new_nav, content, flags=re.DOTALL)
    
    # Write the updated content back to the file
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(updated_content)
    
    print(f"Updated navigation in {file_path}")

print("Navigation update complete!")
