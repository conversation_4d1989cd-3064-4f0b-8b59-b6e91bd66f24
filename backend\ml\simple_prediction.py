"""
Simple Price Prediction Model

This module implements a basic machine learning model for cryptocurrency price prediction.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any

from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class SimplePricePredictor:
    """Simple machine learning model for cryptocurrency price prediction."""

    def __init__(self):
        """Initialize the price prediction model."""
        self.model = None
        self.X_scaler = None
        self.y_scaler = None
        self.lookback_days = 10
        logger.info("Initialized simple price predictor")

    def prepare_data(self, prices: List[float]) -> tuple:
        """
        Prepare data for training.

        Args:
            prices: List of historical prices

        Returns:
            Tuple of (X, y) for training
        """
        # Create sequences
        X, y = [], []
        for i in range(len(prices) - self.lookback_days):
            X.append(prices[i:i + self.lookback_days])
            y.append(prices[i + self.lookback_days])

        # Convert to numpy arrays
        X = np.array(X)
        y = np.array(y)

        # Scale data
        X_scaler = MinMaxScaler()
        y_scaler = MinMaxScaler()
        X_scaled = X_scaler.fit_transform(X)
        y_scaled = y_scaler.fit_transform(y.reshape(-1, 1)).flatten()

        # Store scalers for later use
        self.X_scaler = X_scaler
        self.y_scaler = y_scaler

        return X_scaled, y_scaled

    def train(self, prices: List[float], test_size: float = 0.2) -> Dict[str, Any]:
        """
        Train the price prediction model.

        Args:
            prices: List of historical prices
            test_size: Proportion of data to use for testing

        Returns:
            Dictionary with training results
        """
        # Prepare data
        X, y = self.prepare_data(prices)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, shuffle=False
        )

        # Create and train model
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.model.fit(X_train, y_train)

        # Make predictions
        y_pred_train = self.model.predict(X_train)
        y_pred_test = self.model.predict(X_test)

        # Inverse transform
        y_train_inv = self.y_scaler.inverse_transform(y_train.reshape(-1, 1)).flatten()
        y_test_inv = self.y_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_train_inv = self.y_scaler.inverse_transform(y_pred_train.reshape(-1, 1)).flatten()
        y_pred_test_inv = self.y_scaler.inverse_transform(y_pred_test.reshape(-1, 1)).flatten()

        # Calculate metrics
        train_rmse = np.sqrt(mean_squared_error(y_train_inv, y_pred_train_inv))
        test_rmse = np.sqrt(mean_squared_error(y_test_inv, y_pred_test_inv))
        train_mae = mean_absolute_error(y_train_inv, y_pred_train_inv)
        test_mae = mean_absolute_error(y_test_inv, y_pred_test_inv)
        train_r2 = r2_score(y_train_inv, y_pred_train_inv)
        test_r2 = r2_score(y_test_inv, y_pred_test_inv)

        # Log results
        logger.info(f"Training RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}, R²: {train_r2:.4f}")
        logger.info(f"Testing RMSE: {test_rmse:.4f}, MAE: {test_mae:.4f}, R²: {test_r2:.4f}")

        return {
            "train_rmse": train_rmse,
            "test_rmse": test_rmse,
            "train_mae": train_mae,
            "test_mae": test_mae,
            "train_r2": train_r2,
            "test_r2": test_r2
        }

    def predict(self, prices: List[float], days_ahead: int = 1) -> List[float]:
        """
        Make price predictions.

        Args:
            prices: List of historical prices
            days_ahead: Number of days to predict ahead

        Returns:
            List of predicted prices
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")

        # Make sure we have enough data
        if len(prices) < self.lookback_days:
            raise ValueError(f"Need at least {self.lookback_days} price points")

        # Make predictions
        predictions = []
        current_prices = list(prices.copy())

        for _ in range(days_ahead):
            # Get the most recent prices
            recent_prices = current_prices[-self.lookback_days:]

            # Scale the data
            X = np.array([recent_prices])
            X_scaled = self.X_scaler.transform(X)

            # Make prediction
            pred_scaled = self.model.predict(X_scaled)[0]

            # Inverse transform
            pred = self.y_scaler.inverse_transform([[pred_scaled]])[0][0]

            # Add to predictions
            predictions.append(pred)

            # Update current prices for next prediction
            current_prices.append(pred)

        return predictions

def main():
    """Main function for testing the price prediction model."""
    # Create sample price data
    np.random.seed(42)
    days = 100
    t = np.arange(days)

    # Create a price series with trend and noise
    trend = 100 + 0.5 * t
    noise = np.random.normal(0, 5, days)
    prices = trend + noise

    # Initialize and train model
    model = SimplePricePredictor()
    results = model.train(prices)

    # Print results
    print("\nTraining Results:")
    print(f"RMSE: {results['test_rmse']:.2f}")
    print(f"MAE: {results['test_mae']:.2f}")
    print(f"R²: {results['test_r2']:.2f}")

    # Make predictions
    predictions = model.predict(prices, days_ahead=7)

    # Print predictions
    print("\nPrice Predictions:")
    last_price = prices[-1]
    print(f"Last Price: {last_price:.2f}")

    for i, pred in enumerate(predictions):
        change = (pred / last_price - 1) * 100
        print(f"Day {i+1}: {pred:.2f} ({change:+.2f}%)")

if __name__ == "__main__":
    main()
