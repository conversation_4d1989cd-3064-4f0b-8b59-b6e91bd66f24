"""
Sentiment analyzer for market sentiment analysis.

This module collects and analyzes sentiment data from various sources
like social media, news, and forums to generate trading signals.
"""

import logging
import uuid
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .models import SentimentData, SentimentSource, SentimentType, SentimentSignal
from .news_api import NewsAPIClient

logger = logging.getLogger(__name__)


class SentimentAnalyzer:
    """
    Analyzes market sentiment from various sources and generates trading signals.
    """

    def __init__(self, mcp_client, config: Dict[str, Any] = None):
        """
        Initialize the sentiment analyzer.

        Args:
            mcp_client: Client for sending data to MCP server
            config: Configuration for the analyzer
        """
        self.mcp_client = mcp_client
        self.config = config or {}

        # Configure data sources
        self.sources = self.config.get('sources', [
            SentimentSource.TWITTER,
            SentimentSource.REDDIT,
            SentimentSource.NEWS
        ])

        # Assets to track
        self.tracked_assets = self.config.get('tracked_assets', [
            {"id": "bitcoin", "symbol": "BTC", "name": "Bitcoin"},
            {"id": "ethereum", "symbol": "ETH", "name": "Ethereum"},
            {"id": "uniswap", "symbol": "UNI", "name": "Uniswap"},
            {"id": "aave", "symbol": "AAVE", "name": "Aave"},
            {"id": "compound", "symbol": "COMP", "name": "Compound"},
            {"id": "maker", "symbol": "MKR", "name": "Maker"},
            {"id": "sushiswap", "symbol": "SUSHI", "name": "SushiSwap"},
            {"id": "curve-dao-token", "symbol": "CRV", "name": "Curve DAO"},
            {"id": "synthetix-network-token", "symbol": "SNX", "name": "Synthetix"},
            {"id": "balancer", "symbol": "BAL", "name": "Balancer"},
            {"id": "yearn-finance", "symbol": "YFI", "name": "Yearn.finance"},
        ])

        # API keys for various services
        self.api_keys = self.config.get('api_keys', {})

        # Historical sentiment data
        self.historical_data = {}

        # Last collection time
        self.last_collection_time = datetime.now() - timedelta(days=1)

        logger.info("Sentiment Analyzer initialized")

    async def collect_data(self) -> List[SentimentData]:
        """
        Collect sentiment data from all configured sources.

        Returns:
            List of sentiment data objects
        """
        logger.info("Starting sentiment data collection")

        all_sentiment_data = []

        # Collect data from each source
        for source in self.sources:
            try:
                if source == SentimentSource.TWITTER:
                    data = await self._collect_twitter_sentiment()
                elif source == SentimentSource.REDDIT:
                    data = await self._collect_reddit_sentiment()
                elif source == SentimentSource.NEWS:
                    data = await self._collect_news_sentiment()
                else:
                    logger.warning(f"Unsupported sentiment source: {source}")
                    continue

                all_sentiment_data.extend(data)
                logger.info(f"Collected {len(data)} sentiment data points from {source}")

            except Exception as e:
                logger.error(f"Error collecting sentiment data from {source}: {str(e)}")

        # Update last collection time
        self.last_collection_time = datetime.now()

        # Store historical data
        for data in all_sentiment_data:
            asset_key = data.asset_symbol
            if asset_key not in self.historical_data:
                self.historical_data[asset_key] = []
            self.historical_data[asset_key].append(data)

            # Keep only recent data (last 7 days)
            cutoff = datetime.now() - timedelta(days=7)
            self.historical_data[asset_key] = [
                d for d in self.historical_data[asset_key]
                if d.timestamp > cutoff
            ]

        logger.info(f"Collected {len(all_sentiment_data)} total sentiment data points")
        return all_sentiment_data

    async def _collect_twitter_sentiment(self) -> List[SentimentData]:
        """
        Collect sentiment data from Twitter.

        In a real implementation, this would use the Twitter API to fetch
        tweets about cryptocurrencies and analyze their sentiment.

        Returns:
            List of sentiment data objects
        """
        # This is a placeholder implementation
        # In a real system, you would use the Twitter API and a sentiment analysis model

        sentiment_data = []

        for asset in self.tracked_assets:
            # Simulate sentiment data
            sentiment_score = random.uniform(-0.8, 0.8)
            sentiment_type = self._score_to_sentiment_type(sentiment_score)

            data = SentimentData(
                id=f"twitter_{asset['symbol']}_{uuid.uuid4()}",
                asset_id=asset['id'],
                asset_symbol=asset['symbol'],
                source=SentimentSource.TWITTER,
                sentiment_type=sentiment_type,
                sentiment_score=sentiment_score,
                volume=random.randint(100, 10000),
                timestamp=datetime.now(),
                timeframe="24h",
                keywords=[asset['name'].lower(), "crypto", "blockchain"],
                topics=["price", "trading", "technology"]
            )

            sentiment_data.append(data)

        return sentiment_data

    async def _collect_reddit_sentiment(self) -> List[SentimentData]:
        """
        Collect sentiment data from Reddit.

        In a real implementation, this would use the Reddit API to fetch
        posts about cryptocurrencies and analyze their sentiment.

        Returns:
            List of sentiment data objects
        """
        # This is a placeholder implementation
        # In a real system, you would use the Reddit API and a sentiment analysis model

        sentiment_data = []

        for asset in self.tracked_assets:
            # Simulate sentiment data
            sentiment_score = random.uniform(-0.7, 0.9)
            sentiment_type = self._score_to_sentiment_type(sentiment_score)

            data = SentimentData(
                id=f"reddit_{asset['symbol']}_{uuid.uuid4()}",
                asset_id=asset['id'],
                asset_symbol=asset['symbol'],
                source=SentimentSource.REDDIT,
                sentiment_type=sentiment_type,
                sentiment_score=sentiment_score,
                volume=random.randint(50, 5000),
                timestamp=datetime.now(),
                timeframe="24h",
                keywords=[asset['name'].lower(), "crypto", "investment"],
                topics=["price", "trading", "technology", "development"]
            )

            sentiment_data.append(data)

        return sentiment_data

    async def _collect_news_sentiment(self) -> List[SentimentData]:
        """
        Collect sentiment data from news sources using NewsAPI.

        Returns:
            List of sentiment data objects
        """
        sentiment_data = []

        # Initialize NewsAPI client with API key if available
        news_api_key = self.api_keys.get('news', None)
        news_client = NewsAPIClient(api_key=news_api_key)

        for asset in self.tracked_assets:
            try:
                # Define keywords for this asset
                keywords = [
                    asset['name'],
                    asset['symbol'],
                    f"{asset['name']} crypto",
                    f"{asset['symbol']} price"
                ]

                # Fetch news articles for this asset
                articles = await news_client.fetch_crypto_news(
                    keywords=keywords,
                    days=3  # Look back 3 days
                )

                if not articles:
                    logger.warning(f"No news articles found for {asset['symbol']}")
                    continue

                # Calculate sentiment score based on article titles and descriptions
                # This is a simple implementation - in a real system, you would use NLP
                positive_words = ["bullish", "surge", "rally", "gain", "rise", "soar", "jump", "positive", "growth"]
                negative_words = ["bearish", "crash", "plunge", "drop", "fall", "decline", "negative", "loss"]

                sentiment_scores = []
                for article in articles:
                    title = article.get("title", "").lower()
                    description = article.get("description", "").lower()
                    content = article.get("content", "").lower()

                    # Count positive and negative words
                    pos_count = sum(1 for word in positive_words if word in title or word in description or word in content)
                    neg_count = sum(1 for word in negative_words if word in title or word in description or word in content)

                    # Calculate simple sentiment score
                    if pos_count + neg_count > 0:
                        score = (pos_count - neg_count) / (pos_count + neg_count)
                        sentiment_scores.append(score)

                # Calculate average sentiment score
                if sentiment_scores:
                    avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                else:
                    avg_sentiment = 0.0  # Neutral if no sentiment detected

                # Determine sentiment type
                sentiment_type = self._score_to_sentiment_type(avg_sentiment)

                # Create sentiment data object
                data = SentimentData(
                    id=f"news_{asset['symbol']}_{uuid.uuid4()}",
                    asset_id=asset['id'],
                    asset_symbol=asset['symbol'],
                    source=SentimentSource.NEWS,
                    sentiment_type=sentiment_type,
                    sentiment_score=avg_sentiment,
                    volume=len(articles),
                    timestamp=datetime.now(),
                    timeframe="72h",  # 3 days
                    keywords=[asset['name'].lower(), "crypto", "market"],
                    topics=["price", "market", "regulation", "adoption"],
                    raw_data={"article_count": len(articles)}
                )

                sentiment_data.append(data)
                logger.info(f"Collected news sentiment for {asset['symbol']}: score={avg_sentiment:.2f}, articles={len(articles)}")

            except Exception as e:
                logger.error(f"Error collecting news sentiment for {asset['symbol']}: {str(e)}")

        return sentiment_data

    def _score_to_sentiment_type(self, score: float) -> SentimentType:
        """
        Convert a sentiment score to a sentiment type.

        Args:
            score: Sentiment score between -1.0 and 1.0

        Returns:
            Sentiment type
        """
        if score > 0.5:
            return SentimentType.BULLISH
        elif score > 0.1:
            return SentimentType.POSITIVE
        elif score < -0.5:
            return SentimentType.BEARISH
        elif score < -0.1:
            return SentimentType.NEGATIVE
        else:
            return SentimentType.NEUTRAL

    def _calculate_sentiment_shift(self, asset_symbol: str, timeframe: str = "24h") -> Optional[float]:
        """
        Calculate the shift in sentiment for an asset over a timeframe.

        Args:
            asset_symbol: Symbol of the asset
            timeframe: Timeframe for the shift calculation

        Returns:
            Sentiment shift as a float, or None if insufficient data
        """
        if asset_symbol not in self.historical_data:
            return None

        data = self.historical_data[asset_symbol]

        # Filter by timeframe
        if timeframe == "24h":
            cutoff = datetime.now() - timedelta(hours=24)
        elif timeframe == "7d":
            cutoff = datetime.now() - timedelta(days=7)
        else:
            cutoff = datetime.now() - timedelta(hours=24)  # Default to 24h

        recent_data = [d for d in data if d.timestamp > cutoff]

        if len(recent_data) < 2:
            return None

        # Sort by timestamp
        recent_data.sort(key=lambda x: x.timestamp)

        # Split into two halves
        mid_point = len(recent_data) // 2
        first_half = recent_data[:mid_point]
        second_half = recent_data[mid_point:]

        # Calculate average sentiment for each half
        first_avg = sum(d.sentiment_score for d in first_half) / len(first_half)
        second_avg = sum(d.sentiment_score for d in second_half) / len(second_half)

        # Calculate shift
        shift = second_avg - first_avg

        return shift

    def _calculate_volume_change(self, asset_symbol: str, timeframe: str = "24h") -> Optional[float]:
        """
        Calculate the change in mention volume for an asset over a timeframe.

        Args:
            asset_symbol: Symbol of the asset
            timeframe: Timeframe for the volume calculation

        Returns:
            Volume change as a percentage, or None if insufficient data
        """
        if asset_symbol not in self.historical_data:
            return None

        data = self.historical_data[asset_symbol]

        # Filter by timeframe
        if timeframe == "24h":
            cutoff = datetime.now() - timedelta(hours=24)
        elif timeframe == "7d":
            cutoff = datetime.now() - timedelta(days=7)
        else:
            cutoff = datetime.now() - timedelta(hours=24)  # Default to 24h

        recent_data = [d for d in data if d.timestamp > cutoff]

        if len(recent_data) < 2:
            return None

        # Sort by timestamp
        recent_data.sort(key=lambda x: x.timestamp)

        # Split into two halves
        mid_point = len(recent_data) // 2
        first_half = recent_data[:mid_point]
        second_half = recent_data[mid_point:]

        # Calculate average volume for each half
        first_avg = sum(d.volume for d in first_half) / len(first_half)
        second_avg = sum(d.volume for d in second_half) / len(second_half)

        # Calculate change
        if first_avg == 0:
            return None

        change = (second_avg - first_avg) / first_avg

        return change

    def generate_signals(self, sentiment_data: List[SentimentData]) -> List[SentimentSignal]:
        """
        Generate trading signals from sentiment data.

        Args:
            sentiment_data: List of sentiment data objects

        Returns:
            List of sentiment signals
        """
        signals = []

        # Group sentiment data by asset
        asset_sentiment = {}
        for data in sentiment_data:
            if data.asset_symbol not in asset_sentiment:
                asset_sentiment[data.asset_symbol] = []
            asset_sentiment[data.asset_symbol].append(data)

        # Generate signals for each asset
        for asset_symbol, data_list in asset_sentiment.items():
            try:
                # Find the asset details
                asset = next((a for a in self.tracked_assets if a['symbol'] == asset_symbol), None)
                if not asset:
                    continue

                # Calculate combined sentiment score
                combined_score = sum(d.sentiment_score for d in data_list) / len(data_list)

                # Calculate sentiment shift
                sentiment_shift = self._calculate_sentiment_shift(asset_symbol)

                # Calculate volume change
                volume_change = self._calculate_volume_change(asset_symbol)

                # 1. Sentiment Shift Signal
                if sentiment_shift is not None and abs(sentiment_shift) > 0.3:
                    direction = "bullish" if sentiment_shift > 0 else "bearish"
                    strength = min(1.0, abs(sentiment_shift) * 2)  # Scale up to 1.0

                    signal = SentimentSignal(
                        id=f"sentiment_shift_{asset_symbol}_{uuid.uuid4()}",
                        asset_id=asset['id'],
                        asset_symbol=asset_symbol,
                        signal_type="sentiment_shift",
                        direction=direction,
                        strength=strength,
                        confidence=0.7,
                        timestamp=datetime.now(),
                        expiration=datetime.now() + timedelta(hours=24),
                        sources=[d.source for d in data_list],
                        description=f"Significant {direction} sentiment shift detected for {asset_symbol}"
                    )
                    signals.append(signal)

                # 2. Volume Spike Signal
                if volume_change is not None and volume_change > 1.0:  # 100% increase
                    direction = "bullish" if combined_score > 0 else "bearish"
                    strength = min(1.0, volume_change / 3)  # Scale up to 1.0

                    signal = SentimentSignal(
                        id=f"volume_spike_{asset_symbol}_{uuid.uuid4()}",
                        asset_id=asset['id'],
                        asset_symbol=asset_symbol,
                        signal_type="volume_spike",
                        direction=direction,
                        strength=strength,
                        confidence=0.65,
                        timestamp=datetime.now(),
                        expiration=datetime.now() + timedelta(hours=12),
                        sources=[d.source for d in data_list],
                        description=f"Significant increase in {asset_symbol} mentions with {direction} sentiment"
                    )
                    signals.append(signal)

                # 3. Strong Consensus Signal
                if abs(combined_score) > 0.6 and len(data_list) >= 3:
                    direction = "bullish" if combined_score > 0 else "bearish"
                    strength = min(1.0, abs(combined_score) * 1.5)  # Scale up to 1.0

                    signal = SentimentSignal(
                        id=f"strong_consensus_{asset_symbol}_{uuid.uuid4()}",
                        asset_id=asset['id'],
                        asset_symbol=asset_symbol,
                        signal_type="strong_consensus",
                        direction=direction,
                        strength=strength,
                        confidence=0.8,
                        timestamp=datetime.now(),
                        expiration=datetime.now() + timedelta(hours=48),
                        sources=[d.source for d in data_list],
                        description=f"Strong {direction} consensus across multiple sources for {asset_symbol}"
                    )
                    signals.append(signal)

            except Exception as e:
                logger.error(f"Error generating signals for {asset_symbol}: {str(e)}")

        logger.info(f"Generated {len(signals)} sentiment signals")
        return signals

    async def process_and_send_to_mcp(self) -> bool:
        """
        Process sentiment data and send to MCP server.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Collect sentiment data
            sentiment_data = await self.collect_data()
            if not sentiment_data:
                logger.warning("No sentiment data collected")
                return False

            # Generate signals
            signals = self.generate_signals(sentiment_data)
            if not signals:
                logger.warning("No sentiment signals generated")
                return False

            # Send sentiment data to MCP
            for data in sentiment_data:
                context_item = {
                    "id": data.id,
                    "type": "sentiment_data",
                    "content": {
                        "asset_id": data.asset_id,
                        "asset_symbol": data.asset_symbol,
                        "source": data.source,
                        "sentiment_type": data.sentiment_type,
                        "sentiment_score": data.sentiment_score,
                        "volume": data.volume,
                        "timeframe": data.timeframe,
                        "keywords": data.keywords,
                        "topics": data.topics
                    },
                    "timestamp": data.timestamp.isoformat(),
                    "source": str(data.source),
                    "confidence": 0.7
                }

                await self.mcp_client.add_context(context_item)

            # Send signals to MCP
            for signal in signals:
                context_item = {
                    "id": signal.id,
                    "type": "sentiment_signal",
                    "content": {
                        "asset_id": signal.asset_id,
                        "asset_symbol": signal.asset_symbol,
                        "signal_type": signal.signal_type,
                        "direction": signal.direction,
                        "strength": signal.strength,
                        "sources": [str(s) for s in signal.sources],
                        "description": signal.description
                    },
                    "timestamp": signal.timestamp.isoformat(),
                    "source": "sentiment_analyzer",
                    "confidence": signal.confidence
                }

                await self.mcp_client.add_context(context_item)

            logger.info(f"Successfully sent {len(sentiment_data)} sentiment data points and {len(signals)} signals to MCP")
            return True

        except Exception as e:
            logger.error(f"Error processing and sending sentiment data: {str(e)}")
            return False
