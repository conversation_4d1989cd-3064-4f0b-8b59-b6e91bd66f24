{"name": "@emotion/babel-plugin", "version": "11.13.5", "description": "A recommended babel preprocessing plugin for emotion, The Next Generation of CSS-in-JS.", "main": "dist/emotion-babel-plugin.cjs.js", "module": "dist/emotion-babel-plugin.esm.js", "exports": {".": {"types": {"import": "./dist/emotion-babel-plugin.cjs.mjs", "default": "./dist/emotion-babel-plugin.cjs.js"}, "module": "./dist/emotion-babel-plugin.esm.js", "import": "./dist/emotion-babel-plugin.cjs.mjs", "default": "./dist/emotion-babel-plugin.cjs.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "dist"], "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}, "devDependencies": {"@babel/core": "^7.18.5", "babel-check-duplicated-nodes": "^1.0.0"}, "author": "<PERSON><PERSON>", "homepage": "https://emotion.sh", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/babel-plugin", "keywords": ["styles", "emotion", "react", "css", "css-in-js"], "bugs": {"url": "https://github.com/emotion-js/emotion/issues"}}