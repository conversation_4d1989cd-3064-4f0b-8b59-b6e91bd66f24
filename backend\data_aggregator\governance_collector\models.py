"""
Data models for governance data collection and processing.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional


@dataclass
class GovernanceProposal:
    """Represents a governance proposal from any platform."""
    
    id: str
    title: str
    description: str
    platform: str  # e.g., "snapshot", "tally", "compound"
    space_id: str  # The DAO/space ID
    space_name: str  # The DAO/space name
    state: str  # e.g., "active", "closed", "pending"
    created_at: datetime
    start_date: datetime
    end_date: datetime
    author: str
    choices: List[str] = field(default_factory=list)
    scores: List[float] = field(default_factory=list)
    scores_total: float = 0.0
    votes_count: int = 0
    proposal_type: Optional[str] = None  # e.g., "parameter change", "funding", "upgrade"
    raw_data: Dict[str, Any] = field(default_factory=dict)  # Original API response
    
    @property
    def is_active(self) -> bool:
        """Check if the proposal is currently active."""
        now = datetime.now()
        return self.start_date <= now <= self.end_date
    
    @property
    def time_remaining(self) -> float:
        """Get the time remaining in hours."""
        if not self.is_active:
            return 0.0
        
        remaining = self.end_date - datetime.now()
        return remaining.total_seconds() / 3600  # Convert to hours


@dataclass
class VotingData:
    """Represents voting data for a proposal."""
    
    proposal_id: str
    voter_address: str
    choice: Any  # Could be int, string, or complex structure depending on voting system
    voting_power: float
    timestamp: datetime
    raw_data: Dict[str, Any] = field(default_factory=dict)  # Original API response


@dataclass
class GovernanceSignal:
    """Represents a trading signal derived from governance activity."""
    
    id: str
    source: str  # e.g., "snapshot", "tally"
    proposal_id: str
    signal_type: str  # e.g., "high_participation", "whale_voting", "contentious_proposal"
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    assets_affected: List[str]  # List of asset symbols
    created_at: datetime
    description: str
    metadata: Dict[str, Any] = field(default_factory=dict)
