"""
On-Chain Data Collection Script

This script collects on-chain metrics, analyzes them, and generates trading signals.
It can be run as a standalone script or scheduled to run periodically.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'logs', 'onchain_collection.log'))
    ]
)
logger = logging.getLogger('onchain_collection')

# Create logs directory if it doesn't exist
os.makedirs(os.path.join(os.path.dirname(__file__), 'logs'), exist_ok=True)

# Add the project root to the Python path
sys.path.append(os.path.dirname(__file__))

# Import the necessary modules
try:
    from data_aggregator.onchain_metrics import OnChainMetricsCollector, main as collect_metrics
    from ml.onchain_analyzer import OnChainAnalyzer, main as analyze_metrics
    from signal_generator.onchain_signals import OnChainSignalGenerator, main as generate_signals
    
    logger.info("Successfully imported on-chain modules")
except ImportError as e:
    logger.error(f"Error importing on-chain modules: {str(e)}")
    logger.info("Creating necessary directories...")
    
    # Create necessary directories
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data_aggregator'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'ml'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'signal_generator'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'onchain'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'analysis'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'signals'), exist_ok=True)
    
    logger.info("Please restart the script after ensuring all modules are in place")
    sys.exit(1)

async def run_collection_pipeline():
    """Run the complete on-chain data collection pipeline."""
    try:
        logger.info("Starting on-chain data collection pipeline")
        
        # Step 1: Collect on-chain metrics
        logger.info("Step 1: Collecting on-chain metrics")
        await collect_metrics()
        
        # Step 2: Analyze on-chain metrics
        logger.info("Step 2: Analyzing on-chain metrics")
        analyze_metrics()
        
        # Step 3: Generate trading signals
        logger.info("Step 3: Generating trading signals")
        generate_signals()
        
        logger.info("On-chain data collection pipeline completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error in on-chain data collection pipeline: {str(e)}")
        return False

def main():
    """Main function to run the on-chain data collection pipeline."""
    start_time = datetime.now()
    logger.info(f"Starting on-chain data collection at {start_time}")
    
    # Run the collection pipeline
    success = asyncio.run(run_collection_pipeline())
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    if success:
        logger.info(f"On-chain data collection completed successfully in {duration}")
    else:
        logger.error(f"On-chain data collection failed after {duration}")
    
    return success

if __name__ == "__main__":
    main()
