"""
CryptoCompare API Client

This module provides functions to fetch cryptocurrency data from the CryptoCompare API.
"""

import os
import json
import logging
import time
import requests
from datetime import datetime, timedelta
import websocket
import threading
import ssl

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger('cryptocompare_client')

# Data storage path
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'market')
os.makedirs(DATA_DIR, exist_ok=True)

# Import API key from config
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
try:
    from config.api_keys import CRYPTOCOMPARE_API_KEY
except ImportError:
    CRYPTOCOMPARE_API_KEY = "****************************************************************"  # Fallback to hardcoded key

# API configuration
API_KEY = CRYPTOCOMPARE_API_KEY
BASE_URL = "https://min-api.cryptocompare.com"
WEBSOCKET_URL = "wss://streamer.cryptocompare.com/v2"

# Supported coins
SUPPORTED_COINS = ["BTC", "ETH", "SOL", "ADA", "DOT", "AVAX"]

# Supported timeframes
TIMEFRAMES = {
    "1m": {"api_value": "minute", "limit": 2000, "aggregate": 1},
    "5m": {"api_value": "minute", "limit": 2000, "aggregate": 5},
    "15m": {"api_value": "minute", "limit": 2000, "aggregate": 15},
    "30m": {"api_value": "minute", "limit": 2000, "aggregate": 30},
    "1h": {"api_value": "hour", "limit": 2000, "aggregate": 1},
    "2h": {"api_value": "hour", "limit": 2000, "aggregate": 2},
    "4h": {"api_value": "hour", "limit": 2000, "aggregate": 4},
    "1d": {"api_value": "day", "limit": 2000, "aggregate": 1},
    "1w": {"api_value": "day", "limit": 2000, "aggregate": 7},
}

class CryptoCompareClient:
    """Client for fetching data from CryptoCompare API."""

    def __init__(self, api_key=None):
        """Initialize the CryptoCompare API client."""
        self.api_key = api_key or API_KEY
        self.base_url = BASE_URL
        self.headers = {
            "authorization": f"Apikey {self.api_key}"
        }
        self.websocket = None
        self.websocket_thread = None
        self.websocket_callbacks = {}
        self.websocket_running = False

        logger.info(f"Initialized CryptoCompare client")

    def _make_request(self, endpoint, params=None):
        """
        Make a request to the CryptoCompare API.

        Args:
            endpoint: API endpoint to call
            params: Query parameters

        Returns:
            Response data as JSON
        """
        try:
            url = f"{self.base_url}/{endpoint}"
            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                if data.get("Response") == "Error":
                    logger.error(f"API error: {data.get('Message')}")
                    return None
                return data
            else:
                logger.error(f"API request failed with status code {response.status_code}: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error making request to {endpoint}: {str(e)}")
            return None

    def get_price(self, from_symbols, to_symbols=None):
        """
        Get current price for one or more cryptocurrencies.

        Args:
            from_symbols: List of cryptocurrency symbols or single symbol
            to_symbols: List of currency symbols or single symbol (default: USD)

        Returns:
            Dictionary with price data
        """
        try:
            # Format symbols
            if isinstance(from_symbols, list):
                from_str = ",".join(from_symbols)
            else:
                from_str = from_symbols

            if to_symbols is None:
                to_symbols = ["USD"]

            if isinstance(to_symbols, list):
                to_str = ",".join(to_symbols)
            else:
                to_str = to_symbols

            # Set up parameters
            params = {
                "fsyms": from_str,
                "tsyms": to_str
            }

            # Make request
            response = self._make_request("data/pricemultifull", params)

            if not response or "RAW" not in response:
                logger.error("Failed to get price data")
                return None

            return response["RAW"]
        except Exception as e:
            logger.error(f"Error getting price: {str(e)}")
            return None

    def get_historical_data(self, symbol, timeframe="1d", limit=100, to_symbol="USD"):
        """
        Get historical OHLCV data for a cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol
            timeframe: Timeframe (1m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w)
            limit: Number of data points to return
            to_symbol: Currency to convert to

        Returns:
            List of OHLCV data points
        """
        try:
            # Get timeframe parameters
            if timeframe not in TIMEFRAMES:
                logger.error(f"Invalid timeframe: {timeframe}")
                return None

            tf_params = TIMEFRAMES[timeframe]

            # Set up parameters
            params = {
                "fsym": symbol,
                "tsym": to_symbol,
                "limit": min(limit, tf_params["limit"]),
                "aggregate": tf_params["aggregate"],
                "e": "CCCAGG"  # Cryptocurrency aggregated data
            }

            # Make request
            endpoint = f"data/v2/histo{tf_params['api_value']}"
            response = self._make_request(endpoint, params)

            if not response or "Data" not in response or "Data" not in response["Data"]:
                logger.error(f"Failed to get historical data for {symbol}")
                return None

            return response["Data"]["Data"]
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {str(e)}")
            return None

    def get_top_exchanges(self, symbol, limit=10, to_symbol="USD"):
        """
        Get top exchanges for a cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol
            limit: Number of exchanges to return
            to_symbol: Currency to convert to

        Returns:
            List of exchanges
        """
        try:
            # Set up parameters
            params = {
                "fsym": symbol,
                "tsym": to_symbol,
                "limit": limit
            }

            # Make request
            response = self._make_request("data/top/exchanges", params)

            if not response or "Data" not in response:
                logger.error(f"Failed to get top exchanges for {symbol}")
                return None

            return response["Data"]
        except Exception as e:
            logger.error(f"Error getting top exchanges for {symbol}: {str(e)}")
            return None

    def get_top_pairs(self, symbol, limit=10):
        """
        Get top trading pairs for a cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol
            limit: Number of pairs to return

        Returns:
            List of trading pairs
        """
        try:
            # Set up parameters
            params = {
                "fsym": symbol,
                "limit": limit
            }

            # Make request
            response = self._make_request("data/top/pairs", params)

            if not response or "Data" not in response:
                logger.error(f"Failed to get top pairs for {symbol}")
                return None

            return response["Data"]
        except Exception as e:
            logger.error(f"Error getting top pairs for {symbol}: {str(e)}")
            return None

    def get_news(self, categories=None, limit=10):
        """
        Get cryptocurrency news.

        Args:
            categories: List of news categories
            limit: Number of news items to return

        Returns:
            List of news items
        """
        try:
            # Set up parameters
            params = {
                "lang": "EN",
                "sortOrder": "popular",
                "limit": limit
            }

            if categories:
                if isinstance(categories, list):
                    params["categories"] = ",".join(categories)
                else:
                    params["categories"] = categories

            # Make request
            response = self._make_request("data/v2/news/", params)

            if not response or "Data" not in response:
                logger.error("Failed to get news")
                return None

            return response["Data"]
        except Exception as e:
            logger.error(f"Error getting news: {str(e)}")
            return None

    def get_social_stats(self, symbol):
        """
        Get social media stats for a cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            Dictionary with social stats
        """
        try:
            # Set up parameters
            params = {
                "coinId": symbol
            }

            # Make request
            response = self._make_request("data/social/coin/latest", params)

            if not response or "Data" not in response:
                logger.error(f"Failed to get social stats for {symbol}")
                return None

            return response["Data"]
        except Exception as e:
            logger.error(f"Error getting social stats for {symbol}: {str(e)}")
            return None

    def get_technical_indicators(self, symbol, timeframe="1d", limit=10, to_symbol="USD"):
        """
        Get technical indicators for a cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol
            timeframe: Timeframe (1d, 1h, etc.)
            limit: Number of data points to return
            to_symbol: Currency to convert to

        Returns:
            Dictionary with technical indicators
        """
        try:
            # First get historical data
            historical_data = self.get_historical_data(symbol, timeframe, limit + 50, to_symbol)

            if not historical_data:
                logger.error(f"Failed to get historical data for technical indicators")
                return None

            # Calculate indicators
            indicators = {}

            # Calculate RSI (14)
            indicators["rsi"] = self._calculate_rsi(historical_data, 14)

            # Calculate MACD (12, 26, 9)
            indicators["macd"] = self._calculate_macd(historical_data, 12, 26, 9)

            # Calculate Bollinger Bands (20, 2)
            indicators["bollinger"] = self._calculate_bollinger_bands(historical_data, 20, 2)

            # Limit to requested number of data points
            for key in indicators:
                if isinstance(indicators[key], list):
                    indicators[key] = indicators[key][-limit:]

            return indicators
        except Exception as e:
            logger.error(f"Error calculating technical indicators for {symbol}: {str(e)}")
            return None

    def _calculate_rsi(self, data, period=14):
        """
        Calculate Relative Strength Index (RSI).

        Args:
            data: List of OHLCV data points
            period: RSI period

        Returns:
            List of RSI values
        """
        try:
            if len(data) < period + 1:
                return None

            # Extract closing prices
            closes = [point["close"] for point in data]

            # Calculate price changes
            deltas = [closes[i] - closes[i-1] for i in range(1, len(closes))]

            # Calculate gains and losses
            gains = [delta if delta > 0 else 0 for delta in deltas]
            losses = [-delta if delta < 0 else 0 for delta in deltas]

            # Calculate average gains and losses
            avg_gain = sum(gains[:period]) / period
            avg_loss = sum(losses[:period]) / period

            # Calculate RSI
            rsi_values = []

            for i in range(period, len(deltas)):
                avg_gain = (avg_gain * (period - 1) + gains[i]) / period
                avg_loss = (avg_loss * (period - 1) + losses[i]) / period

                if avg_loss == 0:
                    rsi = 100
                else:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))

                rsi_values.append({
                    "time": data[i+1]["time"],
                    "value": rsi
                })

            return rsi_values
        except Exception as e:
            logger.error(f"Error calculating RSI: {str(e)}")
            return None

    def _calculate_macd(self, data, fast_period=12, slow_period=26, signal_period=9):
        """
        Calculate Moving Average Convergence Divergence (MACD).

        Args:
            data: List of OHLCV data points
            fast_period: Fast EMA period
            slow_period: Slow EMA period
            signal_period: Signal EMA period

        Returns:
            Dictionary with MACD values
        """
        try:
            if len(data) < slow_period + signal_period:
                return None

            # Extract closing prices
            closes = [point["close"] for point in data]

            # Calculate EMAs
            fast_ema = self._calculate_ema(closes, fast_period)
            slow_ema = self._calculate_ema(closes, slow_period)

            # Calculate MACD line
            macd_line = [fast_ema[i] - slow_ema[i] for i in range(len(slow_ema))]

            # Calculate signal line
            signal_line = self._calculate_ema(macd_line, signal_period)

            # Calculate histogram
            histogram = [macd_line[i] - signal_line[i] for i in range(len(signal_line))]

            # Format results
            result = []
            offset = slow_period + signal_period - 2

            for i in range(len(signal_line)):
                result.append({
                    "time": data[i + offset]["time"],
                    "macd": macd_line[i + signal_period - 1],
                    "signal": signal_line[i],
                    "histogram": histogram[i]
                })

            return result
        except Exception as e:
            logger.error(f"Error calculating MACD: {str(e)}")
            return None

    def _calculate_ema(self, data, period):
        """
        Calculate Exponential Moving Average (EMA).

        Args:
            data: List of values
            period: EMA period

        Returns:
            List of EMA values
        """
        if len(data) < period:
            return None

        # Calculate multiplier
        multiplier = 2 / (period + 1)

        # Calculate initial SMA
        sma = sum(data[:period]) / period

        # Calculate EMAs
        emas = [sma]

        for i in range(period, len(data)):
            ema = (data[i] - emas[-1]) * multiplier + emas[-1]
            emas.append(ema)

        return emas

    def _calculate_bollinger_bands(self, data, period=20, std_dev=2):
        """
        Calculate Bollinger Bands.

        Args:
            data: List of OHLCV data points
            period: SMA period
            std_dev: Standard deviation multiplier

        Returns:
            List of Bollinger Bands values
        """
        try:
            if len(data) < period:
                return None

            # Extract closing prices
            closes = [point["close"] for point in data]

            # Calculate Bollinger Bands
            result = []

            for i in range(period - 1, len(closes)):
                # Get subset of data
                subset = closes[i - (period - 1):i + 1]

                # Calculate SMA
                sma = sum(subset) / period

                # Calculate standard deviation
                variance = sum([(x - sma) ** 2 for x in subset]) / period
                std = variance ** 0.5

                # Calculate upper and lower bands
                upper = sma + (std_dev * std)
                lower = sma - (std_dev * std)

                result.append({
                    "time": data[i]["time"],
                    "middle": sma,
                    "upper": upper,
                    "lower": lower
                })

            return result
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {str(e)}")
            return None

    def start_websocket(self, symbols, callback=None):
        """
        Start a websocket connection for real-time price updates.

        Args:
            symbols: List of cryptocurrency symbols
            callback: Function to call when new data is received

        Returns:
            True if websocket started successfully, False otherwise
        """
        try:
            if self.websocket_running:
                logger.warning("Websocket is already running")
                return True

            # Format symbols
            if isinstance(symbols, str):
                symbols = [symbols]

            # Store callback
            if callback:
                for symbol in symbols:
                    self.websocket_callbacks[symbol] = callback

            # Define websocket callbacks
            def on_message(ws, message):
                try:
                    data = json.loads(message)

                    if "TYPE" in data and data["TYPE"] == "2":
                        # Price update
                        symbol = data.get("FROMSYMBOL")
                        price = data.get("PRICE")

                        if symbol and price and symbol in self.websocket_callbacks:
                            self.websocket_callbacks[symbol](symbol, price, data)
                except Exception as e:
                    logger.error(f"Error processing websocket message: {str(e)}")

            def on_error(ws, error):
                logger.error(f"Websocket error: {str(error)}")

            def on_close(ws, close_status_code, close_msg):
                logger.info("Websocket connection closed")
                self.websocket_running = False

            def on_open(ws):
                logger.info("Websocket connection opened")
                self.websocket_running = True

                # Subscribe to price updates
                for symbol in symbols:
                    sub_message = {
                        "action": "SubAdd",
                        "subs": [f"2~CCCAGG~{symbol}~USD"]
                    }
                    ws.send(json.dumps(sub_message))

            # Create websocket
            self.websocket = websocket.WebSocketApp(
                WEBSOCKET_URL,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close,
                on_open=on_open,
                header={"authorization": f"Apikey {self.api_key}"}
            )

            # Start websocket in a separate thread
            self.websocket_thread = threading.Thread(target=self.websocket.run_forever, kwargs={
                "sslopt": {"cert_reqs": ssl.CERT_NONE}
            })
            self.websocket_thread.daemon = True
            self.websocket_thread.start()

            return True
        except Exception as e:
            logger.error(f"Error starting websocket: {str(e)}")
            return False

    def stop_websocket(self):
        """
        Stop the websocket connection.

        Returns:
            True if websocket stopped successfully, False otherwise
        """
        try:
            if not self.websocket_running:
                logger.warning("Websocket is not running")
                return True

            # Close websocket
            if self.websocket:
                self.websocket.close()

            # Clear callbacks
            self.websocket_callbacks = {}
            self.websocket_running = False

            return True
        except Exception as e:
            logger.error(f"Error stopping websocket: {str(e)}")
            return False

    def save_market_data(self, symbols=None):
        """
        Fetch and save market data for specific cryptocurrencies.

        Args:
            symbols: List of cryptocurrency symbols (default: SUPPORTED_COINS)

        Returns:
            Dictionary with saved data paths
        """
        try:
            # Set default symbols if not provided
            if not symbols:
                symbols = SUPPORTED_COINS

            # Get current timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Get price data
            price_data = self.get_price(symbols)
            if not price_data:
                logger.error("Failed to get price data")
                return {}

            saved_files = {}

            # Save price data for each symbol
            for symbol in symbols:
                if symbol not in price_data or "USD" not in price_data[symbol]:
                    logger.warning(f"No price data found for {symbol}")
                    continue

                # Create directory for this coin if it doesn't exist
                coin_dir = os.path.join(DATA_DIR, symbol.lower())
                os.makedirs(coin_dir, exist_ok=True)

                # Save price data
                price_file = os.path.join(coin_dir, f"cc_price_{timestamp}.json")
                with open(price_file, 'w') as f:
                    json.dump(price_data[symbol], f, indent=2)

                # Also save as latest
                latest_price_file = os.path.join(coin_dir, "cc_price_latest.json")
                with open(latest_price_file, 'w') as f:
                    json.dump(price_data[symbol], f, indent=2)

                saved_files[symbol] = {
                    "price": price_file
                }

                # Get and save historical data (1d)
                historical_data = self.get_historical_data(symbol, "1d", 30)
                if historical_data:
                    history_file = os.path.join(coin_dir, f"cc_history_1d_{timestamp}.json")
                    with open(history_file, 'w') as f:
                        json.dump(historical_data, f, indent=2)

                    # Also save as latest
                    latest_history_file = os.path.join(coin_dir, "cc_history_1d_latest.json")
                    with open(latest_history_file, 'w') as f:
                        json.dump(historical_data, f, indent=2)

                    saved_files[symbol]["history_1d"] = history_file

                # Get and save top exchanges
                exchanges = self.get_top_exchanges(symbol)
                if exchanges:
                    exchanges_file = os.path.join(coin_dir, f"cc_exchanges_{timestamp}.json")
                    with open(exchanges_file, 'w') as f:
                        json.dump(exchanges, f, indent=2)

                    # Also save as latest
                    latest_exchanges_file = os.path.join(coin_dir, "cc_exchanges_latest.json")
                    with open(latest_exchanges_file, 'w') as f:
                        json.dump(exchanges, f, indent=2)

                    saved_files[symbol]["exchanges"] = exchanges_file

                # Get and save technical indicators
                indicators = self.get_technical_indicators(symbol)
                if indicators:
                    indicators_file = os.path.join(coin_dir, f"cc_indicators_{timestamp}.json")
                    with open(indicators_file, 'w') as f:
                        json.dump(indicators, f, indent=2)

                    # Also save as latest
                    latest_indicators_file = os.path.join(coin_dir, "cc_indicators_latest.json")
                    with open(latest_indicators_file, 'w') as f:
                        json.dump(indicators, f, indent=2)

                    saved_files[symbol]["indicators"] = indicators_file

            # Get and save news
            news = self.get_news(limit=20)
            if news:
                news_dir = os.path.join(DATA_DIR, "news")
                os.makedirs(news_dir, exist_ok=True)

                news_file = os.path.join(news_dir, f"cc_news_{timestamp}.json")
                with open(news_file, 'w') as f:
                    json.dump(news, f, indent=2)

                # Also save as latest
                latest_news_file = os.path.join(news_dir, "cc_news_latest.json")
                with open(latest_news_file, 'w') as f:
                    json.dump(news, f, indent=2)

                saved_files["news"] = news_file

            logger.info(f"Saved market data for {len(saved_files)} coins")
            return saved_files
        except Exception as e:
            logger.error(f"Error saving market data: {str(e)}")
            return {}

    def get_latest_market_data(self, symbol):
        """
        Get the latest saved market data for a specific cryptocurrency.

        Args:
            symbol: Cryptocurrency symbol

        Returns:
            Dictionary with latest market data
        """
        try:
            coin_dir = os.path.join(DATA_DIR, symbol.lower())

            # Check if directory exists
            if not os.path.exists(coin_dir):
                logger.warning(f"No data directory found for {symbol}")
                return {}

            result = {}

            # Load latest price data
            price_file = os.path.join(coin_dir, "cc_price_latest.json")
            if os.path.exists(price_file):
                with open(price_file, 'r') as f:
                    result["price"] = json.load(f)

            # Load latest historical data
            history_file = os.path.join(coin_dir, "cc_history_1d_latest.json")
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    result["history"] = json.load(f)

            # Load latest exchanges data
            exchanges_file = os.path.join(coin_dir, "cc_exchanges_latest.json")
            if os.path.exists(exchanges_file):
                with open(exchanges_file, 'r') as f:
                    result["exchanges"] = json.load(f)

            # Load latest technical indicators
            indicators_file = os.path.join(coin_dir, "cc_indicators_latest.json")
            if os.path.exists(indicators_file):
                with open(indicators_file, 'r') as f:
                    result["indicators"] = json.load(f)

            return result
        except Exception as e:
            logger.error(f"Error getting latest market data for {symbol}: {str(e)}")
            return {}

    def get_latest_news(self):
        """
        Get the latest saved news.

        Returns:
            List of news items
        """
        try:
            news_dir = os.path.join(DATA_DIR, "news")
            news_file = os.path.join(news_dir, "cc_news_latest.json")

            if not os.path.exists(news_file):
                logger.warning("No news file found")
                return []

            with open(news_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error getting latest news: {str(e)}")
            return []

# Singleton instance
_instance = None

def get_instance():
    """Get the singleton instance of CryptoCompareClient."""
    global _instance
    if _instance is None:
        _instance = CryptoCompareClient()
    return _instance

def main():
    """Main function to test the CryptoCompare client."""
    client = CryptoCompareClient()

    # Save market data for supported coins
    results = client.save_market_data()

    # Print results
    for symbol, files in results.items():
        if files:
            logger.info(f"Saved data for {symbol}")
        else:
            logger.warning(f"Failed to save data for {symbol}")

    return True

if __name__ == "__main__":
    main()
