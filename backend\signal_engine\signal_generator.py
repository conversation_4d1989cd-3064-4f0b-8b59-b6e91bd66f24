"""
Trading Signal Generator

This module generates trading signals based on context information from the MCP server.
It implements various strategies for signal generation, including technical indicators
and sentiment analysis.
"""

import logging
import uuid
import asyncio
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Any, Optional

import httpx
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
MCP_SERVER_URL = "http://localhost:8002"  # Update with your actual MCP server URL
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds

class SignalType(str, Enum):
    """Types of trading signals."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    LONG = "long"
    SHORT = "short"

class TimeFrame(str, Enum):
    """Time frames for trading signals."""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"

class AssetType(str, Enum):
    """Types of assets."""
    CRYPTO = "crypto"
    STOCK = "stock"
    FOREX = "forex"
    COMMODITY = "commodity"
    INDEX = "index"

class Signal(BaseModel):
    """Model for trading signals."""
    id: str
    asset_id: str
    asset_symbol: str
    asset_name: str
    asset_type: AssetType
    signal_type: SignalType
    time_frame: TimeFrame
    confidence: float  # 0.0 to 1.0
    price: float
    timestamp: datetime
    expiration: datetime
    indicators: Dict[str, Any]
    sentiment: Optional[float] = None  # -1.0 to 1.0
    context_ids: List[str]  # IDs of context items used to generate this signal

class ContextQuery(BaseModel):
    """Query parameters for retrieving context."""
    types: Optional[List[str]] = None
    sources: Optional[List[str]] = None
    time_range: Optional[Dict[str, datetime]] = None
    limit: int = 100

class SignalGenerator:
    """Generates trading signals based on MCP context."""

    def __init__(self, mcp_server_url: str = MCP_SERVER_URL, api_token: Optional[str] = None):
        """Initialize the signal generator with MCP server URL and optional API token."""
        self.mcp_server_url = mcp_server_url
        self.api_token = api_token
        self.headers = {}
        if api_token:
            self.headers["Authorization"] = f"Bearer {api_token}"

        # Initialize governance signal processor
        from governance_signals import GovernanceSignalProcessor
        self.governance_processor = GovernanceSignalProcessor(self)

        # Initialize ML model if available
        try:
            from backend.ml.price_prediction import PricePredictionModel
            self.price_predictor = PricePredictionModel(model_type="random_forest")
            self.ml_available = True
            logger.info("ML price prediction model initialized")
        except ImportError:
            self.ml_available = False
            logger.warning("ML price prediction model not available")

        logger.info(f"Signal generator initialized with MCP server at {mcp_server_url}")

    async def fetch_context(self, query: ContextQuery) -> List[Dict[str, Any]]:
        """Fetch context information from the MCP server."""
        url = f"{self.mcp_server_url}/context/query/noauth"

        for attempt in range(MAX_RETRIES):
            try:
                async with httpx.AsyncClient() as client:
                    # Use model_dump() for newer Pydantic or dict() for older versions
                    try:
                        # Try the newer Pydantic v2 method first
                        query_data = query.model_dump()
                    except AttributeError:
                        # Fall back to the older method
                        query_data = query.dict()

                    response = await client.post(
                        url,
                        json=query_data,
                        headers=self.headers,
                        timeout=30
                    )
                    response.raise_for_status()

                    context_items = response.json()
                    logger.info(f"Fetched {len(context_items)} context items from MCP server")
                    return context_items

            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error fetching context: {e}")
                if e.response.status_code == 401:
                    logger.error("Authentication failed. Check your API token.")
                    return []

                if attempt < MAX_RETRIES - 1:
                    logger.info(f"Retrying in {RETRY_DELAY} seconds... (Attempt {attempt + 1}/{MAX_RETRIES})")
                    await asyncio.sleep(RETRY_DELAY)
                else:
                    logger.error(f"Failed to fetch context after {MAX_RETRIES} attempts")
                    return []

            except httpx.RequestError as e:
                logger.error(f"Request error fetching context: {e}")
                if attempt < MAX_RETRIES - 1:
                    logger.info(f"Retrying in {RETRY_DELAY} seconds... (Attempt {attempt + 1}/{MAX_RETRIES})")
                    await asyncio.sleep(RETRY_DELAY)
                else:
                    logger.error(f"Failed to fetch context after {MAX_RETRIES} attempts")
                    return []

            except Exception as e:
                logger.error(f"Unexpected error fetching context: {e}")
                return []

    async def generate_crypto_signals(self) -> List[Signal]:
        """Generate trading signals for cryptocurrencies."""
        # Fetch recent cryptocurrency price data
        query = ContextQuery(
            types=["crypto_price"],
            time_range={
                "start": datetime.utcnow() - timedelta(hours=24)
            },
            limit=100
        )

        context_items = await self.fetch_context(query)
        if not context_items:
            logger.warning("No cryptocurrency context items found")
            return []

        # Group context items by cryptocurrency
        crypto_data = {}
        for item in context_items:
            coin_id = item["content"]["coin_id"]
            if coin_id not in crypto_data:
                crypto_data[coin_id] = []
            crypto_data[coin_id].append(item)

        # Sort each cryptocurrency's data by timestamp
        for coin_id in crypto_data:
            crypto_data[coin_id].sort(key=lambda x: x["timestamp"])

        # Generate price-based signals
        price_signals = []

        # Generate signals for each cryptocurrency
        signals = []
        for coin_id, items in crypto_data.items():
            if len(items) < 2:
                continue  # Need at least 2 data points to generate a signal

            # Get the most recent data point
            latest = items[-1]

            # Calculate simple moving averages (if enough data points)
            sma_5 = self._calculate_sma(items, 5)
            sma_10 = self._calculate_sma(items, 10)

            # Calculate RSI (if enough data points)
            rsi = self._calculate_rsi(items, 14)

            # Determine signal type based on indicators
            signal_type = SignalType.HOLD
            confidence = 0.5

            # Simple strategy: SMA crossover
            if sma_5 and sma_10:
                if sma_5 > sma_10:
                    signal_type = SignalType.BUY
                    confidence = min(0.9, 0.5 + (sma_5 - sma_10) / sma_10)
                elif sma_5 < sma_10:
                    signal_type = SignalType.SELL
                    confidence = min(0.9, 0.5 + (sma_10 - sma_5) / sma_5)

            # Adjust confidence based on RSI
            if rsi is not None:
                if rsi < 30 and signal_type == SignalType.SELL:
                    confidence *= 0.8  # Reduce confidence for sell signals in oversold conditions
                elif rsi > 70 and signal_type == SignalType.BUY:
                    confidence *= 0.8  # Reduce confidence for buy signals in overbought conditions

            # Create signal
            signal = Signal(
                id=f"signal_{coin_id}_{uuid.uuid4()}",
                asset_id=coin_id,
                asset_symbol=latest["content"]["symbol"],
                asset_name=latest["content"]["name"],
                asset_type=AssetType.CRYPTO,
                signal_type=signal_type,
                time_frame=TimeFrame.HOUR_1,
                confidence=confidence,
                price=latest["content"]["price_usd"],
                timestamp=datetime.utcnow(),
                expiration=datetime.utcnow() + timedelta(hours=1),
                indicators={
                    "sma_5": sma_5,
                    "sma_10": sma_10,
                    "rsi": rsi,
                    "price_change_24h": latest["content"]["price_change_percentage_24h"]
                },
                context_ids=[item["id"] for item in items[-5:]]  # Include the 5 most recent context items
            )

            price_signals.append(signal)
            logger.info(f"Generated {signal_type} signal for {latest['content']['name']} with confidence {confidence:.2f}")

        # Get governance signals
        governance_signals = await self.governance_processor.get_governance_trading_signals()

        # Combine all signals
        all_signals = price_signals + governance_signals

        # Sort by timestamp (newest first)
        all_signals.sort(key=lambda x: x.timestamp, reverse=True)

        return all_signals

    def _calculate_sma(self, items: List[Dict[str, Any]], period: int) -> Optional[float]:
        """Calculate Simple Moving Average for a list of context items."""
        if len(items) < period:
            return None

        recent_items = items[-period:]
        prices = [item["content"]["price_usd"] for item in recent_items]
        return sum(prices) / period

    def _calculate_rsi(self, items: List[Dict[str, Any]], period: int) -> Optional[float]:
        """Calculate Relative Strength Index for a list of context items."""
        if len(items) <= period:
            return None

        # Get price changes
        price_changes = []
        for i in range(1, len(items)):
            prev_price = items[i-1]["content"]["price_usd"]
            curr_price = items[i]["content"]["price_usd"]
            price_changes.append(curr_price - prev_price)

        # Use only the most recent 'period' changes
        price_changes = price_changes[-period:]

        # Separate gains and losses
        gains = [change for change in price_changes if change > 0]
        losses = [abs(change) for change in price_changes if change < 0]

        if not gains:
            avg_gain = 0
        else:
            avg_gain = sum(gains) / len(gains)

        if not losses:
            avg_loss = 0
        else:
            avg_loss = sum(losses) / len(losses)

        if avg_loss == 0:
            return 100  # No losses, RSI is 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

async def main():
    """Main function for testing the signal generator."""
    import asyncio

    generator = SignalGenerator()
    signals = await generator.generate_crypto_signals()

    print(f"Generated {len(signals)} trading signals")

    # Print the first signal as an example
    if signals:
        print(f"Example signal: {signals[0].json(indent=2)}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
