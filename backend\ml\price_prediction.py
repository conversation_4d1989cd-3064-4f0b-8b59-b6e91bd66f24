"""
Price Prediction Model

This module implements machine learning models for cryptocurrency price prediction.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple

from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class PricePredictionModel:
    """Machine learning model for cryptocurrency price prediction."""
    
    def __init__(self, model_type: str = "random_forest"):
        """
        Initialize the price prediction model.
        
        Args:
            model_type: Type of model to use ("random_forest", "gradient_boosting", "linear")
        """
        self.model_type = model_type
        self.model = None
        self.feature_scaler = MinMaxScaler()
        self.target_scaler = MinMaxScaler()
        self.feature_columns = []
        self.target_column = "close"
        self.lookback_period = 10
        self.prediction_horizon = 1
        
        logger.info(f"Initialized price prediction model with type: {model_type}")
    
    def _create_model(self):
        """Create the machine learning model."""
        if self.model_type == "random_forest":
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42
            )
        elif self.model_type == "gradient_boosting":
            self.model = GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=5,
                random_state=42
            )
        elif self.model_type == "linear":
            self.model = LinearRegression()
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
    
    def _prepare_features(self, price_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare features and targets for training.
        
        Args:
            price_data: DataFrame with price data
            
        Returns:
            Tuple of (features, targets)
        """
        # Create features
        features = []
        targets = []
        
        # Use basic price and volume data as features
        self.feature_columns = ["open", "high", "low", "close", "volume"]
        
        # Add technical indicators as features
        price_data = self._add_technical_indicators(price_data)
        
        # Create sequences for time series prediction
        for i in range(self.lookback_period, len(price_data) - self.prediction_horizon):
            # Extract features from the lookback period
            feature_window = price_data.iloc[i - self.lookback_period:i][self.feature_columns].values
            feature_window = feature_window.flatten()  # Flatten the window into a 1D array
            
            # Extract target (future price)
            target_value = price_data.iloc[i + self.prediction_horizon][self.target_column]
            
            features.append(feature_window)
            targets.append(target_value)
        
        # Convert to numpy arrays
        features = np.array(features)
        targets = np.array(targets)
        
        return features, targets
    
    def _add_technical_indicators(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to the price data.
        
        Args:
            price_data: DataFrame with price data
            
        Returns:
            DataFrame with added technical indicators
        """
        df = price_data.copy()
        
        # Add moving averages
        df["sma_5"] = df["close"].rolling(window=5).mean()
        df["sma_10"] = df["close"].rolling(window=10).mean()
        df["sma_20"] = df["close"].rolling(window=20).mean()
        
        # Add exponential moving averages
        df["ema_5"] = df["close"].ewm(span=5, adjust=False).mean()
        df["ema_10"] = df["close"].ewm(span=10, adjust=False).mean()
        df["ema_20"] = df["close"].ewm(span=20, adjust=False).mean()
        
        # Add price momentum
        df["momentum_5"] = df["close"] / df["close"].shift(5) - 1
        df["momentum_10"] = df["close"] / df["close"].shift(10) - 1
        
        # Add volatility
        df["volatility_5"] = df["close"].rolling(window=5).std()
        df["volatility_10"] = df["close"].rolling(window=10).std()
        
        # Add volume indicators
        df["volume_ma_5"] = df["volume"].rolling(window=5).mean()
        df["volume_ma_10"] = df["volume"].rolling(window=10).mean()
        
        # Add price changes
        df["price_change"] = df["close"].pct_change()
        df["price_change_5"] = df["close"].pct_change(periods=5)
        
        # Add day of week (cyclical feature)
        if isinstance(df.index, pd.DatetimeIndex):
            df["day_of_week_sin"] = np.sin(2 * np.pi * df.index.dayofweek / 7)
            df["day_of_week_cos"] = np.cos(2 * np.pi * df.index.dayofweek / 7)
        
        # Fill NaN values
        df.fillna(method="bfill", inplace=True)
        
        # Update feature columns
        self.feature_columns = [
            "open", "high", "low", "close", "volume",
            "sma_5", "sma_10", "sma_20",
            "ema_5", "ema_10", "ema_20",
            "momentum_5", "momentum_10",
            "volatility_5", "volatility_10",
            "volume_ma_5", "volume_ma_10",
            "price_change", "price_change_5"
        ]
        
        # Add day of week features if available
        if "day_of_week_sin" in df.columns:
            self.feature_columns.extend(["day_of_week_sin", "day_of_week_cos"])
        
        return df
    
    def train(self, price_data: pd.DataFrame, test_size: float = 0.2) -> Dict[str, Any]:
        """
        Train the price prediction model.
        
        Args:
            price_data: DataFrame with price data
            test_size: Proportion of data to use for testing
            
        Returns:
            Dictionary with training results
        """
        # Create model
        self._create_model()
        
        # Prepare features and targets
        features, targets = self._prepare_features(price_data)
        
        # Scale features and targets
        features_scaled = self.feature_scaler.fit_transform(features)
        targets_scaled = self.target_scaler.fit_transform(targets.reshape(-1, 1)).flatten()
        
        # Split data into training and testing sets
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, targets_scaled, test_size=test_size, shuffle=False
        )
        
        # Train model
        logger.info(f"Training {self.model_type} model with {len(X_train)} samples")
        self.model.fit(X_train, y_train)
        
        # Evaluate model
        y_pred_train = self.model.predict(X_train)
        y_pred_test = self.model.predict(X_test)
        
        # Inverse transform predictions
        y_train_inv = self.target_scaler.inverse_transform(y_train.reshape(-1, 1)).flatten()
        y_test_inv = self.target_scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
        y_pred_train_inv = self.target_scaler.inverse_transform(y_pred_train.reshape(-1, 1)).flatten()
        y_pred_test_inv = self.target_scaler.inverse_transform(y_pred_test.reshape(-1, 1)).flatten()
        
        # Calculate metrics
        train_rmse = np.sqrt(mean_squared_error(y_train_inv, y_pred_train_inv))
        test_rmse = np.sqrt(mean_squared_error(y_test_inv, y_pred_test_inv))
        train_mae = mean_absolute_error(y_train_inv, y_pred_train_inv)
        test_mae = mean_absolute_error(y_test_inv, y_pred_test_inv)
        train_r2 = r2_score(y_train_inv, y_pred_train_inv)
        test_r2 = r2_score(y_test_inv, y_pred_test_inv)
        
        # Calculate directional accuracy
        train_direction = np.sign(np.diff(np.append([y_train_inv[0]], y_train_inv)))
        pred_train_direction = np.sign(np.diff(np.append([y_train_inv[0]], y_pred_train_inv)))
        train_dir_acc = np.mean(train_direction == pred_train_direction)
        
        test_direction = np.sign(np.diff(np.append([y_test_inv[0]], y_test_inv)))
        pred_test_direction = np.sign(np.diff(np.append([y_test_inv[0]], y_pred_test_inv)))
        test_dir_acc = np.mean(test_direction == pred_test_direction)
        
        # Log results
        logger.info(f"Training RMSE: {train_rmse:.4f}, MAE: {train_mae:.4f}, R²: {train_r2:.4f}, Dir Acc: {train_dir_acc:.4f}")
        logger.info(f"Testing RMSE: {test_rmse:.4f}, MAE: {test_mae:.4f}, R²: {test_r2:.4f}, Dir Acc: {test_dir_acc:.4f}")
        
        # Return results
        return {
            "model_type": self.model_type,
            "train_samples": len(X_train),
            "test_samples": len(X_test),
            "train_rmse": train_rmse,
            "test_rmse": test_rmse,
            "train_mae": train_mae,
            "test_mae": test_mae,
            "train_r2": train_r2,
            "test_r2": test_r2,
            "train_dir_acc": train_dir_acc,
            "test_dir_acc": test_dir_acc
        }
    
    def predict(self, price_data: pd.DataFrame, periods: int = 1) -> List[float]:
        """
        Make price predictions.
        
        Args:
            price_data: DataFrame with price data
            periods: Number of periods to predict ahead
            
        Returns:
            List of predicted prices
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet")
        
        # Add technical indicators
        df = self._add_technical_indicators(price_data)
        
        # Make predictions for each period
        predictions = []
        current_data = df.copy()
        
        for _ in range(periods):
            # Prepare features for prediction
            feature_window = current_data.iloc[-self.lookback_period:][self.feature_columns].values
            feature_window = feature_window.flatten()
            
            # Scale features
            feature_window_scaled = self.feature_scaler.transform(feature_window.reshape(1, -1))
            
            # Make prediction
            prediction_scaled = self.model.predict(feature_window_scaled)[0]
            
            # Inverse transform prediction
            prediction = self.target_scaler.inverse_transform([[prediction_scaled]])[0][0]
            predictions.append(prediction)
            
            # Add prediction to data for next period
            new_row = current_data.iloc[-1:].copy()
            new_row.index = [current_data.index[-1] + pd.Timedelta(days=1)]
            new_row[self.target_column] = prediction
            
            # Simple approximation for other price columns
            price_ratio = prediction / current_data.iloc[-1][self.target_column]
            new_row["open"] = current_data.iloc[-1]["open"] * price_ratio
            new_row["high"] = current_data.iloc[-1]["high"] * price_ratio
            new_row["low"] = current_data.iloc[-1]["low"] * price_ratio
            
            # Assume volume stays the same
            new_row["volume"] = current_data.iloc[-1]["volume"]
            
            # Append new row to current data
            current_data = pd.concat([current_data, new_row])
            
            # Recalculate technical indicators
            current_data = self._add_technical_indicators(current_data)
        
        return predictions
    
    def evaluate_predictions(self, price_data: pd.DataFrame, prediction_periods: List[int] = [1, 5, 10]) -> Dict[str, Any]:
        """
        Evaluate model predictions for different time horizons.
        
        Args:
            price_data: DataFrame with price data
            prediction_periods: List of prediction horizons to evaluate
            
        Returns:
            Dictionary with evaluation results
        """
        results = {}
        
        for period in prediction_periods:
            # Set prediction horizon
            self.prediction_horizon = period
            
            # Train model
            train_results = self.train(price_data)
            
            # Store results
            results[f"{period}_day"] = train_results
        
        return results

def main():
    """Main function for testing the price prediction model."""
    # Create sample price data
    dates = pd.date_range(start='2023-01-01', periods=500, freq='D')
    
    # Create a more realistic price series with trend, seasonality, and noise
    t = np.arange(len(dates))
    trend = 100 + 0.1 * t
    seasonality = 10 * np.sin(2 * np.pi * t / 30)  # 30-day cycle
    noise = np.random.normal(0, 5, len(dates))
    
    prices = trend + seasonality + noise
    volumes = np.random.randint(1000, 5000, len(dates))
    
    # Create DataFrame
    price_data = pd.DataFrame({
        'open': prices - np.random.uniform(0, 2, len(dates)),
        'high': prices + np.random.uniform(0, 2, len(dates)),
        'low': prices - np.random.uniform(0, 2, len(dates)),
        'close': prices,
        'volume': volumes
    }, index=dates)
    
    # Initialize and train model
    model = PricePredictionModel(model_type="random_forest")
    train_results = model.train(price_data)
    
    # Print training results
    print("\n===== MODEL TRAINING RESULTS =====")
    print(f"Model Type: {train_results['model_type']}")
    print(f"Training Samples: {train_results['train_samples']}")
    print(f"Testing Samples: {train_results['test_samples']}")
    print(f"Training RMSE: {train_results['train_rmse']:.4f}")
    print(f"Testing RMSE: {train_results['test_rmse']:.4f}")
    print(f"Training MAE: {train_results['train_mae']:.4f}")
    print(f"Testing MAE: {train_results['test_mae']:.4f}")
    print(f"Training R²: {train_results['train_r2']:.4f}")
    print(f"Testing R²: {train_results['test_r2']:.4f}")
    print(f"Training Directional Accuracy: {train_results['train_dir_acc']:.4f}")
    print(f"Testing Directional Accuracy: {train_results['test_dir_acc']:.4f}")
    print("=================================")
    
    # Make predictions
    predictions = model.predict(price_data, periods=10)
    
    # Print predictions
    print("\n===== PRICE PREDICTIONS =====")
    last_price = price_data.iloc[-1]["close"]
    print(f"Last Known Price: {last_price:.2f}")
    
    for i, pred in enumerate(predictions):
        change = (pred / last_price - 1) * 100
        print(f"Day {i+1}: {pred:.2f} ({change:+.2f}%)")
    print("=============================")
    
    # Evaluate predictions for different time horizons
    evaluation = model.evaluate_predictions(price_data, prediction_periods=[1, 3, 5])
    
    # Print evaluation results
    print("\n===== PREDICTION EVALUATION =====")
    for period, results in evaluation.items():
        print(f"\n{period.upper()} PREDICTION:")
        print(f"Testing RMSE: {results['test_rmse']:.4f}")
        print(f"Testing MAE: {results['test_mae']:.4f}")
        print(f"Testing R²: {results['test_r2']:.4f}")
        print(f"Testing Directional Accuracy: {results['test_dir_acc']:.4f}")
    print("=================================")

if __name__ == "__main__":
    main()
