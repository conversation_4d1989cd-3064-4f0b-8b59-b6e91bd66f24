"""
Signal Thresholds for Project Ruby.

This module defines confidence thresholds for trading signals to ensure
that only high-confidence signals are presented as actionable advice.
"""

# Minimum confidence thresholds for different signal types
# These thresholds determine when a signal is considered strong enough to be actionable
SIGNAL_THRESHOLDS = {
    "buy": 0.65,  # 65% confidence required for buy signals
    "sell": 0.65,  # 65% confidence required for sell signals
    "neutral": 0.0  # No threshold for neutral signals
}

# Warning thresholds - signals above this but below the main threshold will show warnings
WARNING_THRESHOLDS = {
    "buy": 0.50,  # 50-65% confidence shows a warning for buy signals
    "sell": 0.50,  # 50-65% confidence shows a warning for sell signals
    "neutral": 0.0  # No warning threshold for neutral signals
}

def get_adjusted_signal(signal_type, confidence):
    """
    Get an adjusted signal based on confidence thresholds.
    
    Args:
        signal_type: The original signal type (buy, sell, neutral)
        confidence: The confidence level (0.0 to 1.0)
        
    Returns:
        dict: Adjusted signal information with the following keys:
            - signal: The adjusted signal type
            - original_signal: The original signal type
            - confidence: The original confidence level
            - warning: Whether the signal should display a warning
            - threshold_status: "strong", "warning", or "weak"
    """
    signal_type = signal_type.lower()
    
    # Default to the original values
    result = {
        "signal": signal_type,
        "original_signal": signal_type,
        "confidence": confidence,
        "warning": False,
        "threshold_status": "weak"
    }
    
    # Check if the signal meets the minimum threshold
    if signal_type in SIGNAL_THRESHOLDS:
        if confidence >= SIGNAL_THRESHOLDS[signal_type]:
            # Strong signal - keep as is
            result["threshold_status"] = "strong"
        elif confidence >= WARNING_THRESHOLDS[signal_type]:
            # Warning level - keep signal but add warning
            result["warning"] = True
            result["threshold_status"] = "warning"
        else:
            # Weak signal - convert to neutral
            result["signal"] = "neutral"
            result["threshold_status"] = "weak"
    
    return result

def get_signal_message(adjusted_signal):
    """
    Get a human-readable message explaining the signal.
    
    Args:
        adjusted_signal: The adjusted signal from get_adjusted_signal()
        
    Returns:
        str: A message explaining the signal
    """
    signal = adjusted_signal["signal"]
    original_signal = adjusted_signal["original_signal"]
    confidence = adjusted_signal["confidence"]
    warning = adjusted_signal["warning"]
    threshold_status = adjusted_signal["threshold_status"]
    
    confidence_pct = int(confidence * 100)
    
    if threshold_status == "strong":
        if signal == "buy":
            return f"Strong BUY signal with {confidence_pct}% confidence. Consider opening a long position."
        elif signal == "sell":
            return f"Strong SELL signal with {confidence_pct}% confidence. Consider opening a short position."
        else:
            return f"NEUTRAL signal with {confidence_pct}% confidence. No clear direction at this time."
    
    elif threshold_status == "warning":
        if original_signal == "buy":
            return f"Moderate BUY signal with {confidence_pct}% confidence. Consider waiting for stronger confirmation."
        elif original_signal == "sell":
            return f"Moderate SELL signal with {confidence_pct}% confidence. Consider waiting for stronger confirmation."
        else:
            return f"NEUTRAL signal with {confidence_pct}% confidence. No clear direction at this time."
    
    else:  # weak
        if original_signal == "buy":
            return f"Weak BUY signal with only {confidence_pct}% confidence. Not recommended for trading."
        elif original_signal == "sell":
            return f"Weak SELL signal with only {confidence_pct}% confidence. Not recommended for trading."
        else:
            return f"NEUTRAL signal with {confidence_pct}% confidence. No clear direction at this time."
