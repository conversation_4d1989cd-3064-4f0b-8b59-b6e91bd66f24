@echo off
echo Starting Project Ruby with REAL DATA...

REM Set environment variables for real data
set USE_REAL_DATA=true
set COINGECKO_API_KEY=CG-Ag17QEEjKxkZAo4yFr66pE9L
set CRYPTOCOMPARE_API_KEY=****************************************************************
set COINMARKETCAP_API_KEY=26858902-d753-4225-8fc2-f5af98144a9d
set BINANCE_API_KEY=GhP7XdXbyxfMyCGXellMwNlEqYDCVYyPulh2FXpR9t10dEGaNlXoUgy7IcDG4wEr

REM Generate multi-timeframe signals
echo Generating multi-timeframe signals...
python generate_timeframe_signals.py > nul
echo Multi-timeframe signals generated successfully!

REM Initialize consensus models
echo Initializing AI consensus models...
python -c "from backend.models.consensus_engine import ConsensusEngine; engine = ConsensusEngine(); predictions = engine.generate_predictions_for_symbols(['BTC', 'ETH', 'SOL', 'ADA'], force_refresh=True); print('AI consensus models initialized successfully!')" > nul
echo AI consensus models initialized successfully!

REM Check if the price server is already running
netstat -ano | findstr :8004 > nul
if %errorlevel% equ 0 (
    echo Price server is already running on port 8004
) else (
    echo Starting price server on port 8004 with REAL DATA...
    start "Price Server - REAL DATA" cmd /c "cd backend && set USE_REAL_DATA=true && python price_server.py"
    timeout /t 3 > nul
)

REM Check if the frontend server is already running
netstat -ano | findstr :8085 > nul
if %errorlevel% equ 0 (
    echo Frontend server is already running on port 8085
) else (
    echo Starting frontend server on port 8085...
    start "Frontend Server" cmd /c "cd frontend\public && python -m http.server 8085"
    timeout /t 2 > nul
)

REM Start the signal updater in the background with real data
echo Starting signal updater in the background with REAL DATA...
start "Signal Updater - REAL DATA" /min cmd /c "set USE_REAL_DATA=true && python backend\signal_updater.py"

REM Open the application in the browser
echo Opening Project Ruby in your browser...
start http://localhost:8085/redirect.html

echo.
echo Project Ruby is now running with REAL DATA!
echo - Frontend: http://localhost:8085
echo - API Server: http://localhost:8004
echo - Real Data Mode: ACTIVE (Live API data)
echo.
echo Press any key to exit this window (servers will continue running)...
pause > nul
