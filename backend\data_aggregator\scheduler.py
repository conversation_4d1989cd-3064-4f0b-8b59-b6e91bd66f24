"""
Data Aggregation Scheduler

This module schedules and coordinates the execution of various data fetchers
to collect information for the MCP server.
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Callable, Awaitable, Optional

import httpx

# Import data fetchers
from data_aggregator.crypto_price_fetcher import CoinGeckoFetcher
from data_aggregator.governance_collector import GovernanceCollector
from data_aggregator.sentiment_analyzer import SentimentAnalyzer

# Import API keys (if available)
try:
    from data_aggregator.config import API_KEYS
except ImportError:
    # Default empty API keys if config.py doesn't exist
    API_KEYS = {
        'news': None,
        'twitter': None,
        'reddit': None,
        'tally': None
    }

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Constants
MCP_SERVER_URL = "http://127.0.0.1:8002"  # Using IP address instead of localhost and a different port
DEFAULT_INTERVAL = 60  # seconds
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds

class DataAggregationScheduler:
    """Schedules and coordinates data aggregation tasks."""

    def __init__(self, mcp_server_url: str = MCP_SERVER_URL, api_token: Optional[str] = None):
        """Initialize the scheduler with MCP server URL and optional API token."""
        self.mcp_server_url = mcp_server_url
        self.api_token = api_token
        self.headers = {}
        if api_token:
            self.headers["Authorization"] = f"Bearer {api_token}"

        # Initialize data fetchers
        self.crypto_fetcher = CoinGeckoFetcher()

        # Initialize governance collector
        # We pass self as the MCP client since we handle the MCP communication
        self.governance_collector = GovernanceCollector(self, {
            'lookback_hours': 24,
            'snapshot_api_url': 'https://hub.snapshot.org/graphql',
            'tally_api_key': '',  # Add your API key if you have one
        })

        # Initialize sentiment analyzer with real API keys
        self.sentiment_analyzer = SentimentAnalyzer(self, {
            'sources': ['news'],  # Start with news only, add more sources as API keys are available
            'api_keys': {
                'twitter': API_KEYS.get('twitter'),
                'reddit': API_KEYS.get('reddit'),
                'news': API_KEYS.get('news')
            }
        })

        # Task scheduling configuration
        self.tasks = {
            "crypto_prices": {
                "interval": 60,  # seconds
                "last_run": None,
                "fetcher": self.fetch_crypto_prices,
            },
            "governance_data": {
                "interval": 3600,  # 1 hour in seconds
                "last_run": None,
                "fetcher": self.fetch_governance_data,
            },
            "sentiment_data": {
                "interval": 1800,  # 30 minutes in seconds
                "last_run": None,
                "fetcher": self.fetch_sentiment_data,
            },
            # Add more tasks here as you implement more data fetchers
        }

        logger.info(f"Data aggregation scheduler initialized with MCP server at {mcp_server_url}")

    async def fetch_crypto_prices(self) -> bool:
        """Fetch cryptocurrency prices and send to MCP server."""
        try:
            # Fetch price data
            price_data = await self.crypto_fetcher.fetch_prices()
            if not price_data:
                logger.warning("No cryptocurrency price data fetched")
                return False

            # Convert to context items
            context_items = self.crypto_fetcher.to_context_items(price_data)

            # Send to MCP server
            return await self.send_to_mcp(context_items)

        except Exception as e:
            logger.error(f"Error fetching cryptocurrency prices: {e}")
            return False

    async def fetch_governance_data(self) -> bool:
        """Fetch governance data and send to MCP server."""
        try:
            # Collect governance data
            governance_data = await self.governance_collector.collect_data()
            if not governance_data:
                logger.warning("No governance data fetched")
                return False

            # Generate signals
            signals = self.governance_collector.signal_generator.generate_signals(governance_data)
            if not signals:
                logger.warning("No governance signals generated")
                return False

            # Convert signals to context items in the format expected by MCP server
            context_items = []
            for signal in signals:
                # Create a properly formatted context item
                context_item = {
                    "id": signal["id"],
                    "type": "governance_signal",
                    "content": {
                        "proposal_id": signal["proposal_id"],
                        "signal_type": signal["signal_type"],
                        "strength": signal["strength"],
                        "confidence": signal.get("confidence", 0.7),
                        "assets_affected": signal["assets_affected"],
                        "description": signal["description"]
                    },
                    "timestamp": signal["timestamp"],
                    "source": signal["source"],
                    "confidence": signal.get("confidence", 0.7)
                }
                context_items.append(context_item)

            # Send to MCP server
            return await self.send_to_mcp(context_items)

        except Exception as e:
            logger.error(f"Error fetching governance data: {e}")
            return False

    async def fetch_sentiment_data(self) -> bool:
        """Fetch sentiment data and send to MCP server."""
        try:
            # Process and send sentiment data to MCP
            # This method handles both collection and sending to MCP
            success = await self.sentiment_analyzer.process_and_send_to_mcp()

            if not success:
                logger.warning("Failed to process and send sentiment data")
                return False

            return True

        except Exception as e:
            logger.error(f"Error fetching sentiment data: {e}")
            return False

    # Method to allow the governance collector to add context
    async def add_context(self, context_item: Dict[str, Any]) -> bool:
        """Add a single context item to the MCP server."""
        return await self.send_to_mcp([context_item])

    async def send_to_mcp(self, context_items: List[Any]) -> bool:
        """Send context items to the MCP server."""
        if not context_items:
            logger.warning("No context items to send")
            return False

        # Use the no-auth endpoint for development/testing
        url = f"{self.mcp_server_url}/context/batch/noauth"

        for attempt in range(MAX_RETRIES):
            try:
                async with httpx.AsyncClient() as client:
                    # Convert items to dictionaries with proper datetime handling
                    items_data = []
                    for item in context_items:
                        # Handle both Pydantic models and dictionaries
                        if hasattr(item, 'model_dump'):
                            # Newer Pydantic v2 method
                            item_dict = item.model_dump()
                        elif hasattr(item, 'dict'):
                            # Older Pydantic method
                            item_dict = item.dict()
                        else:
                            # Already a dictionary
                            item_dict = item

                        # Convert datetime objects to ISO format strings
                        if 'timestamp' in item_dict and isinstance(item_dict['timestamp'], datetime):
                            item_dict['timestamp'] = item_dict['timestamp'].isoformat()

                        items_data.append(item_dict)

                    response = await client.post(
                        url,
                        json=items_data,
                        headers=self.headers,
                        timeout=30
                    )
                    response.raise_for_status()

                    logger.info(f"Successfully sent {len(context_items)} context items to MCP server")
                    return True

            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error sending to MCP server: {e}")
                if e.response.status_code == 401:
                    logger.error("Authentication failed. Check your API token.")
                    return False

                if attempt < MAX_RETRIES - 1:
                    logger.info(f"Retrying in {RETRY_DELAY} seconds... (Attempt {attempt + 1}/{MAX_RETRIES})")
                    await asyncio.sleep(RETRY_DELAY)
                else:
                    logger.error(f"Failed to send data after {MAX_RETRIES} attempts")
                    return False

            except httpx.RequestError as e:
                logger.error(f"Request error sending to MCP server: {e}")
                if attempt < MAX_RETRIES - 1:
                    logger.info(f"Retrying in {RETRY_DELAY} seconds... (Attempt {attempt + 1}/{MAX_RETRIES})")
                    await asyncio.sleep(RETRY_DELAY)
                else:
                    logger.error(f"Failed to send data after {MAX_RETRIES} attempts")
                    return False

            except Exception as e:
                logger.error(f"Unexpected error sending to MCP server: {e}")
                return False

    async def run_task(self, task_name: str) -> None:
        """Run a specific task and update its last run time."""
        if task_name not in self.tasks:
            logger.error(f"Unknown task: {task_name}")
            return

        task = self.tasks[task_name]
        logger.info(f"Running task: {task_name}")

        success = await task["fetcher"]()
        task["last_run"] = datetime.now(timezone.utc)

        if success:
            logger.info(f"Task {task_name} completed successfully")
        else:
            logger.warning(f"Task {task_name} failed")

    async def start(self) -> None:
        """Start the scheduler."""
        logger.info("Starting data aggregation scheduler")
        # Initialize any resources needed
        self.running = True

    async def stop(self) -> None:
        """Stop the scheduler."""
        logger.info("Stopping data aggregation scheduler")
        self.running = False
        # Clean up any resources

    async def process_next_task(self) -> None:
        """Process the next task that is due to run."""
        if not hasattr(self, 'running') or not self.running:
            return

        current_time = datetime.now(timezone.utc)

        for task_name, task in self.tasks.items():
            # Check if task should run
            if (
                task["last_run"] is None or
                (current_time - task["last_run"]).total_seconds() >= task["interval"]
            ):
                await self.run_task(task_name)
                # Only run one task at a time
                return

    async def run_scheduler(self) -> None:
        """Run the scheduler loop to execute tasks at their specified intervals."""
        logger.info("Starting data aggregation scheduler")
        self.running = True

        while self.running:
            await self.process_next_task()
            # Sleep for a short time before checking again
            await asyncio.sleep(1)

async def main():
    """Main function to run the scheduler."""
    # You can set your MCP server URL and API token here
    scheduler = DataAggregationScheduler(
        mcp_server_url=MCP_SERVER_URL,
        api_token=None  # Replace with your API token if needed
    )

    try:
        await scheduler.run_scheduler()
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user")
    except Exception as e:
        logger.error(f"Scheduler stopped due to error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
