"""
CryptoCompare Data Collection Script

This script collects market data from CryptoCompare and saves it to the data directory.
It can be run as a standalone script or scheduled to run periodically.
"""

import os
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'logs', 'cc_collection.log'))
    ]
)
logger = logging.getLogger('cc_collection')

# Create logs directory if it doesn't exist
os.makedirs(os.path.join(os.path.dirname(__file__), 'logs'), exist_ok=True)

# Add the project root to the Python path
sys.path.append(os.path.dirname(__file__))

# Import the CryptoCompare client
try:
    from data_aggregator.cryptocompare_client import CryptoCompareClient, main as collect_cc_data
    logger.info("Successfully imported CryptoCompare client")
except ImportError as e:
    logger.error(f"Error importing CryptoCompare client: {str(e)}")
    logger.info("Creating necessary directories...")
    
    # Create necessary directories
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data_aggregator'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'market'), exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data', 'market', 'news'), exist_ok=True)
    
    logger.info("Please restart the script after ensuring all modules are in place")
    sys.exit(1)

def main():
    """Main function to run the CryptoCompare data collection."""
    start_time = datetime.now()
    logger.info(f"Starting CryptoCompare data collection at {start_time}")
    
    # Run the collection
    success = collect_cc_data()
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    if success:
        logger.info(f"CryptoCompare data collection completed successfully in {duration}")
    else:
        logger.error(f"CryptoCompare data collection failed after {duration}")
    
    return success

if __name__ == "__main__":
    main()
