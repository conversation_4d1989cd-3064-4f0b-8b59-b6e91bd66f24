<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio & Trading | Project Ruby</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/ruby-core.css">
    <style>
        .portfolio-tabs {
            display: flex;
            background: var(--ruby-bg-light);
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            border: 1px solid var(--ruby-border);
        }

        .portfolio-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: var(--ruby-text-secondary);
            font-weight: 500;
        }

        .portfolio-tab.active {
            background: var(--ruby-gold);
            color: var(--ruby-bg-dark);
            font-weight: 600;
        }

        .portfolio-content {
            display: none;
        }

        .portfolio-content.active {
            display: block;
        }

        .portfolio-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .overview-card {
            background: var(--ruby-bg-dark);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid var(--ruby-border);
        }

        .overview-value {
            font-size: 1.8em;
            font-weight: bold;
            color: var(--ruby-gold);
            margin-bottom: 5px;
        }

        .overview-label {
            color: var(--ruby-text-secondary);
            font-size: 0.9em;
        }

        .overview-change {
            font-size: 0.9em;
            margin-top: 5px;
        }

        .positive { color: #22c55e; }
        .negative { color: #ef4444; }

        .portfolio-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .portfolio-card {
            background: var(--ruby-bg-dark);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid var(--ruby-border);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            color: var(--ruby-text-primary);
            font-size: 1.1em;
            font-weight: 600;
        }

        .holdings-table {
            width: 100%;
            border-collapse: collapse;
        }

        .holdings-table th,
        .holdings-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--ruby-border);
        }

        .holdings-table th {
            color: var(--ruby-text-secondary);
            font-weight: 500;
            font-size: 0.9em;
        }

        .holdings-table td {
            color: var(--ruby-text-primary);
        }

        .asset-info {
            display: flex;
            align-items: center;
        }

        .asset-symbol {
            font-weight: bold;
            margin-right: 8px;
        }

        .asset-name {
            color: var(--ruby-text-secondary);
            font-size: 0.9em;
        }

        .trading-form {
            background: var(--ruby-bg-light);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--ruby-border);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            color: var(--ruby-text-primary);
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--ruby-border);
            border-radius: 5px;
            background: var(--ruby-bg-dark);
            color: var(--ruby-text-primary);
        }

        .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--ruby-border);
            border-radius: 5px;
            background: var(--ruby-bg-dark);
            color: var(--ruby-text-primary);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-buy {
            background: #22c55e;
            color: white;
        }

        .btn-buy:hover {
            background: #16a34a;
        }

        .btn-sell {
            background: #ef4444;
            color: white;
        }

        .btn-sell:hover {
            background: #dc2626;
        }

        .trade-history {
            max-height: 400px;
            overflow-y: auto;
        }

        .trade-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--ruby-border);
        }

        .trade-item:last-child {
            border-bottom: none;
        }

        .trade-info {
            display: flex;
            flex-direction: column;
        }

        .trade-type {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .trade-details {
            font-size: 0.9em;
            color: var(--ruby-text-secondary);
        }

        .trade-pnl {
            font-weight: bold;
            text-align: right;
        }

        .coin-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .coin-btn {
            padding: 8px 12px;
            border: 1px solid var(--ruby-border);
            border-radius: 5px;
            background: var(--ruby-bg-light);
            color: var(--ruby-text-primary);
            cursor: pointer;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .coin-btn.active {
            background: var(--ruby-gold);
            color: var(--ruby-bg-dark);
            border-color: var(--ruby-gold);
        }

        .coin-btn:hover:not(.active) {
            background: var(--ruby-bg-dark);
        }
    </style>
</head>
<body class="ruby-theme">
    <div class="container">
        <header>
            <div>
                <div style="margin-bottom: 15px;">
                    <img src="images/project-ruby-text-logo.svg" alt="Project Ruby" style="height: 50px;" />
                </div>
                <h1>Portfolio & Trading</h1>
                <div class="nav">
                    <a href="index.html">Home</a>
                    <a href="dashboard.html">Dashboard</a>
                    <a href="trading.html">Trading</a>
                    <a href="analysis.html">Analysis</a>
                    <a href="charts.html">Charts</a>
                    <a href="predictions.html">Predictions</a>
                    <a href="chatbot.html">Assistant</a>
                    <a href="portfolio.html" style="font-weight: bold;">Portfolio</a>
                    <a href="settings.html">Settings</a>
                </div>
            </div>
        </header>

        <!-- Portfolio Tabs -->
        <div class="portfolio-tabs">
            <div class="portfolio-tab active" data-tab="overview">Portfolio Overview</div>
            <div class="portfolio-tab" data-tab="trading">Paper Trading</div>
            <div class="portfolio-tab" data-tab="history">Trade History</div>
            <div class="portfolio-tab" data-tab="management">Coin Management</div>
        </div>

        <!-- Portfolio Overview Content -->
        <div class="portfolio-content active" id="overview-content">
            <div class="portfolio-overview">
                <div class="overview-card">
                    <div class="overview-value" id="total-value">$25,430.50</div>
                    <div class="overview-label">Total Portfolio Value</div>
                    <div class="overview-change positive">+$1,245.30 (5.1%)</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value" id="daily-pnl">+$342.15</div>
                    <div class="overview-label">24h P&L</div>
                    <div class="overview-change positive">+1.4%</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value" id="total-trades">127</div>
                    <div class="overview-label">Total Trades</div>
                    <div class="overview-change positive">+3 today</div>
                </div>
                <div class="overview-card">
                    <div class="overview-value" id="win-rate">68.5%</div>
                    <div class="overview-label">Win Rate</div>
                    <div class="overview-change positive">+2.1%</div>
                </div>
            </div>

            <div class="portfolio-grid">
                <div class="portfolio-card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-wallet"></i> Current Holdings</h2>
                    </div>
                    <table class="holdings-table">
                        <thead>
                            <tr>
                                <th>Asset</th>
                                <th>Amount</th>
                                <th>Value</th>
                                <th>24h Change</th>
                            </tr>
                        </thead>
                        <tbody id="holdings-list">
                            <tr>
                                <td>
                                    <div class="asset-info">
                                        <span class="asset-symbol">BTC</span>
                                        <span class="asset-name">Bitcoin</span>
                                    </div>
                                </td>
                                <td>0.2534</td>
                                <td>$24,670.50</td>
                                <td class="positive">+2.4%</td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="asset-info">
                                        <span class="asset-symbol">ETH</span>
                                        <span class="asset-name">Ethereum</span>
                                    </div>
                                </td>
                                <td>0.2180</td>
                                <td>$760.00</td>
                                <td class="positive">+1.8%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="portfolio-card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-chart-pie"></i> Allocation</h2>
                    </div>
                    <div id="allocation-chart">
                        <p>Portfolio allocation chart will be displayed here</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Paper Trading Content -->
        <div class="portfolio-content" id="trading-content">
            <div class="portfolio-grid">
                <div class="portfolio-card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-exchange-alt"></i> Execute Trade</h2>
                    </div>
                    <div class="trading-form">
                        <div class="coin-selector">
                            <div class="coin-btn active" data-symbol="BTC">BTC</div>
                            <div class="coin-btn" data-symbol="ETH">ETH</div>
                            <div class="coin-btn" data-symbol="SOL">SOL</div>
                            <div class="coin-btn" data-symbol="ADA">ADA</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Trade Type</label>
                            <select class="form-select" id="trade-type">
                                <option value="market">Market Order</option>
                                <option value="limit">Limit Order</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Amount (USD)</label>
                            <input type="number" class="form-input" id="trade-amount" placeholder="Enter amount">
                        </div>

                        <div class="form-group">
                            <label class="form-label">Current Price</label>
                            <input type="text" class="form-input" id="current-price" value="$97,350.42" readonly>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-buy" onclick="executeTrade('buy')">
                                <i class="fas fa-arrow-up"></i> Buy
                            </button>
                            <button class="btn btn-sell" onclick="executeTrade('sell')">
                                <i class="fas fa-arrow-down"></i> Sell
                            </button>
                        </div>
                    </div>
                </div>

                <div class="portfolio-card">
                    <div class="card-header">
                        <h2 class="card-title"><i class="fas fa-history"></i> Recent Trades</h2>
                    </div>
                    <div class="trade-history" id="recent-trades">
                        <div class="trade-item">
                            <div class="trade-info">
                                <div class="trade-type positive">BUY BTC</div>
                                <div class="trade-details">0.0103 BTC @ $97,200</div>
                            </div>
                            <div class="trade-pnl positive">+$15.50</div>
                        </div>
                        <div class="trade-item">
                            <div class="trade-info">
                                <div class="trade-type negative">SELL ETH</div>
                                <div class="trade-details">0.5 ETH @ $3,480</div>
                            </div>
                            <div class="trade-pnl positive">+$42.30</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trade History Content -->
        <div class="portfolio-content" id="history-content">
            <div class="portfolio-card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Complete Trade History</h2>
                </div>
                <div id="complete-history">
                    <p>Loading complete trade history...</p>
                </div>
            </div>
        </div>

        <!-- Coin Management Content -->
        <div class="portfolio-content" id="management-content">
            <div class="portfolio-card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-coins"></i> Manage Cryptocurrencies</h2>
                </div>
                <div id="coin-management">
                    <p>Cryptocurrency management interface will be loaded here...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-service.js"></script>
    <script src="js/paper-trading.js"></script>
    <script src="js/ruby-common.js"></script>
    <script>
        // Tab switching functionality
        document.querySelectorAll('.portfolio-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.portfolio-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.portfolio-content').forEach(c => c.classList.remove('active'));

                tab.classList.add('active');
                document.getElementById(tab.dataset.tab + '-content').classList.add('active');

                loadTabContent(tab.dataset.tab);
            });
        });

        // Coin selector functionality
        document.querySelectorAll('.coin-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.coin-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                updatePriceForSymbol(btn.dataset.symbol);
            });
        });

        function loadTabContent(tabName) {
            console.log('Loading content for tab:', tabName);

            // Load specific content based on tab
            switch(tabName) {
                case 'overview':
                    loadPortfolioOverview();
                    break;
                case 'trading':
                    loadTradingInterface();
                    break;
                case 'history':
                    loadTradeHistory();
                    break;
                case 'management':
                    loadCoinManagement();
                    break;
            }
        }

        function loadPortfolioOverview() {
            console.log('Loading portfolio overview...');
            // Load portfolio data from API or local storage
        }

        function loadTradingInterface() {
            console.log('Loading trading interface...');
            // Initialize paper trading functionality
            updatePriceForSymbol('BTC'); // Default to BTC
        }

        function loadTradeHistory() {
            console.log('Loading trade history...');
            // Load trade history from API or local storage
        }

        function loadCoinManagement() {
            console.log('Loading coin management...');
            // Load coin management interface
            const managementDiv = document.getElementById('coin-management');
            if (managementDiv) {
                managementDiv.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <p>Coin management functionality will be available here.</p>
                        <p>You can enable/disable cryptocurrencies and manage your watchlist.</p>
                    </div>
                `;
            }
        }

        function updatePriceForSymbol(symbol) {
            console.log('Updating price for:', symbol);

            // Demo prices for different symbols
            const demoPrices = {
                'BTC': '$97,350.42',
                'ETH': '$3,485.75',
                'SOL': '$248.65',
                'ADA': '$1.12'
            };

            const priceInput = document.getElementById('current-price');
            if (priceInput && demoPrices[symbol]) {
                priceInput.value = demoPrices[symbol];
            }

            // In real implementation, this would fetch live prices from API
        }

        function executeTrade(type) {
            const symbol = document.querySelector('.coin-btn.active').dataset.symbol;
            const amount = document.getElementById('trade-amount').value;
            const tradeType = document.getElementById('trade-type').value;
            const currentPrice = document.getElementById('current-price').value;

            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            console.log(`Executing ${type} trade:`, { symbol, amount, tradeType, currentPrice });

            // Show confirmation
            const confirmation = confirm(`Execute ${type.toUpperCase()} order for $${amount} of ${symbol} at ${currentPrice}?`);

            if (confirmation) {
                // Simulate trade execution
                alert(`✅ ${type.toUpperCase()} order executed successfully!\n\nSymbol: ${symbol}\nAmount: $${amount}\nPrice: ${currentPrice}\nType: ${tradeType}`);

                // Clear the amount field
                document.getElementById('trade-amount').value = '';

                // In real implementation, this would:
                // 1. Send trade to backend API
                // 2. Update portfolio
                // 3. Add to trade history
                // 4. Update UI with new data
            }
        }

        // Initialize portfolio data
        document.addEventListener('DOMContentLoaded', function() {
            loadTabContent('overview');
        });
    </script>
</body>
</html>
