"""
Test script for the sentiment analyzer.
"""

import asyncio
import json
import logging
from datetime import datetime

from sentiment_analyzer import SentimentAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Simple MCP client for testing
class TestMCPClient:
    async def add_context(self, context_item):
        print(f"Would send to MCP: {json.dumps(context_item, indent=2)}")
        return True

async def main():
    """Test the sentiment analyzer."""
    # Initialize the sentiment analyzer with a test MCP client
    analyzer = SentimentAnalyzer(
        TestMCPClient(),
        config={
            'sources': ['news'],  # Only use news source for testing
            'tracked_assets': [
                {"id": "bitcoin", "symbol": "BTC", "name": "Bitcoin"},
                {"id": "ethereum", "symbol": "ETH", "name": "Ethereum"},
                {"id": "uniswap", "symbol": "UNI", "name": "Uniswap"},
            ],
            'api_keys': {
                'news': None,  # Use sample data if no API key is provided
            }
        }
    )
    
    # Collect sentiment data
    sentiment_data = await analyzer.collect_data()
    
    # Print results
    print(f"\nCollected {len(sentiment_data)} sentiment data points")
    
    for data in sentiment_data:
        print(f"\n{data.asset_symbol} Sentiment:")
        print(f"  Score: {data.sentiment_score:.2f}")
        print(f"  Type: {data.sentiment_type}")
        print(f"  Volume: {data.volume}")
        print(f"  Source: {data.source}")
    
    # Generate signals
    signals = analyzer.generate_signals(sentiment_data)
    
    # Print signals
    print(f"\nGenerated {len(signals)} sentiment signals")
    
    for signal in signals:
        print(f"\n{signal.asset_symbol} Signal:")
        print(f"  Type: {signal.signal_type}")
        print(f"  Direction: {signal.direction}")
        print(f"  Strength: {signal.strength:.2f}")
        print(f"  Description: {signal.description}")

if __name__ == "__main__":
    asyncio.run(main())
