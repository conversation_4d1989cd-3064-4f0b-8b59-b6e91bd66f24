"""
Signal generator for governance data.

This module analyzes governance data to generate trading signals based on
proposal activity, voting patterns, and governance events.
"""

import logging
import uuid
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from .models import GovernanceProposal, VotingData, GovernanceSignal

logger = logging.getLogger(__name__)

class GovernanceSignalGenerator:
    """
    Generates trading signals from governance data.

    This class analyzes governance proposals, votes, and activities to
    identify potential market-moving events and generate trading signals.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the governance signal generator.

        Args:
            config: Configuration for signal generation
        """
        self.config = config or {}

        # Map of space IDs to affected assets
        # This would ideally be loaded from a database or configuration
        self.space_to_assets = {
            # Snapshot spaces
            "uniswap": ["UNI", "ETH"],
            "aave.eth": ["AAVE", "ETH", "USDC", "USDT", "DAI"],
            "compound-governance.eth": ["COMP", "USDC", "ETH"],
            "maker": ["MKR", "DAI", "ETH"],
            "sushigov.eth": ["SUSHI", "ETH"],
            "curve.eth": ["CRV", "ETH", "USDC", "USDT", "DAI"],
            "synthetixproposal.eth": ["SNX", "ETH"],
            "balancer.eth": ["BAL", "ETH"],
            "yearn": ["YFI", "ETH", "USDC", "DAI"],
            "ens.eth": ["ENS", "ETH"],
            "optimism": ["OP", "ETH"],
            "arbitrum": ["ARB", "ETH"],
            "gitcoin.eth": ["GTC", "ETH"],
            "dydx.eth": ["DYDX", "ETH"],
            # Add more mappings as needed
        }

        # Asset importance weights (market impact)
        self.asset_importance = {
            "BTC": 1.0,
            "ETH": 0.9,
            "USDT": 0.8,
            "USDC": 0.8,
            "BNB": 0.7,
            "XRP": 0.6,
            "ADA": 0.6,
            "SOL": 0.6,
            "DOGE": 0.5,
            "DAI": 0.5,
            "AAVE": 0.4,
            "UNI": 0.4,
            "COMP": 0.4,
            "MKR": 0.4,
            "SUSHI": 0.3,
            "CRV": 0.3,
            "SNX": 0.3,
            "BAL": 0.3,
            "YFI": 0.3,
        }

        # Proposal types that are likely to impact markets
        self.high_impact_proposal_types = [
            "parameter change",
            "upgrade",
            "treasury",
            "funding",
            "emergency",
            "protocol change",
            "fee change",
            "collateral",
            "liquidation",
            "interest rate"
        ]

        # Impact weights for different proposal types
        self.proposal_type_impact = {
            "parameter change": 0.6,
            "upgrade": 0.8,
            "treasury": 0.5,
            "funding": 0.4,
            "emergency": 0.9,
            "protocol change": 0.7,
            "fee change": 0.6,
            "collateral": 0.7,
            "liquidation": 0.8,
            "interest rate": 0.7,
            "general": 0.3
        }

        # Historical signal performance tracking
        self.signal_performance = {
            "high_impact_proposal": {
                "success_count": 0,
                "total_count": 0
            },
            "high_participation": {
                "success_count": 0,
                "total_count": 0
            },
            "contentious_proposal": {
                "success_count": 0,
                "total_count": 0
            },
            "whale_activity": {
                "success_count": 0,
                "total_count": 0
            },
            "governance_trend": {
                "success_count": 0,
                "total_count": 0
            }
        }

        logger.info("Governance Signal Generator initialized")

    def _get_affected_assets(self, proposal: GovernanceProposal) -> List[str]:
        """
        Determine which assets might be affected by a proposal.

        Args:
            proposal: The governance proposal

        Returns:
            List of asset symbols that might be affected
        """
        # First check if we have a direct mapping for this space
        if proposal.space_id in self.space_to_assets:
            return self.space_to_assets[proposal.space_id]

        # Try to match by name if ID doesn't match
        for space_id, assets in self.space_to_assets.items():
            if space_id.lower() in proposal.space_name.lower():
                return assets

        # If no match, try to extract from proposal title/description
        # This is a simple implementation - in practice, you'd want more sophisticated NLP
        affected_assets = []
        for space_id, assets in self.space_to_assets.items():
            for asset in assets:
                if (asset.lower() in proposal.title.lower() or
                    asset.lower() in proposal.description.lower()):
                    affected_assets.append(asset)

        return affected_assets

    def _determine_proposal_type(self, proposal: GovernanceProposal) -> str:
        """
        Determine the type of proposal based on its content.

        Args:
            proposal: The governance proposal

        Returns:
            The proposal type as a string
        """
        # If the proposal already has a type, use it
        if proposal.proposal_type:
            return proposal.proposal_type

        # Otherwise, try to determine from title and description
        title_lower = proposal.title.lower()
        desc_lower = proposal.description.lower()

        if any(word in title_lower for word in ["param", "parameter", "config", "configuration"]):
            return "parameter change"

        if any(word in title_lower for word in ["upgrade", "update", "migration"]):
            return "upgrade"

        if any(word in title_lower for word in ["fund", "grant", "treasury", "budget"]):
            return "funding"

        if any(word in title_lower for word in ["emergency", "urgent", "critical"]):
            return "emergency"

        # Default type
        return "general"

    def _calculate_signal_strength(self, proposal: GovernanceProposal, signal_type: str) -> float:
        """
        Calculate the strength of a signal based on proposal characteristics.

        Args:
            proposal: The governance proposal
            signal_type: The type of signal being generated

        Returns:
            Signal strength as a float between 0.0 and 1.0
        """
        # Base strength starts at 0.3
        strength = 0.3

        # Determine proposal type and get its impact weight
        proposal_type = self._determine_proposal_type(proposal)
        type_impact = self.proposal_type_impact.get(proposal_type, 0.3)
        strength += type_impact * 0.3  # Add up to 0.3 based on proposal type

        # Calculate space importance based on affected assets
        affected_assets = self._get_affected_assets(proposal)
        asset_importance_sum = sum(self.asset_importance.get(asset, 0.1) for asset in affected_assets)
        asset_importance_avg = asset_importance_sum / len(affected_assets) if affected_assets else 0.1
        strength += asset_importance_avg * 0.2  # Add up to 0.2 based on asset importance

        # Calculate voting activity impact
        # Use logarithmic scale to handle wide range of vote counts
        if proposal.votes_count > 0:
            vote_factor = min(1.0, math.log10(proposal.votes_count) / 3)  # log10(1000) = 3
            strength += vote_factor * 0.15  # Add up to 0.15 based on vote count

        # Calculate time-based relevance (newer proposals have higher impact)
        if hasattr(proposal, 'created_at') and proposal.created_at:
            days_old = (datetime.now() - proposal.created_at).total_seconds() / 86400
            time_factor = max(0, 1 - (days_old / 7))  # Full strength if < 1 day, decreasing until 7 days
            strength += time_factor * 0.1  # Add up to 0.1 based on recency

        # Add signal-specific factors
        if signal_type == "high_impact_proposal":
            # Higher strength for proposals with clear financial impact
            financial_keywords = ["fee", "interest", "yield", "revenue", "profit", "loss", "price", "value"]
            if any(keyword in proposal.title.lower() or keyword in proposal.description.lower()
                   for keyword in financial_keywords):
                strength += 0.15

        elif signal_type == "high_participation":
            # Calculate participation relative to typical participation for the space
            # This is a simplified approach - ideally you'd have historical data
            typical_participation = 100  # Placeholder
            relative_participation = proposal.votes_count / typical_participation
            participation_factor = min(1.0, relative_participation)
            strength += participation_factor * 0.15

        elif signal_type == "contentious_proposal":
            # More sophisticated analysis of vote distribution
            if len(proposal.scores) >= 2 and min(proposal.scores) > 0:
                ratio = min(proposal.scores) / max(proposal.scores)
                # Contentious votes are those with close to 50/50 split
                contention_factor = 1.0 - abs(0.5 - ratio)
                strength += contention_factor * 0.2

        elif signal_type == "whale_activity":
            # Strength based on whale influence
            strength += 0.15

        elif signal_type == "governance_trend":
            # Strength based on trend significance
            strength += 0.1

        # Adjust based on historical signal performance
        if signal_type in self.signal_performance:
            perf = self.signal_performance[signal_type]
            if perf["total_count"] > 0:
                success_rate = perf["success_count"] / perf["total_count"]
                strength *= (0.8 + (0.4 * success_rate))  # Scale by 0.8-1.2 based on success rate

        # Cap at 1.0
        return min(strength, 1.0)

    def generate_signals(self, proposals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate trading signals from governance proposals.

        Args:
            proposals: List of governance proposal data

        Returns:
            List of generated signals
        """
        signals = []

        for proposal_data in proposals:
            try:
                # Convert to GovernanceProposal if it's not already
                if not isinstance(proposal_data, GovernanceProposal):
                    # This is a simplified conversion - in practice, you'd want to
                    # handle the different formats from different sources
                    proposal = GovernanceProposal(
                        id=proposal_data.get("id", ""),
                        title=proposal_data.get("title", ""),
                        description=proposal_data.get("body", proposal_data.get("description", "")),
                        platform=proposal_data.get("platform", "unknown"),
                        space_id=proposal_data.get("space_id", ""),
                        space_name=proposal_data.get("space_name", ""),
                        state=proposal_data.get("state", ""),
                        created_at=proposal_data.get("created_at", datetime.now()),
                        start_date=proposal_data.get("start_date", datetime.now()),
                        end_date=proposal_data.get("end_date", datetime.now()),
                        author=proposal_data.get("author", ""),
                        raw_data=proposal_data
                    )
                else:
                    proposal = proposal_data

                # Skip proposals that aren't active or are too old
                if not proposal.is_active and proposal.state != "active":
                    continue

                # Determine affected assets
                affected_assets = self._get_affected_assets(proposal)
                if not affected_assets:
                    continue  # Skip if we can't determine affected assets

                # Determine proposal type
                proposal_type = self._determine_proposal_type(proposal)

                # Generate signals based on proposal characteristics

                # 1. High-impact proposal signal
                if proposal_type in self.high_impact_proposal_types:
                    signal_type = "high_impact_proposal"
                    strength = self._calculate_signal_strength(proposal, signal_type)

                    signal = {
                        "id": str(uuid.uuid4()),
                        "source": proposal.platform,
                        "proposal_id": proposal.id,
                        "signal_type": signal_type,
                        "strength": strength,
                        "confidence": 0.7,
                        "assets_affected": affected_assets,
                        "timestamp": datetime.now().isoformat(),
                        "description": f"High-impact {proposal_type} proposal in {proposal.space_name}: {proposal.title}"
                    }
                    signals.append(signal)

                # 2. High participation signal
                if proposal.votes_count > 50:  # Arbitrary threshold
                    signal_type = "high_participation"
                    strength = self._calculate_signal_strength(proposal, signal_type)

                    signal = {
                        "id": str(uuid.uuid4()),
                        "source": proposal.platform,
                        "proposal_id": proposal.id,
                        "signal_type": signal_type,
                        "strength": strength,
                        "confidence": 0.6,
                        "assets_affected": affected_assets,
                        "timestamp": datetime.now().isoformat(),
                        "description": f"High participation in {proposal.space_name} proposal: {proposal.title}"
                    }
                    signals.append(signal)

                # 3. Contentious proposal signal
                if len(proposal.scores) >= 2 and min(proposal.scores) > 0:
                    ratio = min(proposal.scores) / max(proposal.scores)
                    if ratio > 0.3:  # Close vote
                        signal_type = "contentious_proposal"
                        strength = self._calculate_signal_strength(proposal, signal_type)

                        signal = {
                            "id": str(uuid.uuid4()),
                            "source": proposal.platform,
                            "proposal_id": proposal.id,
                            "signal_type": signal_type,
                            "strength": strength,
                            "confidence": 0.65,
                            "assets_affected": affected_assets,
                            "timestamp": datetime.now().isoformat(),
                            "description": f"Contentious proposal in {proposal.space_name}: {proposal.title}"
                        }
                        signals.append(signal)

                # 4. Whale activity signal
                # Check if there are any large voters (whales) participating
                if hasattr(proposal, 'votes') and proposal.votes:
                    whale_threshold = 0.1  # Whale controls 10% or more of voting power
                    total_votes = sum(vote.get('weight', 0) for vote in proposal.votes)

                    if total_votes > 0:
                        whales = [vote for vote in proposal.votes
                                 if vote.get('weight', 0) / total_votes >= whale_threshold]

                        if whales:
                            signal_type = "whale_activity"
                            strength = self._calculate_signal_strength(proposal, signal_type)

                            signal = {
                                "id": str(uuid.uuid4()),
                                "source": proposal.platform,
                                "proposal_id": proposal.id,
                                "signal_type": signal_type,
                                "strength": strength,
                                "confidence": 0.7,
                                "assets_affected": affected_assets,
                                "timestamp": datetime.now().isoformat(),
                                "description": f"Whale activity detected in {proposal.space_name} proposal: {proposal.title}"
                            }
                            signals.append(signal)

                # 5. Governance trend signal
                # This would ideally analyze trends across multiple proposals
                # For now, we'll use a simplified approach based on recent activity
                if proposal.created_at and (datetime.now() - proposal.created_at).total_seconds() < 86400 * 3:  # Last 3 days
                    # Check if this is part of a trend (multiple proposals in same space)
                    # This would be more sophisticated in a real implementation
                    signal_type = "governance_trend"
                    strength = self._calculate_signal_strength(proposal, signal_type)

                    signal = {
                        "id": str(uuid.uuid4()),
                        "source": proposal.platform,
                        "proposal_id": proposal.id,
                        "signal_type": signal_type,
                        "strength": strength,
                        "confidence": 0.6,
                        "assets_affected": affected_assets,
                        "timestamp": datetime.now().isoformat(),
                        "description": f"Increased governance activity in {proposal.space_name}"
                    }
                    signals.append(signal)

            except Exception as e:
                logger.error(f"Error generating signals for proposal: {str(e)}")

        logger.info(f"Generated {len(signals)} signals from {len(proposals)} proposals")
        return signals
