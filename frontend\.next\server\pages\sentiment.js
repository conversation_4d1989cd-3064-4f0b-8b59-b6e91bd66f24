(()=>{var e={};e.id=55,e.ids=[55,888,660],e.modules={8717:(e,s,t)=>{"use strict";t.r(s),t.d(s,{config:()=>f,default:()=>b,getServerSideProps:()=>y,getStaticPaths:()=>j,getStaticProps:()=>g,reportWebVitals:()=>N,routeModule:()=>k,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>v});var r={};t.r(r),t.d(r,{default:()=>u});var i=t(7093),n=t(5244),l=t(1323),a=t(2899),d=t.n(a),c=t(9413),o=t(997);t(6689);var m=t(1664),x=t.n(m),h=t(968),p=t.n(h);function u(){return(0,o.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[o.jsx(p(),{children:o.jsx("title",{children:"Market Sentiment Analysis"})}),o.jsx("header",{className:"flex justify-between items-center mb-8",children:(0,o.jsxs)("div",{children:[o.jsx("h1",{className:"text-3xl font-bold",children:"Market Sentiment Analysis"}),(0,o.jsxs)("div",{className:"flex mt-2 space-x-4",children:[o.jsx(x(),{href:"/",className:"text-blue-600 hover:text-blue-800",children:"Dashboard"}),o.jsx(x(),{href:"/governance",className:"text-blue-600 hover:text-blue-800",children:"Governance"}),o.jsx(x(),{href:"/sentiment",className:"text-blue-600 hover:text-blue-800 font-bold",children:"Sentiment"})]})]})}),o.jsx("p",{className:"mb-8",children:"Track market sentiment across social media, news, and forums to identify potential market movements before they happen. Our advanced sentiment analysis provides unique insights into market psychology."}),o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:["BTC","ETH","UNI","AAVE"].map((e,s)=>(0,o.jsxs)("div",{className:"p-5 shadow-md border border-gray-200 rounded-lg bg-white",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[o.jsx("h2",{className:"text-xl font-bold",children:e}),o.jsx("span",{className:`px-2 py-1 rounded-full text-sm ${s%3==0?"bg-green-100 text-green-800":s%3==1?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:s%3==0?"Bullish":s%3==1?"Neutral":"Bearish"})]}),o.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"BTC"===e?"Bitcoin":"ETH"===e?"Ethereum":"UNI"===e?"Uniswap":"Aave"}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,o.jsxs)("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Sentiment"}),(0,o.jsxs)("p",{className:"font-bold",children:[65+5*s,"%"]}),(0,o.jsxs)("p",{className:"text-xs text-green-500",children:["↑ ",5+s,"%"]})]}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"text-sm text-gray-500",children:"Volume"}),o.jsx("p",{className:"font-bold",children:(10-s)*1e3}),(0,o.jsxs)("p",{className:"text-xs text-green-500",children:["↑ ",10+2*s,"%"]})]})]})]},e))}),(0,o.jsxs)("div",{className:"mb-8 p-6 border border-gray-200 rounded-lg bg-white",children:[o.jsx("h2",{className:"text-xl font-bold mb-4",children:"Active Sentiment Signals"}),o.jsx("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full bg-white",children:[o.jsx("thead",{children:(0,o.jsxs)("tr",{children:[o.jsx("th",{className:"py-2 px-4 border-b",children:"Type"}),o.jsx("th",{className:"py-2 px-4 border-b",children:"Asset"}),o.jsx("th",{className:"py-2 px-4 border-b",children:"Direction"}),o.jsx("th",{className:"py-2 px-4 border-b",children:"Strength"}),o.jsx("th",{className:"py-2 px-4 border-b",children:"Time"})]})}),o.jsx("tbody",{children:[{id:1,type:"sentiment_shift",asset:"BTC",direction:"bullish",strength:.75},{id:2,type:"volume_spike",asset:"ETH",direction:"bullish",strength:.82},{id:3,type:"strong_consensus",asset:"UNI",direction:"bullish",strength:.68}].map(e=>(0,o.jsxs)("tr",{children:[o.jsx("td",{className:"py-2 px-4 border-b",children:(0,o.jsxs)("div",{className:"flex items-center",children:[o.jsx("span",{className:`inline-block w-3 h-3 rounded-full mr-2 ${"sentiment_shift"===e.type?"bg-blue-500":"volume_spike"===e.type?"bg-purple-500":"bg-teal-500"}`}),o.jsx("span",{children:"sentiment_shift"===e.type?"Sentiment Shift":"volume_spike"===e.type?"Volume Spike":"Strong Consensus"})]})}),o.jsx("td",{className:"py-2 px-4 border-b",children:e.asset}),o.jsx("td",{className:"py-2 px-4 border-b",children:o.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${"bullish"===e.direction?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.direction})}),o.jsx("td",{className:"py-2 px-4 border-b",children:o.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:o.jsx("div",{className:`h-2.5 rounded-full ${"bullish"===e.direction?"bg-green-500":"bg-red-500"}`,style:{width:`${100*e.strength}%`}})})}),o.jsx("td",{className:"py-2 px-4 border-b",children:new Date().toLocaleString()})]},e.id))})]})}),o.jsx("p",{className:"mt-4 text-sm text-gray-500",children:"Note: Showing sample data. Real sentiment signals will appear here once collected."})]}),(0,o.jsxs)("div",{className:"p-6 border border-gray-200 rounded-lg bg-white",children:[o.jsx("h2",{className:"text-xl font-bold mb-4",children:"How Our Sentiment Analysis Works"}),o.jsx("p",{className:"mb-4",children:"Our revolutionary trading platform uses advanced natural language processing and machine learning to analyze sentiment across multiple sources:"}),(0,o.jsxs)("ul",{className:"list-disc pl-5 mb-4 space-y-2",children:[(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"Social Media Analysis:"})," Real-time monitoring of Twitter, Reddit, and other platforms"]}),(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"News Sentiment:"})," Analysis of financial news and crypto publications"]}),(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"Forum Activity:"})," Tracking discussions on specialized crypto forums"]}),(0,o.jsxs)("li",{children:[o.jsx("strong",{children:"Volume Analysis:"})," Measuring changes in discussion volume as an indicator of interest"]})]}),o.jsx("p",{children:"By combining these sources and applying our proprietary algorithms, we can detect sentiment shifts and generate trading signals before they become apparent in price movements."})]})]})}let b=(0,l.l)(r,"default"),g=(0,l.l)(r,"getStaticProps"),j=(0,l.l)(r,"getStaticPaths"),y=(0,l.l)(r,"getServerSideProps"),f=(0,l.l)(r,"config"),N=(0,l.l)(r,"reportWebVitals"),v=(0,l.l)(r,"unstable_getStaticProps"),S=(0,l.l)(r,"unstable_getStaticPaths"),w=(0,l.l)(r,"unstable_getStaticParams"),P=(0,l.l)(r,"unstable_getServerProps"),_=(0,l.l)(r,"unstable_getServerSideProps"),k=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/sentiment",pathname:"/sentiment",bundlePath:"",filename:""},components:{App:c.default,Document:d()},userland:r})},9413:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(997);t(108);let i=function({Component:e,pageProps:s}){return r.jsx(e,{...s})}},108:()=>{},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},5315:e=>{"use strict";e.exports=require("path")}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[899,559],()=>t(8717));module.exports=r})();