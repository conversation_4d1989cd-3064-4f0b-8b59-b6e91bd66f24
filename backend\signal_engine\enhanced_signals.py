"""
Enhanced Signal Generator

This module extends the signal generator with additional data sources and ML capabilities.
"""

import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

class EnhancedSignalGenerator:
    """Extends the signal generator with additional data sources and ML capabilities."""

    def __init__(self, base_signal_generator):
        """
        Initialize the enhanced signal generator.

        Args:
            base_signal_generator: Base signal generator to extend
        """
        self.base_generator = base_signal_generator
        self.data_sources = {}
        self.initialize_data_sources()
        logger.info("Enhanced signal generator initialized")

    def initialize_data_sources(self):
        """Initialize additional data sources."""
        # Initialize economic indicators fetcher
        try:
            from backend.data_aggregator.economic_indicators_mini import EconomicIndicatorsFetcher
            self.data_sources["economic"] = EconomicIndicatorsFetcher({})
            logger.info("Economic indicators fetcher initialized")
        except ImportError:
            logger.warning("Economic indicators fetcher not available")

        # Initialize Fear & Greed Index fetcher
        try:
            from backend.data_aggregator.fear_greed_index import FearGreedIndexFetcher
            self.data_sources["fear_greed"] = FearGreedIndexFetcher()
            logger.info("Fear & Greed Index fetcher initialized")
        except ImportError:
            logger.warning("Fear & Greed Index fetcher not available")

        # Initialize on-chain data fetcher if available
        try:
            from backend.data_aggregator.onchain_data_fetcher import OnChainDataFetcher
            self.data_sources["onchain"] = OnChainDataFetcher({})
            logger.info("On-chain data fetcher initialized")
        except ImportError:
            logger.warning("On-chain data fetcher not available")

        # Initialize derivatives data fetcher if available
        try:
            from backend.data_aggregator.futures_options_fetcher import FuturesOptionsFetcher
            self.data_sources["derivatives"] = FuturesOptionsFetcher({})
            logger.info("Derivatives data fetcher initialized")
        except ImportError:
            logger.warning("Derivatives data fetcher not available")

    async def fetch_additional_data(self) -> Dict[str, Any]:
        """
        Fetch data from additional sources.

        Returns:
            Dictionary with data from additional sources
        """
        additional_data = {}

        # Use mock data for most sources
        logger.info("Using mock data for most sources")

        # Add mock economic indicators
        additional_data["economic_indicators"] = [
            {
                "indicator": "inflation",
                "value": 2.4,
                "date": "2025-03-01",
                "previous": 2.6,
                "source": "mock"
            },
            {
                "indicator": "interest_rate",
                "value": 4.25,
                "date": "2025-03-15",
                "previous": 4.5,
                "source": "mock"
            }
        ]
        logger.info(f"Using {len(additional_data['economic_indicators'])} mock economic indicators")

        # Fetch live Fear & Greed Index
        if "fear_greed" in self.data_sources:
            try:
                index = await self.data_sources["fear_greed"].fetch_current_index()
                if index:
                    additional_data["fear_greed_index"] = index
                    logger.info(f"Using live Fear & Greed Index: {index['value']} ({index['value_classification']})")
                else:
                    # Fallback to mock data if live data fetch fails
                    additional_data["fear_greed_index"] = {
                        "value": 47,
                        "value_classification": "Neutral",
                        "timestamp": "1745280000",
                        "source": "mock (fallback)"
                    }
                    logger.info(f"Using fallback mock Fear & Greed Index: {additional_data['fear_greed_index']['value']} ({additional_data['fear_greed_index']['value_classification']})")
            except Exception as e:
                logger.error(f"Error fetching Fear & Greed Index: {str(e)}")
                # Fallback to mock data
                additional_data["fear_greed_index"] = {
                    "value": 47,
                    "value_classification": "Neutral",
                    "timestamp": "1745280000",
                    "source": "mock (fallback)"
                }
                logger.info(f"Using fallback mock Fear & Greed Index: {additional_data['fear_greed_index']['value']} ({additional_data['fear_greed_index']['value_classification']})")

        # Add mock on-chain metrics
        additional_data["onchain_metrics"] = [
            {
                "asset_id": "bitcoin",
                "metric_type": "active_addresses",
                "value": 1234567,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "mock"
            },
            {
                "asset_id": "ethereum",
                "metric_type": "transaction_count",
                "value": 987654,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "mock"
            }
        ]
        logger.info(f"Using {len(additional_data['onchain_metrics'])} mock on-chain metrics")

        # Add mock derivatives data
        additional_data["derivatives_data"] = [
            {
                "asset_symbol": "BTC",
                "contract_type": "perpetual",
                "exchange": "binance",
                "funding_rate": 0.0001,
                "open_interest": 500000000,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "mock"
            },
            {
                "asset_symbol": "ETH",
                "contract_type": "perpetual",
                "exchange": "binance",
                "funding_rate": -0.0002,
                "open_interest": 200000000,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "mock"
            }
        ]
        logger.info(f"Using {len(additional_data['derivatives_data'])} mock derivatives data points")

        return additional_data

    def analyze_additional_data(self, additional_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze data from additional sources.

        Args:
            additional_data: Dictionary with data from additional sources

        Returns:
            Dictionary with analysis results
        """
        analysis = {}

        # Analyze economic indicators
        if "economic_indicators" in additional_data:
            indicators = additional_data["economic_indicators"]

            # Simple analysis based on indicator trends
            for indicator in indicators:
                if indicator["indicator"] == "inflation":
                    # Decreasing inflation is generally positive for markets
                    if indicator["value"] < indicator["previous"]:
                        analysis["inflation_trend"] = "positive"
                    else:
                        analysis["inflation_trend"] = "negative"

                elif indicator["indicator"] == "interest_rate":
                    # Decreasing interest rates are generally positive for markets
                    if indicator["value"] < indicator["previous"]:
                        analysis["interest_rate_trend"] = "positive"
                    else:
                        analysis["interest_rate_trend"] = "negative"

                elif indicator["indicator"] == "unemployment":
                    # Decreasing unemployment is generally positive for markets
                    if indicator["value"] < indicator["previous"]:
                        analysis["unemployment_trend"] = "positive"
                    else:
                        analysis["unemployment_trend"] = "negative"

        # Analyze Fear & Greed Index
        if "fear_greed_index" in additional_data:
            index = additional_data["fear_greed_index"]

            # Use the Fear & Greed Index fetcher to analyze sentiment
            if "fear_greed" in self.data_sources:
                sentiment = self.data_sources["fear_greed"].analyze_sentiment(index)
                analysis["market_sentiment"] = sentiment

        # Analyze derivatives data
        if "derivatives_data" in additional_data:
            derivatives_data = additional_data["derivatives_data"]

            # Create a simple mock sentiment analysis
            derivatives_sentiment = {
                "BTC": {
                    "overall_sentiment": "bullish",
                    "confidence": 0.7,
                    "funding_rate_sentiment": "neutral",
                    "open_interest_sentiment": "bullish"
                },
                "ETH": {
                    "overall_sentiment": "bearish",
                    "confidence": 0.6,
                    "funding_rate_sentiment": "bearish",
                    "open_interest_sentiment": "neutral"
                }
            }
            analysis["derivatives_sentiment"] = derivatives_sentiment

        return analysis

    def generate_enhanced_signals(self, base_signals: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate enhanced trading signals.

        Args:
            base_signals: List of base signals
            analysis: Dictionary with analysis results

        Returns:
            List of enhanced signals
        """
        enhanced_signals = []

        for signal in base_signals:
            # Create a copy of the base signal
            enhanced_signal = signal.copy()

            # Add additional context from analysis
            enhanced_signal["additional_context"] = {}

            # Add economic context if available
            if "inflation_trend" in analysis:
                enhanced_signal["additional_context"]["inflation_trend"] = analysis["inflation_trend"]

            if "interest_rate_trend" in analysis:
                enhanced_signal["additional_context"]["interest_rate_trend"] = analysis["interest_rate_trend"]

            if "unemployment_trend" in analysis:
                enhanced_signal["additional_context"]["unemployment_trend"] = analysis["unemployment_trend"]

            # Add market sentiment if available
            if "market_sentiment" in analysis:
                enhanced_signal["additional_context"]["market_sentiment"] = analysis["market_sentiment"]["sentiment"]
                enhanced_signal["additional_context"]["sentiment_score"] = analysis["market_sentiment"]["score"]

            # Add derivatives sentiment if available
            if "derivatives_sentiment" in analysis and enhanced_signal["asset"] in analysis["derivatives_sentiment"]:
                asset_sentiment = analysis["derivatives_sentiment"][enhanced_signal["asset"]]
                enhanced_signal["additional_context"]["derivatives_sentiment"] = asset_sentiment["overall_sentiment"]
                enhanced_signal["additional_context"]["derivatives_confidence"] = asset_sentiment["confidence"]

            # Adjust signal confidence based on additional context
            confidence_factors = []

            # Base confidence from original signal
            confidence_factors.append(enhanced_signal.get("confidence", 0.5))

            # Add sentiment confidence if available
            if "market_sentiment" in analysis:
                confidence_factors.append(analysis["market_sentiment"]["confidence"])

            # Add derivatives confidence if available
            if "derivatives_sentiment" in analysis and enhanced_signal["asset"] in analysis["derivatives_sentiment"]:
                confidence_factors.append(analysis["derivatives_sentiment"][enhanced_signal["asset"]]["confidence"])

            # Calculate average confidence
            if confidence_factors:
                enhanced_signal["confidence"] = sum(confidence_factors) / len(confidence_factors)

            # Add timestamp
            enhanced_signal["timestamp"] = datetime.now(timezone.utc).isoformat()

            enhanced_signals.append(enhanced_signal)

        return enhanced_signals

    async def generate_signals(self, assets: List[str]) -> List[Dict[str, Any]]:
        """
        Generate trading signals for specified assets.

        Args:
            assets: List of asset symbols

        Returns:
            List of trading signals
        """
        # Generate base signals
        base_signals = await self.base_generator.generate_crypto_signals()
        logger.info(f"Generated {len(base_signals)} base signals")

        # Fetch additional data
        additional_data = await self.fetch_additional_data()

        # Analyze additional data
        analysis = self.analyze_additional_data(additional_data)

        # Generate enhanced signals
        enhanced_signals = self.generate_enhanced_signals(base_signals, analysis)
        logger.info(f"Generated {len(enhanced_signals)} enhanced signals")

        return enhanced_signals

async def main():
    """Main function for testing the enhanced signal generator."""
    # Import the mock signal generator
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
    from backend.signal_engine.mock_signal_generator import MockSignalGenerator

    # Initialize mock signal generator
    base_generator = MockSignalGenerator()

    # Initialize enhanced signal generator
    enhanced_generator = EnhancedSignalGenerator(base_generator)

    # Generate signals for Bitcoin and Ethereum
    signals = await enhanced_generator.generate_signals(["BTC", "ETH"])

    # Print results
    print(f"Generated {len(signals)} enhanced signals")

    if signals:
        print("\nSample Signal:")
        for key, value in signals[0].items():
            if key != "additional_context":
                print(f"{key}: {value}")

        print("\nAdditional Context:")
        for key, value in signals[0].get("additional_context", {}).items():
            print(f"{key}: {value}")

if __name__ == "__main__":
    asyncio.run(main())
