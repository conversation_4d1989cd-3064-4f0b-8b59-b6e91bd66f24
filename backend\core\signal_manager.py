"""
Signal Manager for MCP Trading Platform

This module consolidates all signal generation functionality to eliminate
duplication and provide a unified interface for trading signals.
"""

import logging
import async<PERSON>
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

# Import signal generators
from signal_engine.signal_generator import SignalGenerator, Signal, SignalType, TimeFrame, AssetType
from signal_engine.enhanced_signals import EnhancedSignalGenerator
from signal_engine.multi_timeframe_analysis import MultiTimeframeAnalyzer
from signal_generator.onchain_signals import OnChainSignalGenerator
from signal_generator.advanced_signals import AdvancedSignalGenerator

logger = logging.getLogger(__name__)

class SignalCategory(str, Enum):
    """Categories of trading signals."""
    TECHNICAL = "technical"
    ONCHAIN = "onchain"
    SENTIMENT = "sentiment"
    MULTI_TIMEFRAME = "multi_timeframe"
    CONSENSUS = "consensus"
    ENHANCED = "enhanced"

@dataclass
class SignalGenerationResult:
    """Result of signal generation."""
    category: SignalCategory
    signals: List[Signal]
    timestamp: datetime
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = None

class SignalManager:
    """Centralized signal manager for the trading platform."""
    
    def __init__(self, settings):
        self.settings = settings
        self.generators: Dict[SignalCategory, Any] = {}
        self.signal_cache: Dict[str, List[Signal]] = {}
        self.last_generation_times: Dict[SignalCategory, datetime] = {}
        
    async def initialize(self):
        """Initialize all signal generators."""
        logger.info("Initializing signal manager...")
        
        try:
            # Initialize basic signal generator
            self.generators[SignalCategory.TECHNICAL] = SignalGenerator()
            
            # Initialize enhanced signal generator
            self.generators[SignalCategory.ENHANCED] = EnhancedSignalGenerator()
            
            # Initialize multi-timeframe analyzer
            self.generators[SignalCategory.MULTI_TIMEFRAME] = MultiTimeframeAnalyzer()
            
            # Initialize on-chain signal generator
            self.generators[SignalCategory.ONCHAIN] = OnChainSignalGenerator()
            
            # Initialize advanced signal generator
            self.generators[SignalCategory.CONSENSUS] = AdvancedSignalGenerator()
            
            logger.info(f"Initialized {len(self.generators)} signal generators")
            
        except Exception as e:
            logger.error(f"Error initializing signal generators: {e}")
            raise
    
    async def cleanup(self):
        """Clean up signal manager resources."""
        logger.info("Cleaning up signal manager...")
        self.signal_cache.clear()
        self.generators.clear()
    
    async def generate_signals(
        self, 
        symbols: List[str], 
        categories: Optional[List[SignalCategory]] = None
    ) -> Dict[SignalCategory, SignalGenerationResult]:
        """Generate signals for specified symbols and categories."""
        
        if categories is None:
            categories = list(SignalCategory)
        
        results = {}
        
        # Generate signals for each category
        for category in categories:
            if category not in self.generators:
                logger.warning(f"No generator available for category: {category}")
                continue
            
            try:
                result = await self._generate_category_signals(category, symbols)
                results[category] = result
                
                # Cache successful results
                if result.success and result.signals:
                    cache_key = f"{category}_{datetime.now().strftime('%Y%m%d_%H%M')}"
                    self.signal_cache[cache_key] = result.signals
                    self.last_generation_times[category] = datetime.now()
                
            except Exception as e:
                logger.error(f"Error generating {category} signals: {e}")
                results[category] = SignalGenerationResult(
                    category=category,
                    signals=[],
                    timestamp=datetime.now(),
                    success=False,
                    error=str(e)
                )
        
        return results
    
    async def _generate_category_signals(
        self, 
        category: SignalCategory, 
        symbols: List[str]
    ) -> SignalGenerationResult:
        """Generate signals for a specific category."""
        
        generator = self.generators[category]
        signals = []
        metadata = {}
        
        try:
            if category == SignalCategory.TECHNICAL:
                # Generate basic technical signals
                signals = await generator.generate_crypto_signals()
                
            elif category == SignalCategory.ENHANCED:
                # Generate enhanced signals with additional data
                signals = await generator.generate_signals(symbols)
                
            elif category == SignalCategory.MULTI_TIMEFRAME:
                # Generate multi-timeframe analysis
                for symbol in symbols:
                    analysis = await generator.analyze_multiple_timeframes(symbol)
                    # Convert analysis to signals
                    symbol_signals = self._analysis_to_signals(symbol, analysis)
                    signals.extend(symbol_signals)
                
            elif category == SignalCategory.ONCHAIN:
                # Generate on-chain enhanced signals
                generator.load_onchain_insights()
                generator.load_technical_signals()
                generator.generate_combined_signals()
                ui_signals = generator.get_signals_for_ui()
                
                # Convert to Signal objects
                signals = self._convert_onchain_signals(ui_signals)
                
            elif category == SignalCategory.CONSENSUS:
                # Generate consensus signals
                signals = await generator.generate_advanced_signals(symbols)
            
            return SignalGenerationResult(
                category=category,
                signals=signals,
                timestamp=datetime.now(),
                success=True,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"Error in {category} signal generation: {e}")
            return SignalGenerationResult(
                category=category,
                signals=[],
                timestamp=datetime.now(),
                success=False,
                error=str(e)
            )
    
    def _analysis_to_signals(self, symbol: str, analysis: Dict[str, Any]) -> List[Signal]:
        """Convert multi-timeframe analysis to Signal objects."""
        signals = []
        
        for timeframe, data in analysis.get('timeframe_signals', {}).items():
            signal = Signal(
                id=f"mtf_{symbol}_{timeframe}_{datetime.now().timestamp()}",
                asset_id=symbol.lower(),
                asset_symbol=symbol,
                asset_name=symbol,
                asset_type=AssetType.CRYPTO,
                signal_type=SignalType(data.get('direction', 'HOLD').upper()),
                time_frame=TimeFrame.HOUR_1,  # Map timeframe appropriately
                confidence=data.get('confidence', 0.5),
                price=data.get('current_price', 0.0),
                timestamp=datetime.now(),
                expiration=datetime.now() + timedelta(hours=1),
                indicators=data.get('indicators', {}),
                context_ids=[]
            )
            signals.append(signal)
        
        return signals
    
    def _convert_onchain_signals(self, ui_signals: Dict[str, Any]) -> List[Signal]:
        """Convert on-chain UI signals to Signal objects."""
        signals = []
        
        for coin, timeframes in ui_signals.items():
            for timeframe, signal_data in timeframes.items():
                signal = Signal(
                    id=f"onchain_{coin}_{timeframe}_{datetime.now().timestamp()}",
                    asset_id=coin.lower(),
                    asset_symbol=coin,
                    asset_name=coin,
                    asset_type=AssetType.CRYPTO,
                    signal_type=SignalType(signal_data.get('direction', 'HOLD').upper()),
                    time_frame=TimeFrame.HOUR_1,
                    confidence=signal_data.get('confidence', 0.5),
                    price=signal_data.get('current_price', 0.0),
                    timestamp=datetime.now(),
                    expiration=datetime.now() + timedelta(hours=1),
                    indicators=signal_data.get('indicators', {}),
                    sentiment=signal_data.get('sentiment_score'),
                    context_ids=[]
                )
                signals.append(signal)
        
        return signals
    
    async def get_latest_signals(
        self, 
        category: Optional[SignalCategory] = None,
        symbols: Optional[List[str]] = None,
        max_age_minutes: int = 30
    ) -> List[Signal]:
        """Get latest signals from cache or generate new ones."""
        
        cutoff_time = datetime.now() - timedelta(minutes=max_age_minutes)
        
        # If specific category requested
        if category:
            # Check if we have recent signals
            if (category in self.last_generation_times and 
                self.last_generation_times[category] > cutoff_time):
                
                # Return cached signals
                cache_keys = [k for k in self.signal_cache.keys() if k.startswith(str(category))]
                if cache_keys:
                    latest_key = max(cache_keys)
                    cached_signals = self.signal_cache[latest_key]
                    
                    # Filter by symbols if specified
                    if symbols:
                        cached_signals = [
                            s for s in cached_signals 
                            if s.asset_symbol.upper() in [sym.upper() for sym in symbols]
                        ]
                    
                    return cached_signals
            
            # Generate new signals
            symbols = symbols or self.settings.supported_cryptocurrencies
            results = await self.generate_signals(symbols, [category])
            return results.get(category, SignalGenerationResult(category, [], datetime.now(), False)).signals
        
        # Return all recent signals
        all_signals = []
        for cache_key, signals in self.signal_cache.items():
            # Check if signals are recent enough
            signal_time = max(s.timestamp for s in signals) if signals else datetime.min
            if signal_time > cutoff_time:
                all_signals.extend(signals)
        
        # Filter by symbols if specified
        if symbols:
            all_signals = [
                s for s in all_signals 
                if s.asset_symbol.upper() in [sym.upper() for sym in symbols]
            ]
        
        return all_signals
    
    async def get_consensus_signals(self, symbols: List[str]) -> List[Signal]:
        """Generate consensus signals by combining multiple categories."""
        
        # Generate signals from multiple categories
        categories = [
            SignalCategory.TECHNICAL,
            SignalCategory.ONCHAIN,
            SignalCategory.MULTI_TIMEFRAME
        ]
        
        results = await self.generate_signals(symbols, categories)
        
        # Combine and weight signals to create consensus
        consensus_signals = []
        
        for symbol in symbols:
            symbol_signals = []
            
            # Collect all signals for this symbol
            for category, result in results.items():
                if result.success:
                    symbol_signals.extend([
                        s for s in result.signals 
                        if s.asset_symbol.upper() == symbol.upper()
                    ])
            
            if symbol_signals:
                # Create consensus signal
                consensus_signal = self._create_consensus_signal(symbol, symbol_signals)
                consensus_signals.append(consensus_signal)
        
        return consensus_signals
    
    def _create_consensus_signal(self, symbol: str, signals: List[Signal]) -> Signal:
        """Create a consensus signal from multiple signals."""
        
        # Weight signals by confidence and recency
        buy_weight = 0
        sell_weight = 0
        hold_weight = 0
        total_confidence = 0
        
        for signal in signals:
            weight = signal.confidence
            total_confidence += weight
            
            if signal.signal_type == SignalType.BUY:
                buy_weight += weight
            elif signal.signal_type == SignalType.SELL:
                sell_weight += weight
            else:
                hold_weight += weight
        
        # Determine consensus signal type
        if buy_weight > sell_weight and buy_weight > hold_weight:
            signal_type = SignalType.BUY
            confidence = buy_weight / total_confidence if total_confidence > 0 else 0.5
        elif sell_weight > buy_weight and sell_weight > hold_weight:
            signal_type = SignalType.SELL
            confidence = sell_weight / total_confidence if total_confidence > 0 else 0.5
        else:
            signal_type = SignalType.HOLD
            confidence = hold_weight / total_confidence if total_confidence > 0 else 0.5
        
        # Get latest price from signals
        latest_price = max(signals, key=lambda s: s.timestamp).price if signals else 0.0
        
        return Signal(
            id=f"consensus_{symbol}_{datetime.now().timestamp()}",
            asset_id=symbol.lower(),
            asset_symbol=symbol,
            asset_name=symbol,
            asset_type=AssetType.CRYPTO,
            signal_type=signal_type,
            time_frame=TimeFrame.HOUR_1,
            confidence=confidence,
            price=latest_price,
            timestamp=datetime.now(),
            expiration=datetime.now() + timedelta(hours=1),
            indicators={
                "consensus_sources": len(signals),
                "buy_weight": buy_weight,
                "sell_weight": sell_weight,
                "hold_weight": hold_weight
            },
            context_ids=[]
        )
